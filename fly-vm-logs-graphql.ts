/**
 * Fly.io Machine Logs Function - GraphQL Implementation using VM.recentLogs
 *
 * Based on GraphQL schema discovery showing VM.recentLogs field:
 * - VM.recentLogs(limit: Int = 10, range: Int = 300): [LogEntry!]!
 * - LogEntry { id, instanceId, level, message, region, timestamp }
 * 
 * CLI command equivalent: fly logs -a appName -i machineId
 * Example: fly logs -a 9b1deb4d-3b7d-4bad-9bdd-2b0d7b3dcb6d -i e2860112ce1258
 *
 * This uses GraphQL VM.recentLogs instead of the REST API.
 */

interface LogEntry {
  id: string;
  instanceId: string;
  level: string;
  message: string;
  region: string;
  timestamp: string;
}

interface MachineLogsResult {
  logs: LogEntry[];
  success: boolean;
  error?: string;
}

/**
 * Get logs for a specific machine in an app using GraphQL VM.recentLogs
 * Add this method to your existing service class (the one with this.flyAxios)
 * 
 * This uses the GraphQL VM.recentLogs field discovered in the schema
 * 
 * @param appName - The name of the Fly.io app
 * @param machineId - The machine/instance ID 
 * @param limit - Maximum number of log entries to return (default: 100)
 * @param range - Max age of log entries in seconds (default: 3600 = 1 hour)
 * @returns Promise with logs data
 */
async function getMachineLogs(
  this: any, // For the flyAxios context
  appName: string,
  machineId: string,
  limit: number = 100,
  range: number = 3600
): Promise<MachineLogsResult> {
  try {
    console.log(`Getting logs for machine: ${machineId} in app: ${appName}`);

    // GraphQL query using the correct schema path: app -> vms -> nodes -> recentLogs
    const query = `
      query GetVMLogs($appName: String!, $limit: Int!, $range: Int!) {
        app(name: $appName) {
          id
          name
          vms {
            nodes {
              id
              idShort
              region
              status
              desiredStatus
              privateIP
              recentLogs(limit: $limit, range: $range) {
                id
                instanceId
                level
                message
                region
                timestamp
              }
            }
          }
        }
      }
    `;

    const variables = {
      appName,
      limit,
      range,
    };

    // Make the GraphQL request
    const response = await this.flyAxios.graphql.post('', {
      query,
      variables,
    });

    if (response.data.errors?.length) {
      throw new Error(`GraphQL error: ${JSON.stringify(response.data.errors)}`);
    }

    const app = response.data.data?.app;
    if (!app) {
      throw new Error(`App '${appName}' not found`);
    }

    console.log(`Found ${app.vms.nodes.length} VMs in app`);

    // Find the specific VM
    const targetVM = app.vms.nodes.find(
      (vm: any) =>
        vm.id === machineId ||
        vm.idShort === machineId ||
        vm.id.startsWith(machineId) ||
        vm.idShort.startsWith(machineId)
    );

    if (!targetVM) {
      const availableVMs = app.vms.nodes.map((vm: any) => `${vm.id} (short: ${vm.idShort})`).join(', ');
      throw new Error(`VM '${machineId}' not found in app '${appName}'. Available VMs: ${availableVMs}`);
    }

    console.log(`Found target VM: ${targetVM.id} (short: ${targetVM.idShort})`);

    // Check if the VM has recentLogs field
    if (!targetVM.recentLogs) {
      throw new Error(`VM '${machineId}' found but recentLogs field is not available. VM status: ${targetVM.status}`);
    }

    // Extract logs from the target VM
    const logs: LogEntry[] = targetVM.recentLogs.map((log: any) => ({
      id: log.id,
      instanceId: log.instanceId,
      level: log.level,
      message: log.message,
      region: log.region,
      timestamp: log.timestamp,
    }));

    // Sort by timestamp (newest first)
    logs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    console.log(`✅ Retrieved ${logs.length} log entries for VM ${machineId} via GraphQL app.vms.nodes.recentLogs`);
    
    return {
      logs,
      success: true,
    };

  } catch (error: any) {
    console.error('❌ Failed to get machine logs:', error);
    
    return {
      logs: [],
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Alternative approach: Get logs from all VMs and filter by VM ID
 * This might work better if the VM ID matching is not working as expected
 */
async function getAllVMLogsFiltered(
  this: any,
  appName: string,
  machineId: string,
  limit: number = 50,
  range: number = 3600
): Promise<MachineLogsResult> {
  try {
    console.log(`Getting all VM logs for app: ${appName}, filtering by VM: ${machineId}`);

    const query = `
      query GetAllVMLogs($appName: String!, $limit: Int!, $range: Int!) {
        app(name: $appName) {
          id
          name
          vms {
            nodes {
              id
              idShort
              region
              status
              desiredStatus
              recentLogs(limit: $limit, range: $range) {
                id
                instanceId
                level
                message
                region
                timestamp
              }
            }
          }
        }
      }
    `;

    const variables = {
      appName,
      limit,
      range,
    };

    const response = await this.flyAxios.graphql.post('', {
      query,
      variables,
    });

    if (response.data.errors?.length) {
      throw new Error(`GraphQL error: ${JSON.stringify(response.data.errors)}`);
    }

    const app = response.data.data?.app;
    if (!app) {
      throw new Error(`App '${appName}' not found`);
    }

    // Collect all logs from all VMs
    let allLogs: LogEntry[] = [];

    for (const vm of app.vms.nodes) {
      if (vm.recentLogs && Array.isArray(vm.recentLogs)) {
        const vmLogs = vm.recentLogs.map((log: any) => ({
          id: log.id,
          instanceId: log.instanceId,
          level: log.level,
          message: log.message,
          region: log.region,
          timestamp: log.timestamp,
          // Add VM info for debugging
          vmId: vm.id,
          vmIdShort: vm.idShort,
        }));
        allLogs.push(...vmLogs);
      }
    }

    // Filter logs by VM ID or instance ID
    const filteredLogs = allLogs.filter((log: any) =>
      log.instanceId === machineId ||
      log.vmId === machineId ||
      log.vmIdShort === machineId ||
      log.instanceId.startsWith(machineId) ||
      log.vmId.startsWith(machineId) ||
      log.vmIdShort.startsWith(machineId)
    );

    // Sort by timestamp (newest first)
    filteredLogs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    console.log(`✅ Retrieved ${filteredLogs.length} filtered log entries from ${allLogs.length} total logs`);
    
    return {
      logs: filteredLogs,
      success: true,
    };

  } catch (error: any) {
    console.error('❌ Failed to get filtered machine logs:', error);
    
    return {
      logs: [],
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// Export the functions and types
export { getMachineLogs, getAllMachineLogsFiltered };
export type { LogEntry, MachineLogsResult };

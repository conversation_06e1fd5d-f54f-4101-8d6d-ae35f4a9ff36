/**
 * Fly.io Machine Logs Function - GraphQL Implementation using VM.recentLogs
 *
 * Based on GraphQL schema discovery showing VM.recentLogs field:
 * - VM.recentLogs(limit: Int = 10, range: Int = 300): [LogEntry!]!
 * - LogEntry { id, instanceId, level, message, region, timestamp }
 * 
 * CLI command equivalent: fly logs -a appName -i machineId
 * Example: fly logs -a 9b1deb4d-3b7d-4bad-9bdd-2b0d7b3dcb6d -i e2860112ce1258
 *
 * This uses GraphQL VM.recentLogs instead of the REST API.
 */

interface LogEntry {
  id: string;
  instanceId: string;
  level: string;
  message: string;
  region: string;
  timestamp: string;
}

interface MachineLogsResult {
  logs: LogEntry[];
  success: boolean;
  error?: string;
}

/**
 * Get logs for a specific machine in an app using GraphQL VM.recentLogs
 * Add this method to your existing service class (the one with this.flyAxios)
 * 
 * This uses the GraphQL VM.recentLogs field discovered in the schema
 * 
 * @param appName - The name of the Fly.io app
 * @param machineId - The machine/instance ID 
 * @param limit - Maximum number of log entries to return (default: 100)
 * @param range - Max age of log entries in seconds (default: 3600 = 1 hour)
 * @returns Promise with logs data
 */
async function getMachineLogs(
  this: any, // For the flyAxios context
  appName: string,
  machineId: string,
  limit: number = 100,
  range: number = 3600
): Promise<MachineLogsResult> {
  try {
    console.log(`Getting logs for machine: ${machineId} in app: ${appName}`);

    // GraphQL query using VM.recentLogs field from the schema
    const query = `
      query GetVMLogs($appName: String!, $limit: Int!, $range: Int!) {
        app(name: $appName) {
          id
          name
          machines(active: true) {
            nodes {
              id
              instanceId
              region
              state
              recentLogs(limit: $limit, range: $range) {
                id
                instanceId
                level
                message
                region
                timestamp
              }
            }
          }
        }
      }
    `;

    const variables = {
      appName,
      limit,
      range,
    };

    // Make the GraphQL request
    const response = await this.flyAxios.graphql.post('', {
      query,
      variables,
    });

    if (response.data.errors?.length) {
      throw new Error(`GraphQL error: ${JSON.stringify(response.data.errors)}`);
    }

    const app = response.data.data?.app;
    if (!app) {
      throw new Error(`App '${appName}' not found`);
    }

    console.log(`Found ${app.machines.nodes.length} machines in app`);

    // Find the specific machine/VM
    const targetMachine = app.machines.nodes.find(
      (machine: any) => 
        machine.id === machineId || 
        machine.instanceId === machineId ||
        machine.id.startsWith(machineId) ||
        machine.instanceId.startsWith(machineId)
    );

    if (!targetMachine) {
      const availableMachines = app.machines.nodes.map((m: any) => `${m.id} (instanceId: ${m.instanceId})`).join(', ');
      throw new Error(`Machine '${machineId}' not found in app '${appName}'. Available machines: ${availableMachines}`);
    }

    console.log(`Found target machine: ${targetMachine.id} (instanceId: ${targetMachine.instanceId})`);

    // Check if the machine has recentLogs field
    if (!targetMachine.recentLogs) {
      throw new Error(`Machine '${machineId}' found but recentLogs field is not available. Machine state: ${targetMachine.state}`);
    }

    // Extract logs from the target machine
    const logs: LogEntry[] = targetMachine.recentLogs.map((log: any) => ({
      id: log.id,
      instanceId: log.instanceId,
      level: log.level,
      message: log.message,
      region: log.region,
      timestamp: log.timestamp,
    }));

    // Sort by timestamp (newest first)
    logs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    console.log(`✅ Retrieved ${logs.length} log entries for machine ${machineId} via GraphQL VM.recentLogs`);
    
    return {
      logs,
      success: true,
    };

  } catch (error: any) {
    console.error('❌ Failed to get machine logs:', error);
    
    return {
      logs: [],
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Alternative approach: Get logs from all machines and filter by instanceId
 * This might work better if the machine ID matching is not working as expected
 */
async function getAllMachineLogsFiltered(
  this: any,
  appName: string,
  machineId: string,
  limit: number = 50,
  range: number = 3600
): Promise<MachineLogsResult> {
  try {
    console.log(`Getting all machine logs for app: ${appName}, filtering by machine: ${machineId}`);

    const query = `
      query GetAllVMLogs($appName: String!, $limit: Int!, $range: Int!) {
        app(name: $appName) {
          id
          name
          machines(active: true) {
            nodes {
              id
              instanceId
              region
              state
              recentLogs(limit: $limit, range: $range) {
                id
                instanceId
                level
                message
                region
                timestamp
              }
            }
          }
        }
      }
    `;

    const variables = {
      appName,
      limit,
      range,
    };

    const response = await this.flyAxios.graphql.post('', {
      query,
      variables,
    });

    if (response.data.errors?.length) {
      throw new Error(`GraphQL error: ${JSON.stringify(response.data.errors)}`);
    }

    const app = response.data.data?.app;
    if (!app) {
      throw new Error(`App '${appName}' not found`);
    }

    // Collect all logs from all machines
    let allLogs: LogEntry[] = [];
    
    for (const machine of app.machines.nodes) {
      if (machine.recentLogs && Array.isArray(machine.recentLogs)) {
        const machineLogs = machine.recentLogs.map((log: any) => ({
          id: log.id,
          instanceId: log.instanceId,
          level: log.level,
          message: log.message,
          region: log.region,
          timestamp: log.timestamp,
          // Add machine info for debugging
          machineId: machine.id,
          machineInstanceId: machine.instanceId,
        }));
        allLogs.push(...machineLogs);
      }
    }

    // Filter logs by machine ID or instance ID
    const filteredLogs = allLogs.filter((log: any) => 
      log.instanceId === machineId ||
      log.machineId === machineId ||
      log.machineInstanceId === machineId ||
      log.instanceId.startsWith(machineId) ||
      log.machineId.startsWith(machineId) ||
      log.machineInstanceId.startsWith(machineId)
    );

    // Sort by timestamp (newest first)
    filteredLogs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    console.log(`✅ Retrieved ${filteredLogs.length} filtered log entries from ${allLogs.length} total logs`);
    
    return {
      logs: filteredLogs,
      success: true,
    };

  } catch (error: any) {
    console.error('❌ Failed to get filtered machine logs:', error);
    
    return {
      logs: [],
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// Export the functions and types
export { getMachineLogs, getAllMachineLogsFiltered };
export type { LogEntry, MachineLogsResult };

/**
 * Usage Example: How to integrate the machine logs function into your existing service
 * 
 * This shows how to add the getMachineLogs method to your existing Fly.io service class
 */

import { getMachineLogs, getAllAppLogs, LogEntry, MachineLogsResult } from './fly-machine-logs';

// Example of how to add this to your existing service class
class FlyService {
  private flyAxios: any; // Your existing flyAxios instance

  constructor(flyAxios: any) {
    this.flyAxios = flyAxios;
  }

  // Your existing method (for reference)
  async allocateSharedIPv4(appName: string): Promise<string> {
    try {
      console.log(`Allocating shared IPv4 for app: ${appName}`);

      const query = `
        mutation ($input: AllocateIPAddressInput!) {
          allocateIpAddress(input: $input) {
            ipAddress {
              address
            }
          }
        }
      `;

      const variables = {
        input: {
          appId: appName,
          type: 'shared_v4',
        },
      };

      const response = await this.flyAxios.graphql.post('', {
        query,
        variables,
      });

      if (response.data.errors?.length) {
        throw new Error(
          `GraphQL error: ${JSON.stringify(response.data.errors)}`,
        );
      }

      console.log(`✅ Shared IPv4 allocated`);
      return 'success';
    } catch (error) {
      console.error('❌ IPv4 allocation failed:', error);
      throw error;
    }
  }

  // NEW METHOD: Add this to your service class
  async getMachineLogs(
    appName: string,
    machineId: string,
    limit: number = 100,
    range: number = 3600
  ): Promise<MachineLogsResult> {
    try {
      console.log(`Getting logs for machine: ${machineId} in app: ${appName}`);

      const query = `
        query GetMachineLogs($appName: String!, $limit: Int!, $range: Int!) {
          app(name: $appName) {
            id
            name
            allocations(showCompleted: true) {
              id
              idShort
              region
              status
              desiredStatus
              recentLogs(limit: $limit, range: $range) {
                id
                instanceId
                level
                message
                region
                timestamp
              }
            }
          }
        }
      `;

      const variables = {
        appName,
        limit,
        range,
      };

      const response = await this.flyAxios.graphql.post('', {
        query,
        variables,
      });

      if (response.data.errors?.length) {
        throw new Error(
          `GraphQL error: ${JSON.stringify(response.data.errors)}`,
        );
      }

      const app = response.data.data?.app;
      if (!app) {
        throw new Error(`App '${appName}' not found`);
      }

      // Find the specific machine/allocation by ID (full or short)
      const targetAllocation = app.allocations.find(
        (allocation: any) => 
          allocation.id === machineId || 
          allocation.idShort === machineId ||
          allocation.id.startsWith(machineId)
      );

      if (!targetAllocation) {
        // If specific machine not found, collect all logs and filter by instanceId
        let allLogs: LogEntry[] = [];
        
        for (const allocation of app.allocations) {
          const logs = allocation.recentLogs
            .filter((log: any) => 
              !machineId || 
              log.instanceId === machineId || 
              log.instanceId.startsWith(machineId)
            )
            .map((log: any) => ({
              id: log.id,
              instanceId: log.instanceId,
              level: log.level,
              message: log.message,
              region: log.region,
              timestamp: log.timestamp,
            }));
          
          allLogs.push(...logs);
        }

        if (allLogs.length === 0) {
          throw new Error(`No logs found for machine '${machineId}' in app '${appName}'`);
        }

        // Sort by timestamp (newest first)
        allLogs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

        console.log(`✅ Retrieved ${allLogs.length} log entries for machine ${machineId}`);
        return {
          logs: allLogs,
          success: true,
        };
      }

      // Extract logs from the target allocation
      const logs: LogEntry[] = targetAllocation.recentLogs.map((log: any) => ({
        id: log.id,
        instanceId: log.instanceId,
        level: log.level,
        message: log.message,
        region: log.region,
        timestamp: log.timestamp,
      }));

      // Sort by timestamp (newest first)
      logs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

      console.log(`✅ Retrieved ${logs.length} log entries for machine ${machineId}`);
      
      return {
        logs,
        success: true,
      };

    } catch (error) {
      console.error('❌ Failed to get machine logs:', error);
      return {
        logs: [],
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  // OPTIONAL: Method to get all logs for an app
  async getAllAppLogs(
    appName: string,
    limit: number = 50,
    range: number = 3600
  ): Promise<MachineLogsResult> {
    // Implementation similar to above but without machine filtering
    // See the full implementation in fly-machine-logs.ts
    // ... (implementation details)
    return { logs: [], success: false, error: 'Not implemented in this example' };
  }
}

// Usage examples:
async function exampleUsage() {
  const flyService = new FlyService(/* your flyAxios instance */);

  try {
    // Example 1: Get logs for a specific machine
    const result = await flyService.getMachineLogs(
      '9b1deb4d-3b7d-4bad-9bdd-2b0d7b3dcb6d', // appName
      'e2860112ce1258', // machineId
      100, // limit (optional)
      3600 // range in seconds (optional)
    );

    if (result.success) {
      console.log(`Found ${result.logs.length} log entries:`);
      result.logs.forEach(log => {
        console.log(`[${log.timestamp}] ${log.level}: ${log.message}`);
      });
    } else {
      console.error('Failed to get logs:', result.error);
    }

    // Example 2: Get recent logs (last 10 minutes)
    const recentLogs = await flyService.getMachineLogs(
      'my-app-name',
      'my-machine-id',
      50,  // limit to 50 entries
      600  // last 10 minutes (600 seconds)
    );

    // Example 3: Get all logs for debugging
    const allLogs = await flyService.getAllAppLogs('my-app-name');

  } catch (error) {
    console.error('Error:', error);
  }
}

export { FlyService, exampleUsage };

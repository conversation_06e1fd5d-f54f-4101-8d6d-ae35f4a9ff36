/**
 * Fly.io Machine Logs Function
 *
 * Based on analysis of the fly.io CLI codebase (flyctl)
 * CLI command equivalent: fly logs -a appName -i machineId
 * Example: fly logs -a 9b1deb4d-3b7d-4bad-9bdd-2b0d7b3dcb6d -i e2860112ce1258
 *
 * The CLI uses a REST API endpoint, not GraphQL, for fetching logs.
 * Add this method to your existing service class that has this.flyAxios configured.
 */

interface LogEntry {
  id: string;
  instanceId: string;
  level: string;
  message: string;
  region: string;
  timestamp: string;
}

interface MachineLogsResult {
  logs: LogEntry[];
  success: boolean;
  error?: string;
}

/**
 * Get logs for a specific machine in an app
 * Add this method to your existing service class (the one with this.flyAxios)
 *
 * @param appName - The name of the Fly.io app
 * @param machineId - The machine/instance ID (can be full ID or short ID)
 * @param limit - Maximum number of log entries to return (default: 100)
 * @param range - Max age of log entries in seconds (default: 3600 = 1 hour)
 * @returns Promise with logs data
 */
  try {
    console.log(`Getting logs for machine: ${machineId} in app: ${appName}`);

    // Based on CLI analysis, try different REST API endpoints that might work
    const possibleEndpoints = [
      `/api/v1/apps/${appName}/logs?instance=${machineId}&limit=${limit}&range=${range}`,
      `/api/v1/apps/${appName}/logs?machine=${machineId}&limit=${limit}&range=${range}`,
      `/api/v1/apps/${appName}/machines/${machineId}/logs?limit=${limit}&range=${range}`,
      `/apps/${appName}/logs?instance=${machineId}&limit=${limit}&range=${range}`,
    ];

    for (const endpoint of possibleEndpoints) {
      try {
        console.log(`Trying endpoint: ${endpoint}`);
        const response = await this.flyAxios.get(endpoint);

        if (response.data && Array.isArray(response.data)) {
          const logs: LogEntry[] = response.data.map((log: any) => ({
            id: log.id || `${log.timestamp}-${Math.random()}`,
            instanceId: log.instance || log.instanceId || machineId,
            level: log.level || 'info',
            message: log.message || log.msg || '',
            region: log.region || '',
            timestamp: log.timestamp || log.ts || new Date().toISOString(),
          }));

          // Sort by timestamp (newest first)
          logs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

          console.log(`✅ Retrieved ${logs.length} log entries via ${endpoint}`);
          return {
            logs,
            success: true,
          };
        }
      } catch (endpointError) {
        console.log(`Endpoint ${endpoint} failed:`, endpointError.message);
        continue;
      }
    }

    // If REST API fails, try GraphQL to at least verify the machine exists
    const query = `
      query GetMachineInfo($appName: String!) {
        app(name: $appName) {
          id
          name
          machines(active: true) {
            nodes {
              id
              instanceId
              region
              state
            }
          }
        }
      }
    `;

    const response = await this.flyAxios.graphql.post('', {
      query,
      variables: { appName },
    });

    if (response.data.errors?.length) {
      throw new Error(`GraphQL error: ${JSON.stringify(response.data.errors)}`);
    }

    const app = response.data.data?.app;
    if (!app) {
      throw new Error(`App '${appName}' not found`);
    }

    const targetMachine = app.machines.nodes.find(
      (machine: any) =>
        machine.id === machineId ||
        machine.instanceId === machineId ||
        machine.id.startsWith(machineId) ||
        machine.instanceId.startsWith(machineId)
    );

    if (!targetMachine) {
      const availableMachines = app.machines.nodes.map((m: any) => `${m.id} (${m.instanceId})`).join(', ');
      throw new Error(`Machine '${machineId}' not found in app '${appName}'. Available machines: ${availableMachines}`);
    }

    console.log(`Machine ${machineId} found but logs API endpoints are not accessible. Machine info:`, targetMachine);
    return {
      logs: [],
      success: false,
      error: `Machine found but logs are not accessible via API. Try using the CLI: fly logs -a ${appName} -i ${machineId}`,
    };

  } catch (error) {
    console.error('❌ Failed to get machine logs:', error);
    return {
      logs: [],
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Fly.io Machine Logs Function
 * 
 * Based on analysis of the fly.io CLI codebase (flyctl)
 * CLI command equivalent: fly logs -a appName -i machineId
 * Example: fly logs -a 9b1deb4d-3b7d-4bad-9bdd-2b0d7b3dcb6d -i e2860112ce1258
 * 
 * This function uses the GraphQL API to fetch logs for a specific machine.
 * Add this method to your existing service class that has this.flyAxios configured.
 */

interface LogEntry {
  id: string;
  instanceId: string;
  level: string;
  message: string;
  region: string;
  timestamp: string;
}

interface MachineLogsResult {
  logs: LogEntry[];
  success: boolean;
  error?: string;
}

/**
 * Get logs for a specific machine in an app
 * 
 * @param appName - The name of the Fly.io app
 * @param machineId - The machine/instance ID (can be full ID or short ID)
 * @param limit - Maximum number of log entries to return (default: 100)
 * @param range - Max age of log entries in seconds (default: 3600 = 1 hour)
 * @returns Promise with logs data
 */
async getMachineLogs(
  appName: string,
  machineId: string,
  limit: number = 100,
  range: number = 3600
): Promise<MachineLogsResult> {
  try {
    console.log(`Getting logs for machine: ${machineId} in app: ${appName}`);

    // GraphQL query to get app allocations and their logs
    // Based on the schema analysis, we query allocations and their recentLogs
    const query = `
      query GetMachineLogs($appName: String!, $limit: Int!, $range: Int!) {
        app(name: $appName) {
          id
          name
          allocations(showCompleted: true) {
            id
            idShort
            region
            status
            desiredStatus
            recentLogs(limit: $limit, range: $range) {
              id
              instanceId
              level
              message
              region
              timestamp
            }
          }
        }
      }
    `;

    const variables = {
      appName,
      limit,
      range,
    };

    // Make the GraphQL request using your existing flyAxios setup
    const response = await this.flyAxios.graphql.post('', {
      query,
      variables,
    });

    if (response.data.errors?.length) {
      throw new Error(
        `GraphQL error: ${JSON.stringify(response.data.errors)}`,
      );
    }

    const app = response.data.data?.app;
    if (!app) {
      throw new Error(`App '${appName}' not found`);
    }

    // Find the specific machine/allocation by ID (full or short)
    const targetAllocation = app.allocations.find(
      (allocation: any) => 
        allocation.id === machineId || 
        allocation.idShort === machineId ||
        allocation.id.startsWith(machineId)
    );

    if (!targetAllocation) {
      // If specific machine not found, collect all logs and filter by instanceId
      let allLogs: LogEntry[] = [];
      
      for (const allocation of app.allocations) {
        const logs = allocation.recentLogs
          .filter((log: any) => 
            !machineId || 
            log.instanceId === machineId || 
            log.instanceId.startsWith(machineId)
          )
          .map((log: any) => ({
            id: log.id,
            instanceId: log.instanceId,
            level: log.level,
            message: log.message,
            region: log.region,
            timestamp: log.timestamp,
          }));
        
        allLogs.push(...logs);
      }

      if (allLogs.length === 0) {
        throw new Error(`No logs found for machine '${machineId}' in app '${appName}'`);
      }

      // Sort by timestamp (newest first)
      allLogs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

      console.log(`✅ Retrieved ${allLogs.length} log entries for machine ${machineId}`);
      return {
        logs: allLogs,
        success: true,
      };
    }

    // Extract logs from the target allocation
    const logs: LogEntry[] = targetAllocation.recentLogs.map((log: any) => ({
      id: log.id,
      instanceId: log.instanceId,
      level: log.level,
      message: log.message,
      region: log.region,
      timestamp: log.timestamp,
    }));

    // Sort by timestamp (newest first)
    logs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    console.log(`✅ Retrieved ${logs.length} log entries for machine ${machineId}`);
    
    return {
      logs,
      success: true,
    };

  } catch (error) {
    console.error('❌ Failed to get machine logs:', error);
    return {
      logs: [],
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Alternative method: Get all logs for an app (useful for debugging or when machine ID is unknown)
 * 
 * @param appName - The name of the Fly.io app
 * @param limit - Maximum number of log entries per allocation (default: 50)
 * @param range - Max age of log entries in seconds (default: 3600 = 1 hour)
 * @returns Promise with all logs data
 */
async getAllAppLogs(
  appName: string,
  limit: number = 50,
  range: number = 3600
): Promise<MachineLogsResult> {
  try {
    console.log(`Getting all logs for app: ${appName}`);

    const query = `
      query GetAllAppLogs($appName: String!, $limit: Int!, $range: Int!) {
        app(name: $appName) {
          id
          name
          allocations(showCompleted: true) {
            id
            idShort
            region
            status
            recentLogs(limit: $limit, range: $range) {
              id
              instanceId
              level
              message
              region
              timestamp
            }
          }
        }
      }
    `;

    const variables = {
      appName,
      limit,
      range,
    };

    const response = await this.flyAxios.graphql.post('', {
      query,
      variables,
    });

    if (response.data.errors?.length) {
      throw new Error(
        `GraphQL error: ${JSON.stringify(response.data.errors)}`,
      );
    }

    const app = response.data.data?.app;
    if (!app) {
      throw new Error(`App '${appName}' not found`);
    }

    // Collect all logs from all allocations
    let allLogs: LogEntry[] = [];
    
    for (const allocation of app.allocations) {
      const logs = allocation.recentLogs.map((log: any) => ({
        id: log.id,
        instanceId: log.instanceId,
        level: log.level,
        message: log.message,
        region: log.region,
        timestamp: log.timestamp,
        // Add allocation info for context
        allocationId: allocation.id,
        allocationIdShort: allocation.idShort,
        allocationStatus: allocation.status,
      }));
      
      allLogs.push(...logs);
    }

    // Sort by timestamp (newest first)
    allLogs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    console.log(`✅ Retrieved ${allLogs.length} total log entries from ${app.allocations.length} allocations`);
    
    return {
      logs: allLogs,
      success: true,
    };

  } catch (error) {
    console.error('❌ Failed to get app logs:', error);
    return {
      logs: [],
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// Export types and functions
export { getMachineLogs, getAllAppLogs };
export type { LogEntry, MachineLogsResult };

# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks
repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
    -   id: trailing-whitespace
    -   id: end-of-file-fixer
    -   id: check-yaml
    -   id: check-added-large-files

-   repo: https://github.com/tekwizely/pre-commit-golang
    rev: v1.0.0-rc.1
    hooks:
    -   id: go-mod-tidy

# NOTE: This pre-commit hook is ignored when running on Github Workflow
# because goalngci-lint github action is much more useful than the pre-commit action.
# The trick is to run github action only for "manual" hook stage
-   repo: https://github.com/golangci/golangci-lint
    rev: v1.54.2
    hooks:
    -   id: golangci-lint
        stages: [pre-commit]

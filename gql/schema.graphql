schema {
  query: Queries
  mutation: Mutations
}

type AccessToken implements Node {
  createdAt: ISO8601DateTime!
  id: ID!
  name: String!
  token: String!
  type: AccessTokenType!
}

"""
The connection type for AccessToken.
"""
type AccessTokenConnection {
  """
  A list of edges.
  """
  edges: [AccessTokenEdge]

  """
  A list of nodes.
  """
  nodes: [AccessToken]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

"""
An edge in a connection.
"""
type AccessTokenEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: AccessToken
}

enum AccessTokenType {
  """
  used for querying access tokens
  """
  all

  """
  token generated for and used by flyctl
  """
  flyctl

  """
  token for fly-metrics.net
  """
  grafana

  """
  personal access token generated in the UI
  """
  pat

  """
  used for Sentry
  """
  sentry

  """
  access token
  """
  token

  """
  token generated for our UI frontend
  """
  ui
}

"""
Autogenerated return type of AddCertificate.
"""
type AddCertificatePayload {
  app: App
  certificate: AppCertificate
  check: HostnameCheck
  errors: [String!]
}

"""
Autogenerated input type of AddDevrelCoupon
"""
input AddDevrelCouponInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  name: String!
}

"""
Autogenerated return type of AddDevrelCoupon.
"""
type AddDevrelCouponPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  success: Boolean
}

type AddOn implements Node {
  """
  The add-on plan
  """
  addOnPlan: AddOnPlan

  """
  The display name for an add-on plan
  """
  addOnPlanName: String

  """
  The add-on provider
  """
  addOnProvider: AddOnProvider

  """
  An app associated with this add-on
  """
  app: App

  """
  Apps associated with this add-on
  """
  apps(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): AppConnection
  createdAt: ISO8601DateTime!

  """
  Environment variables for the add-on
  """
  environment: JSON

  """
  Optional error message when `status` is `error`
  """
  errorMessage: String

  """
  DNS hostname for the add-on
  """
  hostname: String
  id: ID!

  """
  Add-on metadata
  """
  metadata: JSON

  """
  The service name according to the provider
  """
  name: String

  """
  Add-on options
  """
  options: JSON

  """
  Organization that owns this service
  """
  organization: Organization!

  """
  Password for the add-on
  """
  password: String

  """
  Region where the primary instance is deployed
  """
  primaryRegion: String

  """
  Private flycast IP address of the add-on
  """
  privateIp: String

  """
  Public URL for this service
  """
  publicUrl: String

  """
  Regions where replica instances are deployed
  """
  readRegions: [String!]

  """
  Single sign-on link to the add-on dashboard
  """
  ssoLink: String

  """
  Redis database statistics
  """
  stats: JSON

  """
  Status of the add-on
  """
  status: String
  updatedAt: ISO8601DateTime!
}

"""
The connection type for AddOn.
"""
type AddOnConnection {
  """
  A list of edges.
  """
  edges: [AddOnEdge]

  """
  A list of nodes.
  """
  nodes: [AddOn]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

"""
An edge in a connection.
"""
type AddOnEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: AddOn
}

type AddOnPlan implements Node {
  description: String
  displayName: String
  id: ID!
  maxCommandsPerSec: Int
  maxConcurrentConnections: Int
  maxDailyBandwidth: String
  maxDailyCommands: Int
  maxDataSize: String
  maxRequestSize: String
  name: String
  pricePerMonth: Int
}

"""
The connection type for AddOnPlan.
"""
type AddOnPlanConnection {
  """
  A list of edges.
  """
  edges: [AddOnPlanEdge]

  """
  A list of nodes.
  """
  nodes: [AddOnPlan]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

"""
An edge in a connection.
"""
type AddOnPlanEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: AddOnPlan
}

type AddOnProvider {
  asyncProvisioning: Boolean!
  autoProvision: Boolean!
  beta: Boolean!
  detectPlatform: Boolean!
  displayName: String
  excludedRegions: [Region!]
  id: ID!
  internal: Boolean!
  name: String
  nameSuffix: String
  provisioningInstructions: String
  regions: [Region!]
  resourceName: String!
  selectName: Boolean!
  selectRegion: Boolean!
  selectReplicaRegions: Boolean!
  tosAgreement: String
  tosUrl: String
}

enum AddOnType {
  """
  An Arcjet site
  """
  arcjet

  """
  An Enveloop project
  """
  enveloop

  """
  A MySQL database
  """
  fly_mysql

  """
  A Kubernetes cluster
  """
  kubernetes

  """
  An Upstash Redis database
  """
  redis

  """
  A Sentry project endpoint
  """
  sentry

  """
  A Supabase database
  """
  supabase

  """
  A Tigris Data bucket
  """
  tigris

  """
  An Upstash Kafka cluster
  """
  upstash_kafka

  """
  An Upstash Redis database
  """
  upstash_redis

  """
  An Upstash Vector cluster
  """
  upstash_vector

  """
  A Wafris firewall
  """
  wafris
}

"""
Autogenerated input type of AddStripePaymentMethod
"""
input AddStripePaymentMethodInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  stripePaymentMethodId: String
}

"""
Autogenerated return type of AddStripePaymentMethod.
"""
type AddStripePaymentMethodPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  prepaid: Boolean
  valid: Boolean
}

"""
Autogenerated input type of AddWireGuardPeer
"""
input AddWireGuardPeerInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  An ephemeral peer will be destroyed if not validated for too long
  """
  ephemeral: Boolean

  """
  Create peer on specific gateway
  """
  gwname: String

  """
  The name with which to refer to the peer
  """
  name: String!

  """
  Add via NATS transaction (deprecated - nats is always used)
  """
  nats: Boolean

  """
  Network ID to attach wireguard peer to
  """
  network: String

  """
  The node ID of the organization
  """
  organizationId: ID!

  """
  The 25519 public key for the peer
  """
  pubkey: String!

  """
  The region in which to deploy the peer
  """
  region: String
}

"""
Autogenerated return type of AddWireGuardPeer.
"""
type AddWireGuardPeerPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  endpointip: String!
  network: String
  peerip: String!
  pubkey: String!
}

"""
Autogenerated input type of AllocateEgressIPAddress
"""
input AllocateEgressIPAddressInput {
  """
  The ID of the app
  """
  appId: ID!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  ID of the machine
  """
  machineId: ID!
}

"""
Autogenerated return type of AllocateEgressIPAddress.
"""
type AllocateEgressIPAddressPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  v4: String!
  v6: String!
}

"""
Autogenerated input type of AllocateIPAddress
"""
input AllocateIPAddressInput {
  """
  The application to allocate the ip address for
  """
  appId: ID!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The target network name in the specified organization
  """
  network: String

  """
  The organization whose network should be used for private IP allocation
  """
  organizationId: ID

  """
  Desired IP region (defaults to global)
  """
  region: String

  """
  The name of the associated service
  """
  serviceName: String

  """
  The type of IP address to allocate (v4, v6, or private_v6)
  """
  type: IPAddressType!
}

"""
Autogenerated return type of AllocateIPAddress.
"""
type AllocateIPAddressPayload {
  app: App!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  ipAddress: IPAddress
}

type Allocation implements Node {
  attachedVolumes(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): VolumeConnection!
  canary: Boolean!
  checks(
    """
    Filter checks by name
    """
    name: String
  ): [CheckState!]!
  createdAt: ISO8601DateTime!
  criticalCheckCount: Int!

  """
  Desired status
  """
  desiredStatus: String!
  events: [AllocationEvent!]!
  failed: Boolean!
  healthy: Boolean!

  """
  Unique ID for this instance
  """
  id: ID!

  """
  Short unique ID for this instance
  """
  idShort: ID!

  """
  Indicates if this instance is from the latest job version
  """
  latestVersion: Boolean!
  passingCheckCount: Int!

  """
  Private IPv6 address for this instance
  """
  privateIP: String
  recentLogs(
    """
    Max number of entries to return
    """
    limit: Int = 10

    """
    Max age of log entries in seconds
    """
    range: Int = 300
  ): [LogEntry!]!

  """
  Region this allocation is running in
  """
  region: String!
  restarts: Int!

  """
  Current status
  """
  status: String!
  taskName: String!
  totalCheckCount: Int!
  transitioning: Boolean!
  updatedAt: ISO8601DateTime!

  """
  The configuration version of this instance
  """
  version: Int!
  warningCheckCount: Int!
}

type AllocationEvent {
  message: String!
  timestamp: ISO8601DateTime!
  type: String!
}

type App implements Node {
  addOns(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
    type: AddOnType
  ): AddOnConnection!
  allocation(id: String!): Allocation
  allocations(showCompleted: Boolean): [Allocation!]!
  appUrl: String
  autoscaling: AutoscalingConfig
  backupRegions: [Region!]!

  """
  [DEPRECATED] Builds of this application
  """
  builds(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): BuildConnection! @deprecated(reason: "Superseded by source_builds")

  """
  Find a certificate by hostname
  """
  certificate(hostname: String!): AppCertificate

  """
  Certificates for this app
  """
  certificates(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String
    filter: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int
    id: String

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): AppCertificateConnection!

  """
  Changes to this application
  """
  changes(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): AppChangeConnection!
  config: AppConfig!
  createdAt: ISO8601DateTime!
  currentLock: AppLock
  currentPlacement: [RegionPlacement!]!

  """
  The latest release of this application
  """
  currentRelease: Release

  """
  The latest release of this application, without any config processing
  """
  currentReleaseUnprocessed: ReleaseUnprocessed
  deployed: Boolean!

  """
  Continuous deployment configuration
  """
  deploymentSource: DeploymentSource

  """
  Find a deployment by id, defaults to latest
  """
  deploymentStatus(evaluationId: String, id: ID): DeploymentStatus

  """
  Check if this app has a configured deployment source
  """
  hasDeploymentSource: Boolean!
  healthChecks(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int

    """
    Filter health checks by name
    """
    name: String
  ): CheckStateConnection!

  """
  Host issues affecting this app
  """
  hostIssues(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): IssueConnection

  """
  Autogenerated hostname for this application
  """
  hostname: String

  """
  Unique application ID
  """
  id: ID!

  """
  Resolve an image from a reference
  """
  image(ref: String!): Image

  """
  Image details
  """
  imageDetails: ImageVersion
  imageUpgradeAvailable: Boolean
  imageVersionTrackingEnabled: Boolean!

  """
  Authentication key to use with Instrumentation endpoints
  """
  instrumentsKey: String
  internalId: String!
  internalNumericId: Int!

  """
  Find an ip address by address string
  """
  ipAddress(address: String!): IPAddress
  ipAddresses(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): IPAddressConnection!

  """
  This object's unique key
  """
  key: String!

  """
  Latest image details
  """
  latestImageDetails: ImageVersion
  limitedAccessTokens(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): LimitedAccessTokenConnection!
  machine(id: String!): Machine
  machines(
    """
    Return only started/stopped machines (excludes destroyed, etc.)
    """
    active: Boolean

    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
    version: Int
  ): MachineConnection!
  metricTypes: [String!]!

  """
  The unique application name
  """
  name: String!
  network: String
  networkId: Int

  """
  Organization that owns this app
  """
  organization: Organization!
  parseConfig(definition: JSON!): AppConfig!

  """
  Fly platform version
  """
  platformVersion: PlatformVersionEnum
  processGroups: [ProcessGroup!]!
  regions: [Region!]!

  """
  Find a specific release
  """
  release(id: ID, version: Int): Release

  """
  Individual releases for this application
  """
  releases(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): ReleaseConnection!

  """
  Individual releases for this application, without any config processing
  """
  releasesUnprocessed(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
    status: String
  ): ReleaseUnprocessedConnection!
  role: AppRole

  """
  Application runtime
  """
  runtime: RuntimeType!

  """
  Secrets set on the application
  """
  secrets: [Secret!]!
  services: [Service!]!
  sharedIpAddress: String
  state: AppState!

  """
  Application status
  """
  status: String!
  taskGroupCounts: [TaskGroupCount!]!
  tsusage(kinds: [String!]!): JSON!
  usage: [AppUsage!]!
  version: Int!
  vmSize: VMSize!
  vms(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
    showCompleted: Boolean
  ): VMConnection!
  volume(internalId: String!): Volume

  """
  Volumes associated with app
  """
  volumes(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): VolumeConnection!
}

type AppCertificate implements Node {
  acmeAlpnConfigured: Boolean! @deprecated(reason: "use isAcmeAlpnConfigured")
  acmeDnsConfigured: Boolean! @deprecated(reason: "use isAcmeDNSConfigured")
  certificateAuthority: String
  certificateRequestedAt: ISO8601DateTime
  check: Boolean!
  clientStatus: String!
  configured: Boolean! @deprecated(reason: "use isConfigured")
  createdAt: ISO8601DateTime
  dnsProvider: String
  dnsValidationHostname: String!
  dnsValidationInstructions: String!
  dnsValidationTarget: String!
  domain: String
  hostname: String!
  id: ID!
  isAcmeAlpnConfigured: Boolean!
  isAcmeDnsConfigured: Boolean!
  isAcmeHttpConfigured: Boolean!
  isApex: Boolean!
  isConfigured: Boolean!
  isWildcard: Boolean!
  issued(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int
    includeExpired: Boolean

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): CertificateConnection!
  source: String
  validationErrors: [AppCertificateValidationError!]!
}

"""
The connection type for AppCertificate.
"""
type AppCertificateConnection {
  """
  A list of edges.
  """
  edges: [AppCertificateEdge]

  """
  A list of nodes.
  """
  nodes: [AppCertificate]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

"""
An edge in a connection.
"""
type AppCertificateEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: AppCertificate
}

type AppCertificateValidationError {
  message: String!
  timestamp: ISO8601DateTime!
}

type AppChange implements Node {
  """
  Object that triggered the change
  """
  actor: AppChangeActor
  actorType: String!
  app: App!
  createdAt: ISO8601DateTime!
  description: String!
  id: ID!
  status: String
  updatedAt: ISO8601DateTime!
  user: User
}

"""
Objects that change apps
"""
union AppChangeActor = Build | Release | Secret

"""
The connection type for AppChange.
"""
type AppChangeConnection {
  """
  A list of edges.
  """
  edges: [AppChangeEdge]

  """
  A list of nodes.
  """
  nodes: [AppChange]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

"""
An edge in a connection.
"""
type AppChangeEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: AppChange
}

type AppConfig {
  definition: JSON!
  errors: [String!]!
  services: [Service!]!
  valid: Boolean!
}

"""
The connection type for App.
"""
type AppConnection {
  """
  A list of edges.
  """
  edges: [AppEdge]

  """
  A list of nodes.
  """
  nodes: [App]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

"""
An edge in a connection.
"""
type AppEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: App
}

"""
app lock
"""
type AppLock {
  """
  Time when the lock expires
  """
  expiration: ISO8601DateTime!

  """
  Lock ID
  """
  lockId: ID!
}

interface AppRole {
  """
  The name of this role
  """
  name: String!
}

enum AppState {
  """
  App has been deployed
  """
  DEPLOYED

  """
  App has not been deployed
  """
  PENDING

  """
  App has been suspended
  """
  SUSPENDED
}

"""
Application usage data
"""
type AppUsage {
  """
  The timespan interval for this usage sample
  """
  interval: String!

  """
  Total requests for this time period
  """
  requestsCount: Int!

  """
  Total app execution time (in seconds) for this time period
  """
  totalAppExecS: Int!

  """
  Total GB transferred out in this time period
  """
  totalDataOutGB: Float!

  """
  The start of the timespan for this usage sample
  """
  ts: ISO8601DateTime!
}

"""
Autogenerated input type of AttachPostgresCluster
"""
input AttachPostgresClusterInput {
  """
  The application to attach postgres to
  """
  appId: ID!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The database to attach. Defaults to a new database with the same name as the app.
  """
  databaseName: String

  """
  The database user to create. Defaults to using the database name.
  """
  databaseUser: String

  """
  Flag used to indicate that flyctl will exec calls
  """
  manualEntry: Boolean

  """
  The postgres cluster application id
  """
  postgresClusterAppId: ID!

  """
  The environment variable name to set the connection string to. Defaults to DATABASE_URL
  """
  variableName: String
}

"""
Autogenerated return type of AttachPostgresCluster.
"""
type AttachPostgresClusterPayload {
  app: App!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  connectionString: String!
  environmentVariableName: String!
  postgresClusterApp: App!
}

type AutoscaleRegionConfig {
  """
  The region code
  """
  code: String!

  """
  The minimum number of VMs to run in this region
  """
  minCount: Int

  """
  The relative weight for this region
  """
  weight: Int
}

"""
Region autoscaling configuration
"""
input AutoscaleRegionConfigInput {
  """
  The region code to configure
  """
  code: String!

  """
  Minimum number of VMs to run in this region
  """
  minCount: Int

  """
  Reset the configuration for this region
  """
  reset: Boolean

  """
  The weight
  """
  weight: Int
}

enum AutoscaleStrategy {
  """
  place vms in regions near connection sources
  """
  CONNECTION_SOURCES

  """
  autoscaling is disabled
  """
  NONE

  """
  place vms in preferred regions by weight
  """
  PREFERRED_REGIONS
}

type AutoscalingConfig {
  backupRegions: [String!]!
  balanceRegions: Boolean!
  enabled: Boolean!
  maxCount: Int!
  minCount: Int!
  preferredRegion: String
  regions: [AutoscaleRegionConfig!]!
  strategy: AutoscaleStrategy!
}

"""
Represents non-fractional signed whole numeric values. Since the value may
exceed the size of a 32-bit integer, it's encoded as a string.
"""
scalar BigInt

enum BillingStatus {
  CURRENT
  DELINQUENT
  PAST_DUE
  SOURCE_REQUIRED
  SUSPENDED
  TRIAL_ACTIVE
  TRIAL_ENDED
}

type Build implements Node {
  app: App!
  commitId: String
  commitUrl: String
  createdAt: ISO8601DateTime!

  """
  The user who initiated the build
  """
  createdBy: User

  """
  Indicates if this build is complete and failed
  """
  failed: Boolean!
  id: ID!
  image: String

  """
  Indicates if this build is currently in progress
  """
  inProgress: Boolean!

  """
  Log output
  """
  logs: String!
  number: Int!

  """
  Status of the build
  """
  status: String!

  """
  Indicates if this build is complete and succeeded
  """
  succeeded: Boolean!
  updatedAt: ISO8601DateTime!
}

"""
The connection type for Build.
"""
type BuildConnection {
  """
  A list of edges.
  """
  edges: [BuildEdge]

  """
  A list of nodes.
  """
  nodes: [Build]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

"""
An edge in a connection.
"""
type BuildEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: Build
}

input BuildFinalImageInput {
  """
  Sha256 id of docker image
  """
  id: String!

  """
  Size in bytes of the docker image
  """
  sizeBytes: BigInt!

  """
  Tag used for docker image
  """
  tag: String!
}

input BuildImageOptsInput {
  """
  Set of build time variables passed to cli
  """
  buildArgs: JSON

  """
  Fly.toml build.buildpacks setting
  """
  buildPacks: [String!]

  """
  Fly.toml build.builder setting
  """
  builder: String

  """
  Builtin builder to use
  """
  builtIn: String

  """
  Builtin builder settings
  """
  builtInSettings: JSON

  """
  Path to dockerfile, if one exists
  """
  dockerfilePath: String

  """
  Unused in cli?
  """
  extraBuildArgs: JSON

  """
  Image label to use when tagging and pushing to the fly registry
  """
  imageLabel: String

  """
  Unused in cli?
  """
  imageRef: String

  """
  Do not use the build cache when building the image
  """
  noCache: Boolean

  """
  Whether publishing to the registry was requested
  """
  publish: Boolean

  """
  Docker tag used to publish image to registry
  """
  tag: String

  """
  Set the target build stage to build if the Dockerfile has more than one stage
  """
  target: String
}

"""
Autogenerated input type of BuildMachine
"""
input BuildMachineInput {
  """
  The ID of the app
  """
  appId: ID!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  Configuration
  """
  config: JSON!

  """
  [deprecated]
  """
  dedicationId: String

  """
  The flyd ID of the machine
  """
  id: String

  """
  Name of the machine
  """
  name: String

  """
  Region for the machine
  """
  region: String
}

"""
Autogenerated return type of BuildMachine.
"""
type BuildMachinePayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  config: JSON!
  hostId: Int
  name: String!
  region: String!
  timeout: Int!
}

input BuildStrategyAttemptInput {
  """
  Optional error message from strategy
  """
  error: String

  """
  Optional note about this strategy or its result
  """
  note: String

  """
  Result attempting this strategy
  """
  result: String!

  """
  Build strategy attempted
  """
  strategy: String!
}

input BuildTimingsInput {
  """
  Time to build and push the image, measured by flyctl
  """
  buildAndPushMs: BigInt

  """
  Time to build the image including create context, measured by flyctl
  """
  buildMs: BigInt

  """
  Time to initialize client used to connect to either remote or local builder
  """
  builderInitMs: BigInt

  """
  Time to create the build context tar file, measured by flyctl
  """
  contextBuildMs: BigInt

  """
  Time for builder to build image after receiving context, measured by flyctl
  """
  imageBuildMs: BigInt

  """
  Time to push completed image to registry, measured by flyctl
  """
  pushMs: BigInt
}

"""
Autogenerated input type of BuildVolume
"""
input BuildVolumeInput {
  """
  The application to attach the new volume to
  """
  appId: ID!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  docker image name to consider in volume placement
  """
  computeImage: String

  """
  compute requirements for volume placement (cpu, mem, gpu, ...)
  """
  computeRequirements: JSON

  """
  [deprecated]
  """
  dedicationId: String

  """
  Volume should be encrypted at rest
  """
  encrypted: Boolean = true

  """
  Desired region for volume
  """
  region: String!

  """
  Desired volume size, in GB
  """
  sizeGb: Int!
  snapshot: ID

  """
  Number of days to retain snapshots
  """
  snapshotRetentionDays: Int
}

"""
Autogenerated return type of BuildVolume.
"""
type BuildVolumePayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  imageRef: JSON
  ineligibleHosts: JSON
  machinesOnlyHosts: JSON
  ok: Boolean!
  platformVersion: String!
  region: String!
  restoreKey: String
  vaultSecretPath: String
}

input BuilderMetaInput {
  """
  Local or remote builder type
  """
  builderType: String!

  """
  Whther or not buildkit is enabled on builder
  """
  buildkitEnabled: Boolean

  """
  Docker version reported by builder
  """
  dockerVersion: String

  """
  Platform reported by the builder
  """
  platform: String

  """
  Remote builder app used
  """
  remoteAppName: ID

  """
  Remote builder machine used
  """
  remoteMachineId: ID
}

"""
Autogenerated return type of CancelBuild.
"""
type CancelBuildPayload {
  build: Build!
}

"""
A set of base64 messagepack encoded macaroon caveats (See https://github.com/superfly/macaroon)
"""
scalar CaveatSet

type Certificate implements Node {
  expiresAt: ISO8601DateTime!
  hostname: String!
  id: ID!
  type: String!
}

"""
The connection type for Certificate.
"""
type CertificateConnection {
  """
  A list of edges.
  """
  edges: [CertificateEdge]

  """
  A list of nodes.
  """
  nodes: [Certificate]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

"""
An edge in a connection.
"""
type CertificateEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: Certificate
}

"""
Autogenerated input type of ChangeOrganizationPlan
"""
input ChangeOrganizationPlanInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The id of the new plan.
  """
  newPlanId: String

  """
  The node ID of the organization
  """
  organizationId: ID!
}

"""
Autogenerated return type of ChangeOrganizationPlan.
"""
type ChangeOrganizationPlanPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  organization: Organization!
}

"""
health check
"""
type Check {
  httpHeaders: [CheckHeader!]
  httpMethod: String
  httpPath: String
  httpProtocol: HTTPProtocol
  httpTlsSkipVerify: Boolean

  """
  Check interval in milliseconds
  """
  interval: Int!
  name: String
  scriptArgs: [String!]
  scriptCommand: String

  """
  Check timeout in milliseconds
  """
  timeout: Int!
  type: CheckType!
}

"""
Autogenerated input type of CheckCertificate
"""
input CheckCertificateInput {
  """
  Application to ID
  """
  appId: ID!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  Certificate hostname to check
  """
  hostname: String!
}

"""
Autogenerated return type of CheckCertificate.
"""
type CheckCertificatePayload {
  app: App
  certificate: AppCertificate
  check: HostnameCheck

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated input type of CheckDomain
"""
input CheckDomainInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  Domain name to check
  """
  domainName: String!
}

"""
Autogenerated return type of CheckDomain.
"""
type CheckDomainPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  dnsAvailable: Boolean!
  domainName: String!
  registrationAvailable: Boolean!
  registrationPeriod: Int
  registrationPrice: Int
  registrationSupported: Boolean!
  tld: String!
  transferAvailable: Boolean!
}

"""
check job http response
"""
type CheckHTTPResponse implements Node {
  closeTs: String!
  connectedTs: String!
  dnsTs: String!
  firstTs: String!
  flyioDebug: JSON
  headers: JSON!
  id: ID!
  lastTs: String!
  location: CheckLocation!
  rawHeaders: String!
  rawOutput: [String!]!
  resolvedIp: String!
  sentTs: String!
  startTs: String!
  statusCode: Int!
  tlsTs: String
}

"""
The connection type for CheckHTTPResponse.
"""
type CheckHTTPResponseConnection {
  """
  A list of edges.
  """
  edges: [CheckHTTPResponseEdge]

  """
  A list of nodes.
  """
  nodes: [CheckHTTPResponse]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

"""
An edge in a connection.
"""
type CheckHTTPResponseEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: CheckHTTPResponse
}

"""
All available http checks verbs
"""
enum CheckHTTPVerb {
  GET
  HEAD
}

"""
HTTP header for a health check
"""
type CheckHeader {
  name: String!
  value: String!
}

input CheckHeaderInput {
  name: String!
  value: String!
}

input CheckInput {
  httpHeaders: [CheckHeaderInput!]
  httpMethod: HTTPMethod
  httpPath: String
  httpProtocol: HTTPProtocol
  httpTlsSkipVerify: Boolean

  """
  Check interval in milliseconds
  """
  interval: Int
  name: String
  scriptArgs: [String!]
  scriptCommand: String

  """
  Check timeout in milliseconds
  """
  timeout: Int
  type: CheckType!
}

"""
check job
"""
type CheckJob implements Node {
  httpOptions: CheckJobHTTPOptions
  id: ID!
  locations(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): CheckLocationConnection!
  nextRunAt: ISO8601DateTime
  runs(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): CheckJobRunConnection!
  schedule: String
  url: String!
}

"""
The connection type for CheckJob.
"""
type CheckJobConnection {
  """
  A list of edges.
  """
  edges: [CheckJobEdge]

  """
  A list of nodes.
  """
  nodes: [CheckJob]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

"""
An edge in a connection.
"""
type CheckJobEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: CheckJob
}

"""
health check state
"""
type CheckJobHTTPOptions {
  headers: [String!]!
  verb: CheckHTTPVerb!
}

"""
health check state
"""
input CheckJobHTTPOptionsInput {
  headers: [String!] = []
  verb: CheckHTTPVerb! = GET
}

"""
check job run
"""
type CheckJobRun implements Node {
  completedAt: ISO8601DateTime
  createdAt: ISO8601DateTime!
  httpOptions: CheckJobHTTPOptions!
  httpResponses(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): CheckHTTPResponseConnection!
  id: ID!
  locations(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): CheckLocationConnection!
  state: String!
  tests: [String!]!
  url: String!
}

"""
The connection type for CheckJobRun.
"""
type CheckJobRunConnection {
  """
  A list of edges.
  """
  edges: [CheckJobRunEdge]

  """
  A list of nodes.
  """
  nodes: [CheckJobRun]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

"""
An edge in a connection.
"""
type CheckJobRunEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: CheckJobRun
}

"""
check location
"""
type CheckLocation {
  coordinates: [Float!]!
  country: String!
  locality: String!
  name: String!
  state: String
  title: String!
}

"""
The connection type for CheckLocation.
"""
type CheckLocationConnection {
  """
  A list of edges.
  """
  edges: [CheckLocationEdge]

  """
  A list of nodes.
  """
  nodes: [CheckLocation]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

"""
An edge in a connection.
"""
type CheckLocationEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: CheckLocation
}

"""
health check state
"""
type CheckState {
  allocation: Allocation!
  allocationId: String!
  name: String!
  output(
    """
    Remove newlines and trim whitespace
    """
    compact: Boolean

    """
    The number of characters to truncate output to
    """
    limit: Int
  ): String!
  serviceName: String!
  status: String!
  type: CheckType!
  updatedAt: ISO8601DateTime!
}

"""
The connection type for CheckState.
"""
type CheckStateConnection {
  """
  A list of edges.
  """
  edges: [CheckStateEdge]

  """
  A list of nodes.
  """
  nodes: [CheckState]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

"""
An edge in a connection.
"""
type CheckStateEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: CheckState
}

enum CheckType {
  """
  http health check
  """
  HTTP

  """
  script health check
  """
  SCRIPT

  """
  tcp health check
  """
  TCP
}

type CliSession {
  metadata: JSON
  name: String
  signup: Boolean
}

"""
Autogenerated input type of ConfigureRegions
"""
input ConfigureRegionsInput {
  """
  Regions to allow running in
  """
  allowRegions: [String!]

  """
  The ID of the app
  """
  appId: ID!

  """
  Fallback regions. Used if preferred regions are having issues
  """
  backupRegions: [String!]

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  Regions to deny running in
  """
  denyRegions: [String!]

  """
  Process group to modify
  """
  group: String
}

"""
Autogenerated return type of ConfigureRegions.
"""
type ConfigureRegionsPayload {
  app: App!
  backupRegions: [Region!]!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  group: String
  regions: [Region!]!
}

"""
Autogenerated input type of ConfirmCliSession
"""
input ConfirmCliSessionInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  id: String!
  metadata: JSON
}

"""
Autogenerated return type of ConfirmCliSession.
"""
type ConfirmCliSessionPayload {
  cliSession: CliSession

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  success: Boolean
}

"""
Autogenerated input type of CreateAccessToken
"""
input CreateAccessTokenInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  name: String!
}

"""
Autogenerated return type of CreateAccessToken.
"""
type CreateAccessTokenPayload {
  accessToken: AccessToken!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated input type of CreateAddOn
"""
input CreateAddOnInput {
  """
  An optional application ID to attach the add-on to after provisioning
  """
  appId: ID

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  An optional name for the add-on
  """
  name: String

  """
  Options specific to the add-on
  """
  options: JSON

  """
  The organization which owns the add-on
  """
  organizationId: ID

  """
  A provider organization plan to set along with provisioning
  """
  organizationPlanId: String

  """
  The add-on plan ID
  """
  planId: ID

  """
  Desired primary region for the add-on
  """
  primaryRegion: String

  """
  Desired regions to place replicas in
  """
  readRegions: [String!]

  """
  The add-on type to provision
  """
  type: AddOnType!
}

"""
Autogenerated return type of CreateAddOn.
"""
type CreateAddOnPayload {
  addOn: AddOn!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated input type of CreateAndRegisterDomain
"""
input CreateAndRegisterDomainInput {
  """
  Enable auto renew on the registration
  """
  autoRenew: Boolean

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The domain name
  """
  name: String!

  """
  The node ID of the organization
  """
  organizationId: ID!

  """
  Enable whois privacy on the registration
  """
  whoisPrivacy: Boolean
}

"""
Autogenerated return type of CreateAndRegisterDomain.
"""
type CreateAndRegisterDomainPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  domain: Domain!
  organization: Organization!
}

"""
Autogenerated input type of CreateAndTransferDomain
"""
input CreateAndTransferDomainInput {
  """
  The authorization code
  """
  authorizationCode: String!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The domain name
  """
  name: String!

  """
  The node ID of the organization
  """
  organizationId: ID!
}

"""
Autogenerated return type of CreateAndTransferDomain.
"""
type CreateAndTransferDomainPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  domain: Domain!
  organization: Organization!
}

"""
Autogenerated input type of CreateApp
"""
input CreateAppInput {
  appRoleId: String

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  enableSubdomains: Boolean = false
  heroku: Boolean
  machines: Boolean = true

  """
  The name of the new application. Defaults to a random name.
  """
  name: String
  network: String

  """
  The node ID of the organization
  """
  organizationId: ID!
  preferredRegion: String

  """
  The application runtime
  """
  runtime: RuntimeType = FIRECRACKER
}

"""
Autogenerated return type of CreateApp.
"""
type CreateAppPayload {
  app: App!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated input type of CreateBuild
"""
input CreateBuildInput {
  """
  The name of the app being built
  """
  appName: ID!

  """
  The kind of builder being used
  """
  builderType: String!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  Options set for building image
  """
  imageOpts: BuildImageOptsInput

  """
  The ID of the machine being built (only set for machine builds)
  """
  machineId: ID

  """
  List of available build strategies that will be attempted
  """
  strategiesAvailable: [String!]!
}

"""
Autogenerated return type of CreateBuild.
"""
type CreateBuildPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  build id
  """
  id: ID!

  """
  stored build status
  """
  status: String!
}

"""
Autogenerated input type of CreateCheckJob
"""
input CreateCheckJobInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  http check options
  """
  httpOptions: CheckJobHTTPOptionsInput!

  """
  http checks locations
  """
  locations: [String!]!

  """
  Organization ID
  """
  organizationId: ID!

  """
  The URL to check
  """
  url: String!
}

"""
Autogenerated return type of CreateCheckJob.
"""
type CreateCheckJobPayload {
  checkJob: CheckJob!
  checkJobRun: CheckJobRun

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated input type of CreateCheckJobRun
"""
input CreateCheckJobRunInput {
  """
  Check Job ID
  """
  checkJobId: ID!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated return type of CreateCheckJobRun.
"""
type CreateCheckJobRunPayload {
  checkJob: CheckJob!
  checkJobRun: CheckJobRun

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated input type of CreateDNSPortal
"""
input CreateDNSPortalInput {
  """
  The secondary branding color
  """
  accentColor: String

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The unique name of this portal. A random name will be generated if omitted.
  """
  name: String

  """
  The node ID of the organization
  """
  organizationId: ID!

  """
  The primary branding color
  """
  primaryColor: String

  """
  The return url for this portal
  """
  returnUrl: String

  """
  The text to display for the return url link
  """
  returnUrlText: String

  """
  The support url for this portal
  """
  supportUrl: String

  """
  The text to display for the support url link
  """
  supportUrlText: String

  """
  The title of this portal
  """
  title: String
}

"""
Autogenerated return type of CreateDNSPortal.
"""
type CreateDNSPortalPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  dnsPortal: DNSPortal!
}

"""
Autogenerated input type of CreateDNSPortalSession
"""
input CreateDNSPortalSessionInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The node ID of the dns portal
  """
  dnsPortalId: ID!

  """
  The node ID of the domain to edit
  """
  domainId: ID!

  """
  Optionally override the portal's default return url for this session
  """
  returnUrl: String

  """
  Optionally override the portal's default return url text for this session
  """
  returnUrlText: String

  """
  Optionally override the portal's default title for this session
  """
  title: String
}

"""
Autogenerated return type of CreateDNSPortalSession.
"""
type CreateDNSPortalSessionPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  dnsPortalSession: DNSPortalSession!
}

"""
Autogenerated input type of CreateDNSRecord
"""
input CreateDNSRecordInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The node ID of the domain
  """
  domainId: ID!

  """
  The dns record name
  """
  name: String!

  """
  The content of the record
  """
  rdata: String!

  """
  The TTL in seconds
  """
  ttl: Int!

  """
  The type of the record
  """
  type: DNSRecordType!
}

"""
Autogenerated return type of CreateDNSRecord.
"""
type CreateDNSRecordPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  record: DNSRecord!
}

"""
Autogenerated input type of CreateDelegatedWireGuardToken
"""
input CreateDelegatedWireGuardTokenInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The name with which to refer to the peer
  """
  name: String

  """
  The node ID of the organization
  """
  organizationId: ID!
}

"""
Autogenerated return type of CreateDelegatedWireGuardToken.
"""
type CreateDelegatedWireGuardTokenPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  token: String!
}

"""
Autogenerated input type of CreateDoctorReport
"""
input CreateDoctorReportInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The report data
  """
  data: JSON!
}

"""
Autogenerated return type of CreateDoctorReport.
"""
type CreateDoctorReportPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  reportId: ID!
}

"""
Autogenerated return type of CreateDoctorUrl.
"""
type CreateDoctorUrlPayload {
  putUrl: String!
}

"""
Autogenerated input type of CreateDomain
"""
input CreateDomainInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The domain name
  """
  name: String!

  """
  The node ID of the organization
  """
  organizationId: ID!
}

"""
Autogenerated return type of CreateDomain.
"""
type CreateDomainPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  domain: Domain!
  organization: Organization!
}

"""
Autogenerated input type of CreateExtensionTosAgreement
"""
input CreateExtensionTosAgreementInput {
  """
  The add-on provider name
  """
  addOnProviderName: String!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The organization that agrees to the ToS
  """
  organizationId: ID
}

"""
Autogenerated return type of CreateExtensionTosAgreement.
"""
type CreateExtensionTosAgreementPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated input type of CreateLimitedAccessToken
"""
input CreateLimitedAccessTokenInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  expiry: String
  name: String!

  """
  Names of third-party configurations to opt into
  """
  optInThirdParties: [String!]

  """
  Names of third-party configurations to opt out of
  """
  optOutThirdParties: [String!]

  """
  The node ID of the organization
  """
  organizationId: ID!
  profile: String!
  profileParams: JSON
}

"""
Autogenerated return type of CreateLimitedAccessToken.
"""
type CreateLimitedAccessTokenPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  limitedAccessToken: LimitedAccessToken!
}

"""
Autogenerated input type of CreateMachineApp
"""
input CreateMachineAppInput {
  """
  The name of the app
  """
  appName: String!
  appRoleId: String

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  enableSubdomains: Boolean = false
  network: String

  """
  The organization slug
  """
  organizationSlug: String!
}

"""
Autogenerated return type of CreateMachineApp.
"""
type CreateMachineAppPayload {
  app: App!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated input type of CreateOrganization
"""
input CreateOrganizationInput {
  """
  Whether or not new apps in this org use Apps V2 by default
  """
  appsV2DefaultOn: Boolean
  billingParentOrganizationId: ID

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The name of the organization
  """
  name: String!
}

"""
Autogenerated input type of CreateOrganizationInvitation
"""
input CreateOrganizationInvitationInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The email to invite
  """
  email: String!

  """
  The node ID of the organization
  """
  organizationId: ID!
}

"""
Autogenerated return type of CreateOrganizationInvitation.
"""
type CreateOrganizationInvitationPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  invitation: OrganizationInvitation!
}

"""
Autogenerated return type of CreateOrganization.
"""
type CreateOrganizationPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  organization: Organization!
  token: String!
}

"""
Autogenerated input type of CreatePasswordReset
"""
input CreatePasswordResetInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  User email to create the password reset for
  """
  email: String!
}

"""
Autogenerated return type of CreatePasswordReset.
"""
type CreatePasswordResetPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  success: Boolean!
}

"""
Autogenerated input type of CreatePostgresClusterDatabase
"""
input CreatePostgresClusterDatabaseInput {
  """
  The name of the postgres cluster app
  """
  appName: String!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The name of the database
  """
  databaseName: String!
}

"""
Autogenerated return type of CreatePostgresClusterDatabase.
"""
type CreatePostgresClusterDatabasePayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  database: PostgresClusterDatabase!
  postgresClusterRole: PostgresClusterAppRole!
}

"""
Autogenerated input type of CreatePostgresClusterUser
"""
input CreatePostgresClusterUserInput {
  """
  The name of the postgres cluster app
  """
  appName: String!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The password of the user
  """
  password: String!

  """
  Should this user be a superuser
  """
  superuser: Boolean

  """
  The name of the database
  """
  username: String!
}

"""
Autogenerated return type of CreatePostgresClusterUser.
"""
type CreatePostgresClusterUserPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  postgresClusterRole: PostgresClusterAppRole!
  user: PostgresClusterUser!
}

"""
Autogenerated input type of CreateRelease
"""
input CreateReleaseInput {
  """
  The ID of the app
  """
  appId: ID!

  """
  The build ID linked to the release
  """
  buildId: ID

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  app definition
  """
  definition: JSON!

  """
  The image to deploy
  """
  image: String!

  """
  nomad or machines
  """
  platformVersion: String!

  """
  The strategy for replacing existing instances. Defaults to canary.
  """
  strategy: DeploymentStrategy!
}

"""
Autogenerated return type of CreateRelease.
"""
type CreateReleasePayload {
  app: App!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  release: Release
}

"""
Autogenerated input type of CreateStripeVerificationSession
"""
input CreateStripeVerificationSessionInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated return type of CreateStripeVerificationSession.
"""
type CreateStripeVerificationSessionPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  clientSecret: String
  verified: Boolean!
}

"""
Autogenerated input type of CreateTemplateDeployment
"""
input CreateTemplateDeploymentInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The node ID of the organization to move the app to
  """
  organizationId: ID!
  skipCc: Boolean
  template: JSON!
  variables: [PropertyInput!]
}

"""
Autogenerated return type of CreateTemplateDeployment.
"""
type CreateTemplateDeploymentPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  templateDeployment: TemplateDeployment!
}

"""
Autogenerated input type of CreateThirdPartyConfiguration
"""
input CreateThirdPartyConfigurationInput {
  """
  Restrictions to be placed on third-party caveats
  """
  caveats: CaveatSet

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  Whether to add this third-party caveat on tokens issued via `flyctl tokens create`
  """
  customLevel: ThirdPartyConfigurationLevel!

  """
  Whether to add this third-party caveat on session tokens issued to flyctl
  """
  flyctlLevel: ThirdPartyConfigurationLevel!

  """
  Location URL of the third-party service capable of discharging
  """
  location: String!

  """
  Friendly name for this configuration
  """
  name: String!

  """
  The node ID of the organization
  """
  organizationId: ID!

  """
  Whether to add this third-party caveat on Fly.io session tokens
  """
  uiexLevel: ThirdPartyConfigurationLevel!
}

"""
Autogenerated return type of CreateThirdPartyConfiguration.
"""
type CreateThirdPartyConfigurationPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  thirdPartyConfiguration: ThirdPartyConfiguration!
}

"""
Autogenerated input type of CreateUserSignup
"""
input CreateUserSignupInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  """
  path: [String!]

  """
  Sign up method (github, email, etc.)
  """
  signupMethod: String

  """
  Sign up source
  """
  source: String!

  """
  Timestamp of when the user arrived at the website
  """
  startedAt: String!

  """
  UTM query string params
  """
  utmParams: JSON
}

"""
Autogenerated return type of CreateUserSignup.
"""
type CreateUserSignupPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  saved: Boolean!
}

"""
Autogenerated input type of CreateVolume
"""
input CreateVolumeInput {
  """
  The application to attach the new volume to
  """
  appId: ID!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  Volume should be encrypted at rest
  """
  encrypted: Boolean = true
  fsType: FsTypeType = ext4

  """
  Volume name
  """
  name: String!

  """
  Desired region for volume
  """
  region: String!

  """
  Provision volume in a redundancy zone not already in use by this app
  """
  requireUniqueZone: Boolean

  """
  Desired volume size, in GB
  """
  sizeGb: Int!
  snapshotId: ID
}

"""
Autogenerated return type of CreateVolume.
"""
type CreateVolumePayload {
  app: App!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  volume: Volume!
}

"""
Autogenerated input type of CreateVolumeSnapshot
"""
input CreateVolumeSnapshotInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  volumeId: ID!
}

"""
Autogenerated return type of CreateVolumeSnapshot.
"""
type CreateVolumeSnapshotPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  volume: Volume!
}

type DNSPortal implements Node {
  accentColor: String!
  createdAt: ISO8601DateTime!
  id: ID!
  name: String!
  organization: Organization!
  primaryColor: String!
  returnUrl: String
  returnUrlText: String
  supportUrl: String
  supportUrlText: String
  title: String!
  updatedAt: ISO8601DateTime!
}

"""
The connection type for DNSPortal.
"""
type DNSPortalConnection {
  """
  A list of edges.
  """
  edges: [DNSPortalEdge]

  """
  A list of nodes.
  """
  nodes: [DNSPortal]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

"""
An edge in a connection.
"""
type DNSPortalEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: DNSPortal
}

type DNSPortalSession implements Node {
  createdAt: ISO8601DateTime!

  """
  The dns portal this session
  """
  dnsPortal: DNSPortal!
  expiresAt: ISO8601DateTime!
  id: ID!

  """
  Is this session expired?
  """
  isExpired: Boolean!

  """
  The overridden return url for this session
  """
  returnUrl: String

  """
  The overridden return url text for this session
  """
  returnUrlText: String

  """
  The overridden title for this session
  """
  title: String

  """
  The url to access this session's dns portal
  """
  url: String!
}

type DNSRecord implements Node {
  createdAt: ISO8601DateTime!

  """
  The domain this record belongs to
  """
  domain: Domain!

  """
  Fully qualified domain name for this record
  """
  fqdn: String!
  id: ID!

  """
  Is this record at the zone apex?
  """
  isApex: Boolean!

  """
  Is this a system record? System records are managed by fly and not editable.
  """
  isSystem: Boolean!

  """
  Is this record a wildcard?
  """
  isWildcard: Boolean!

  """
  The name of this record. @ indicates the record is at the zone apex.
  """
  name: String!

  """
  The record data
  """
  rdata: String!

  """
  The number of seconds this record can be cached for
  """
  ttl: Int!

  """
  The type of record
  """
  type: DNSRecordType!
  updatedAt: ISO8601DateTime!
}

type DNSRecordAttributes {
  """
  The name of the record.
  """
  name: String!

  """
  The record data.
  """
  rdata: String!

  """
  The number of seconds this record can be cached for.
  """
  ttl: Int!

  """
  The type of record.
  """
  type: DNSRecordType!
}

enum DNSRecordChangeAction {
  """
  A record should be created with the provided attributes
  """
  CREATE

  """
  A record with the provided ID should be deleted
  """
  DELETE

  """
  A record with the provided ID should be updated
  """
  UPDATE
}

input DNSRecordChangeInput {
  """
  The action to perform on this record.
  """
  action: DNSRecordChangeAction!

  """
  The name of the record. If omitted it will default to @ - the zone apex.
  """
  name: String

  """
  The record data. Required if action is CREATE
  """
  rdata: String

  """
  The id of the record this action will apply to. This is required if the action is UPDATE or DELETE.
  """
  recordId: ID

  """
  The number of seconds this record can be cached for. Defaults to 1 hour.
  """
  ttl: Int

  """
  The record type. This is required if action is CREATE.
  """
  type: DNSRecordType
}

"""
The connection type for DNSRecord.
"""
type DNSRecordConnection {
  """
  A list of edges.
  """
  edges: [DNSRecordEdge]

  """
  A list of nodes.
  """
  nodes: [DNSRecord]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

type DNSRecordDiff {
  """
  The action that was performed.
  """
  action: DNSRecordChangeAction!

  """
  The attributes for this record after the action was performed.
  """
  newAttributes: DNSRecordAttributes

  """
  The text representation of this record after the action was performed.
  """
  newText: String

  """
  The attributes for this record before the action was performed.
  """
  oldAttributes: DNSRecordAttributes

  """
  The text representation of this record before the action was performed.
  """
  oldText: String
}

"""
An edge in a connection.
"""
type DNSRecordEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: DNSRecord
}

enum DNSRecordType {
  A
  AAAA
  ALIAS
  CNAME
  MX
  NS
  SOA
  SRV
  TXT
}

type DNSRecordWarning {
  """
  The action to perform.
  """
  action: DNSRecordChangeAction!

  """
  The desired attributes for this record.
  """
  attributes: DNSRecordAttributes!

  """
  The warning message.
  """
  message: String!

  """
  The record this warning applies to.
  """
  record: DNSRecord
}

type DelegatedWireGuardToken implements Node {
  id: ID!
  name: String!
}

"""
The connection type for DelegatedWireGuardToken.
"""
type DelegatedWireGuardTokenConnection {
  """
  A list of edges.
  """
  edges: [DelegatedWireGuardTokenEdge]

  """
  A list of nodes.
  """
  nodes: [DelegatedWireGuardToken]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

"""
An edge in a connection.
"""
type DelegatedWireGuardTokenEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: DelegatedWireGuardToken
}

"""
Autogenerated input type of DeleteAccessToken
"""
input DeleteAccessTokenInput {
  """
  The node ID of the access token
  """
  accessTokenId: ID!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated return type of DeleteAccessToken.
"""
type DeleteAccessTokenPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated input type of DeleteAddOn
"""
input DeleteAddOnInput {
  """
  The ID of the add-on to delete
  """
  addOnId: ID

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The name of the add-on to delete
  """
  name: String

  """
  The add-on service provider type
  """
  provider: String
}

"""
Autogenerated return type of DeleteAddOn.
"""
type DeleteAddOnPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  deletedAddOnName: String
}

"""
Autogenerated return type of DeleteApp.
"""
type DeleteAppPayload {
  """
  The organization that owned the deleted app
  """
  organization: Organization!
}

"""
Autogenerated return type of DeleteCertificate.
"""
type DeleteCertificatePayload {
  app: App
  certificate: AppCertificate
  errors: [String!]
}

"""
Autogenerated input type of DeleteDNSPortal
"""
input DeleteDNSPortalInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The node ID of the dns portal
  """
  dnsPortalId: ID!
}

"""
Autogenerated return type of DeleteDNSPortal.
"""
type DeleteDNSPortalPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The organization that owned the dns portal
  """
  organization: Organization!
}

"""
Autogenerated input type of DeleteDNSPortalSession
"""
input DeleteDNSPortalSessionInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The node ID of the dns portal session
  """
  dnsPortalSessionId: ID!
}

"""
Autogenerated return type of DeleteDNSPortalSession.
"""
type DeleteDNSPortalSessionPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The dns portal that owned the session
  """
  dnsPortal: DNSPortal!
}

"""
Autogenerated input type of DeleteDNSRecord
"""
input DeleteDNSRecordInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The node ID of the DNS record
  """
  recordId: ID!
}

"""
Autogenerated return type of DeleteDNSRecord.
"""
type DeleteDNSRecordPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  domain: Domain!
}

"""
Autogenerated input type of DeleteDelegatedWireGuardToken
"""
input DeleteDelegatedWireGuardTokenInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The name with which to refer to the token
  """
  name: String

  """
  The node ID of the organization
  """
  organizationId: ID!

  """
  The raw WireGuard token
  """
  token: String
}

"""
Autogenerated return type of DeleteDelegatedWireGuardToken.
"""
type DeleteDelegatedWireGuardTokenPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  token: String!
}

"""
Autogenerated input type of DeleteDeploymentSource
"""
input DeleteDeploymentSourceInput {
  """
  The application to update
  """
  appId: String!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated return type of DeleteDeploymentSource.
"""
type DeleteDeploymentSourcePayload {
  app: App

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated input type of DeleteDomain
"""
input DeleteDomainInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The node ID of the domain
  """
  domainId: ID!
}

"""
Autogenerated return type of DeleteDomain.
"""
type DeleteDomainPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  organization: Organization!
}

"""
Autogenerated input type of DeleteHealthCheckHandler
"""
input DeleteHealthCheckHandlerInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  Handler name
  """
  name: String!

  """
  The node ID of the organization
  """
  organizationId: ID!
}

"""
Autogenerated return type of DeleteHealthCheckHandler.
"""
type DeleteHealthCheckHandlerPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated input type of DeleteIdentity
"""
input DeleteIdentityInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The node ID of the identity
  """
  identityId: ID!
}

"""
Autogenerated return type of DeleteIdentity.
"""
type DeleteIdentityPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  identities: [Identity!]!
}

"""
Autogenerated input type of DeleteLimitedAccessToken
"""
input DeleteLimitedAccessTokenInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The node ID for real
  """
  id: ID

  """
  The email of the user who revoked the token
  """
  revokedBy: String

  """
  The root of the macaroon
  """
  token: String
}

"""
Autogenerated return type of DeleteLimitedAccessToken.
"""
type DeleteLimitedAccessTokenPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  revokedBy: String
  token: String
}

"""
Autogenerated input type of DeleteOrganization
"""
input DeleteOrganizationInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The ID of the organization to delete
  """
  organizationId: ID!
}

"""
Autogenerated input type of DeleteOrganizationInvitation
"""
input DeleteOrganizationInvitationInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The node ID of the invitation
  """
  invitationId: ID!
}

"""
Autogenerated return type of DeleteOrganizationInvitation.
"""
type DeleteOrganizationInvitationPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  organization: Organization!
}

"""
Autogenerated input type of DeleteOrganizationMembership
"""
input DeleteOrganizationMembershipInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The node ID of the organization
  """
  organizationId: ID!

  """
  The node ID of the user
  """
  userId: ID!
}

"""
Autogenerated return type of DeleteOrganizationMembership.
"""
type DeleteOrganizationMembershipPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  organization: Organization!
  user: User!
}

"""
Autogenerated return type of DeleteOrganization.
"""
type DeleteOrganizationPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  deletedOrganizationId: ID!
}

"""
Autogenerated input type of DeleteRemoteBuilder
"""
input DeleteRemoteBuilderInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The node ID of the organization
  """
  organizationId: ID!
}

"""
Autogenerated return type of DeleteRemoteBuilder.
"""
type DeleteRemoteBuilderPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  organization: Organization!
}

"""
Autogenerated input type of DeleteThirdPartyConfiguration
"""
input DeleteThirdPartyConfigurationInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The node ID of the configuration
  """
  thirdPartyConfigurationId: ID!
}

"""
Autogenerated return type of DeleteThirdPartyConfiguration.
"""
type DeleteThirdPartyConfigurationPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  ok: Boolean!
}

"""
Autogenerated input type of DeleteUser
"""
input DeleteUserInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated return type of DeleteUser.
"""
type DeleteUserPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  deletedUserId: ID!
}

"""
Autogenerated input type of DeleteVolume
"""
input DeleteVolumeInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  Unique lock ID
  """
  lockId: ID

  """
  The node ID of the volume
  """
  volumeId: ID!
}

"""
Autogenerated return type of DeleteVolume.
"""
type DeleteVolumePayload {
  app: App!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated input type of DeployImage
"""
input DeployImageInput {
  """
  The ID of the app
  """
  appId: ID!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  app definition
  """
  definition: JSON

  """
  The image to deploy
  """
  image: String!

  """
  Network services to expose
  """
  services: [ServiceInput!]

  """
  The strategy for replacing existing instances. Defaults to canary.
  """
  strategy: DeploymentStrategy
}

"""
Autogenerated return type of DeployImage.
"""
type DeployImagePayload {
  app: App!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  release: Release
  releaseCommand: ReleaseCommand
}

"""
Continuous deployment configuration
"""
type DeploymentSource {
  backend: JSON!
  baseDir: String!
  connected: Boolean!
  id: ID!
  provider: String!

  """
  The ref to build from
  """
  ref: String!
  repositoryId: String!

  """
  The repository to fetch source code from
  """
  repositoryUrl: String!
}

type DeploymentStatus {
  allocations: [Allocation!]!
  description: String!
  desiredCount: Int!
  healthyCount: Int!

  """
  Unique ID for this deployment
  """
  id: ID!
  inProgress: Boolean!
  placedCount: Int!
  promoted: Boolean!
  status: String!
  successful: Boolean!
  unhealthyCount: Int!
  version: Int!
}

enum DeploymentStrategy {
  """
  Launch all new instances before shutting down previous instances
  """
  BLUEGREEN

  """
  Ensure new instances are healthy before continuing with a rolling deployment
  """
  CANARY

  """
  Deploy new instances all at once
  """
  IMMEDIATE

  """
  Incrementally replace old instances with new ones
  """
  ROLLING

  """
  Incrementally replace old instances with new ones, 1 by 1
  """
  ROLLING_ONE

  """
  Deploy new instances all at once
  """
  SIMPLE
}

"""
Autogenerated input type of DestroyIdentities
"""
input DestroyIdentitiesInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated return type of DestroyIdentities.
"""
type DestroyIdentitiesPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  success: Boolean
}

"""
Autogenerated input type of DetachPostgresCluster
"""
input DetachPostgresClusterInput {
  """
  The application to detach postgres from
  """
  appId: ID!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The postgres cluster application id
  """
  postgresClusterAppId: ID!

  """
  The postgres attachment id
  """
  postgresClusterAttachmentId: ID
}

"""
Autogenerated return type of DetachPostgresCluster.
"""
type DetachPostgresClusterPayload {
  app: App!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  postgresClusterApp: App!
}

"""
Autogenerated input type of DisableOneTimePassword
"""
input DisableOneTimePasswordInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated return type of DisableOneTimePassword.
"""
type DisableOneTimePasswordPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated input type of DischargeRootToken
"""
input DischargeRootTokenInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  expiry: String
  organizationId: Int!
  rootToken: String!
}

"""
Autogenerated return type of DischargeRootToken.
"""
type DischargeRootTokenPayload {
  authToken: String!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

type Domain implements Node {
  autoRenew: Boolean
  createdAt: ISO8601DateTime!

  """
  The delegated nameservers for the registration
  """
  delegatedNameservers: [String!]

  """
  The dns records for this domain
  """
  dnsRecords(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): DNSRecordConnection!
  dnsStatus: DomainDNSStatus!
  expiresAt: ISO8601DateTime
  id: ID!

  """
  The name for this domain
  """
  name: String!

  """
  The organization that owns this domain
  """
  organization: Organization!
  registrationStatus: DomainRegistrationStatus!
  updatedAt: ISO8601DateTime!

  """
  The nameservers for the hosted zone
  """
  zoneNameservers: [String!]!
}

"""
The connection type for Domain.
"""
type DomainConnection {
  """
  A list of edges.
  """
  edges: [DomainEdge]

  """
  A list of nodes.
  """
  nodes: [Domain]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

enum DomainDNSStatus {
  """
  The DNS zone has not been created yet
  """
  PENDING

  """
  The DNS zone is ready
  """
  READY

  """
  The DNS zone is being updated
  """
  UPDATING
}

"""
An edge in a connection.
"""
type DomainEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: Domain
}

enum DomainRegistrationStatus {
  """
  The domain registration has expired
  """
  EXPIRED

  """
  The domain is registered
  """
  REGISTERED

  """
  The domain is being registered
  """
  REGISTERING

  """
  The domain is being transferred
  """
  TRANSFERRING

  """
  The domain is not registered on fly
  """
  UNMANAGED
}

"""
Autogenerated input type of DummyWireGuardPeer
"""
input DummyWireGuardPeerInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The node ID of the organization
  """
  organizationId: ID!

  """
  The region in which to deploy the peer
  """
  region: String
}

"""
Autogenerated return type of DummyWireGuardPeer.
"""
type DummyWireGuardPeerPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  endpointip: String!
  localpub: String!
  peerip: String!
  privkey: String!
  pubkey: String!
}

type EgressIPAddress implements Node {
  """
  ID of the object.
  """
  id: ID!
  ip: String!
  region: String!
  version: Int!
}

"""
The connection type for EgressIPAddress.
"""
type EgressIPAddressConnection {
  """
  A list of edges.
  """
  edges: [EgressIPAddressEdge]

  """
  A list of nodes.
  """
  nodes: [EgressIPAddress]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

"""
An edge in a connection.
"""
type EgressIPAddressEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: EgressIPAddress
}

type EmptyAppRole implements AppRole {
  """
  The name of this role
  """
  name: String!
}

"""
Autogenerated input type of EnableDedicatedVms
"""
input EnableDedicatedVmsInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated return type of EnableDedicatedVms.
"""
type EnableDedicatedVmsPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  success: Boolean
}

"""
Autogenerated input type of EnableOneTimePassword
"""
input EnableOneTimePasswordInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  token: String!
  verificationCode: String
}

"""
Autogenerated return type of EnableOneTimePassword.
"""
type EnableOneTimePasswordPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  qrCode: String!
  status: String!
  uri: String!
  verification: String!
}

"""
Autogenerated input type of EnablePostgresConsul
"""
input EnablePostgresConsulInput {
  """
  The ID of the app
  """
  appId: ID

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  region: String
}

"""
Autogenerated return type of EnablePostgresConsul.
"""
type EnablePostgresConsulPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  consulUrl: String!
}

"""
Autogenerated input type of EnsureDepotRemoteBuilder
"""
input EnsureDepotRemoteBuilderInput {
  """
  The unique application name
  """
  appName: String

  """
  The scope of the builder; either "app" or "organization"
  """
  builderScope: String

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The node ID of the organization
  """
  organizationId: ID

  """
  Desired region for the remote builder
  """
  region: String
}

"""
Autogenerated return type of EnsureDepotRemoteBuilder.
"""
type EnsureDepotRemoteBuilderPayload {
  buildId: String!
  buildToken: String!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated input type of EnsureFlyctlMachineHost
"""
input EnsureFlyctlMachineHostInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The node ID of the organization
  """
  organizationId: ID!
}

"""
Autogenerated return type of EnsureFlyctlMachineHost.
"""
type EnsureFlyctlMachineHostPayload {
  app: App!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated input type of EnsureMachineRemoteBuilder
"""
input EnsureMachineRemoteBuilderInput {
  """
  The unique application name
  """
  appName: String

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The node ID of the organization
  """
  organizationId: ID

  """
  Desired region for the remote builder
  """
  region: String

  """
  Use v2 machines
  """
  v2: Boolean
}

"""
Autogenerated return type of EnsureMachineRemoteBuilder.
"""
type EnsureMachineRemoteBuilderPayload {
  app: App!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  machine: Machine!
}

"""
Autogenerated input type of EnsureOrganizationStripeContainerSubscription
"""
input EnsureOrganizationStripeContainerSubscriptionInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The node ID of the organization
  """
  organizationId: ID!
}

"""
Autogenerated return type of EnsureOrganizationStripeContainerSubscription.
"""
type EnsureOrganizationStripeContainerSubscriptionPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  organization: Organization!
}

"""
Autogenerated input type of EstablishSSHKey
"""
input EstablishSSHKeyInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The node ID of the organization
  """
  organizationId: ID!

  """
  Establish a key even if one is already set
  """
  override: Boolean
}

"""
Autogenerated return type of EstablishSSHKey.
"""
type EstablishSSHKeyPayload {
  certificate: String!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated input type of ExportDNSZone
"""
input ExportDNSZoneInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  ID of the domain to export
  """
  domainId: ID!
}

"""
Autogenerated return type of ExportDNSZone.
"""
type ExportDNSZonePayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  contents: String!
  domain: Domain!
}

"""
Autogenerated input type of ExtendVolume
"""
input ExtendVolumeInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The target volume size
  """
  sizeGb: Int!

  """
  The node ID of the volume
  """
  volumeId: ID!
}

"""
Autogenerated return type of ExtendVolume.
"""
type ExtendVolumePayload {
  app: App!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  needsRestart: Boolean!
  volume: Volume!
}

"""
Autogenerated input type of FinishBuild
"""
input FinishBuildInput {
  """
  The name of the app being built
  """
  appName: ID!

  """
  Build id returned by createBuild() mutation
  """
  buildId: ID!

  """
  Metadata about the builder
  """
  builderMeta: BuilderMetaInput

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  Information about the docker image that was built
  """
  finalImage: BuildFinalImageInput

  """
  Log or error output
  """
  logs: String

  """
  The ID of the machine being built (only set for machine builds)
  """
  machineId: ID

  """
  Indicate whether build completed or failed
  """
  status: String!

  """
  Build strategies attempted and their result, should be in order of attempt
  """
  strategiesAttempted: [BuildStrategyAttemptInput!]

  """
  Timings for different phases of the build
  """
  timings: BuildTimingsInput
}

"""
Autogenerated return type of FinishBuild.
"""
type FinishBuildPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  build id
  """
  id: ID!

  """
  stored build status
  """
  status: String!

  """
  wall clock time for this build
  """
  wallclockTimeMs: Int!
}

type FlyPlatform {
  """
  Latest flyctl release details
  """
  flyctl: FlyctlRelease!

  """
  Fly global regions
  """
  regions: [Region!]!

  """
  Region current request from
  """
  requestRegion: String

  """
  Available VM sizes
  """
  vmSizes: [VMSize!]!
}

type FlyctlMachineHostAppRole implements AppRole {
  """
  The name of this role
  """
  name: String!
}

type FlyctlRelease {
  timestamp: ISO8601DateTime!
  version: String!
}

"""
Autogenerated input type of ForkVolume
"""
input ForkVolumeInput {
  """
  The application to attach the new volume to
  """
  appId: ID!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  Unique lock ID
  """
  lockId: ID

  """
  Lock the new volume to only usable on machines
  """
  machinesOnly: Boolean

  """
  Volume name
  """
  name: String

  """
  Enables experimental cross-host volume forking
  """
  remote: Boolean

  """
  The volume to fork
  """
  sourceVolId: ID!
}

"""
Autogenerated return type of ForkVolume.
"""
type ForkVolumePayload {
  app: App!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  volume: Volume!
}

enum FsTypeType {
  """
  default ext4 filesystem
  """
  ext4

  """
  raw block device, no filesystem
  """
  raw
}

"""
Autogenerated input type of GenerateTwoFactorRecoveryCodes
"""
input GenerateTwoFactorRecoveryCodesInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated return type of GenerateTwoFactorRecoveryCodes.
"""
type GenerateTwoFactorRecoveryCodesPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  recoveryCodes: [String!]!
}

type GithubAppInstallation {
  editUrl: String!
  id: ID!
  owner: String!
  repositories: [GithubRepository!]!
}

type GithubIntegration {
  installationUrl: String!
  installations: [GithubAppInstallation!]!
  viewerAuthenticated: Boolean!
}

type GithubRepository {
  fork: Boolean!
  fullName: String!
  id: String!
  name: String!
  private: Boolean!
}

"""
Autogenerated input type of GrantPostgresClusterUserAccess
"""
input GrantPostgresClusterUserAccessInput {
  """
  The name of the postgres cluster app
  """
  appName: String!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The database to grant access to
  """
  databaseName: String!

  """
  The name of the database
  """
  username: String!
}

"""
Autogenerated return type of GrantPostgresClusterUserAccess.
"""
type GrantPostgresClusterUserAccessPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  database: PostgresClusterDatabase!
  postgresClusterRole: PostgresClusterAppRole!
  user: PostgresClusterUser!
}

enum HTTPMethod {
  DELETE
  GET
  HEAD
  PATCH
  POST
  PUT
}

enum HTTPProtocol {
  """
  HTTP protocol
  """
  HTTP

  """
  HTTPS protocol
  """
  HTTPS
}

type HealthCheck {
  """
  Raw name of entity
  """
  entity: String!

  """
  Time check last passed
  """
  lastPassing: ISO8601DateTime

  """
  Check name
  """
  name: String!

  """
  Latest check output
  """
  output: String

  """
  Current check state
  """
  state: String!
}

"""
The connection type for HealthCheck.
"""
type HealthCheckConnection {
  """
  A list of edges.
  """
  edges: [HealthCheckEdge]

  """
  A list of nodes.
  """
  nodes: [HealthCheck]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

"""
An edge in a connection.
"""
type HealthCheckEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: HealthCheck
}

type HealthCheckHandler {
  """
  Handler name
  """
  name: String!

  """
  Handler type (Slack or Pagerduty)
  """
  type: String!
}

"""
The connection type for HealthCheckHandler.
"""
type HealthCheckHandlerConnection {
  """
  A list of edges.
  """
  edges: [HealthCheckHandlerEdge]

  """
  A list of nodes.
  """
  nodes: [HealthCheckHandler]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

"""
An edge in a connection.
"""
type HealthCheckHandlerEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: HealthCheckHandler
}

type HerokuApp {
  id: String!
  name: String!
  region: String
  releasedAt: ISO8601DateTime!
  stack: String
  teamName: String
}

type HerokuIntegration {
  herokuApps: [HerokuApp!]!
  viewerAuthenticated: Boolean!
}

type Host implements Node {
  id: ID!
}

type HostnameCheck {
  aRecords: [String!]!
  aaaaRecords: [String!]!
  acmeDnsConfigured: Boolean!
  caaRecords: [String!]!
  cnameRecords: [String!]!
  dnsConfigured: Boolean!
  dnsProvider: String
  dnsVerificationRecord: String
  errors: [String!]
  id: ID!
  isProxied: Boolean!
  resolvedAddresses: [String!]!
  soa: String
}

type IPAddress implements Node {
  address: String!
  createdAt: ISO8601DateTime!
  id: ID!
  network: Network
  region: String
  serviceName: String
  type: IPAddressType!
}

"""
The connection type for IPAddress.
"""
type IPAddressConnection {
  """
  A list of edges.
  """
  edges: [IPAddressEdge]

  """
  A list of nodes.
  """
  nodes: [IPAddress]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

"""
An edge in a connection.
"""
type IPAddressEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: IPAddress
}

enum IPAddressType {
  private_v6
  shared_v4
  v4
  v6
}

"""
An ISO 8601-encoded datetime
"""
scalar ISO8601DateTime @specifiedBy(url: "https://tools.ietf.org/html/rfc3339")

type Identity {
  """
  The primary email address configured with this provider
  """
  email: String!
  id: ID!

  """
  Name used on this identity
  """
  name: String

  """
  Provider name
  """
  provider: String

  """
  Sealed OAuth token for the provider
  """
  sealedToken: String

  """
  OAuth token for the provider
  """
  token: String

  """
  ID on the provider
  """
  uid: String!
}

type Image {
  absoluteRef: String!
  compressedSize: Int! @deprecated(reason: "Int cannot handle sizes over 2GB. Use compressed_size_full instead")
  compressedSizeFull: BigInt!
  config: JSON!
  configDigest: JSON!
  createdAt: ISO8601DateTime!
  digest: String!
  id: ID!
  label: String!
  manifest: JSON!
  ref: String!
  registry: String!
  repository: String!
  tag: String
}

type ImageVersion {
  digest: String!
  registry: String!
  repository: String!
  tag: String!
  version: String
}

"""
Autogenerated return type of ImportCertificate.
"""
type ImportCertificatePayload {
  app: App
  appCertificate: AppCertificate
  certificate: Certificate
  errors: [String!]
}

"""
Autogenerated input type of ImportDNSZone
"""
input ImportDNSZoneInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  ID of the domain to export
  """
  domainId: ID!
  zonefile: String!
}

"""
Autogenerated return type of ImportDNSZone.
"""
type ImportDNSZonePayload {
  changes: [DNSRecordDiff!]!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  domain: Domain!
  warnings: [DNSRecordWarning!]!
}

type Issue implements Node {
  createdAt: ISO8601DateTime!
  id: ID!
  internalId: String!

  """
  Issue message
  """
  message: String
  resolvedAt: ISO8601DateTime
  updatedAt: ISO8601DateTime!
}

"""
Autogenerated input type of IssueCertificate
"""
input IssueCertificateInput {
  """
  The names of the apps this certificate will be limited to accessing
  """
  appNames: [String!]

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  Email address of user to be issued certificate
  """
  email: String @deprecated(reason: "use principals")

  """
  The node ID of the organization
  """
  organizationId: ID!

  """
  SSH principals for certificate (e.g. ["fly", "root"])
  """
  principals: [String!]

  """
  The openssh-formatted ED25519 public key to issue the certificate for
  """
  publicKey: String

  """
  Unix username valid for certificate
  """
  username: String @deprecated(reason: "use principals")

  """
  Hours for which certificate will be valid
  """
  validHours: Int
}

"""
Autogenerated return type of IssueCertificate.
"""
type IssueCertificatePayload {
  certificate: String!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The private key, if a public_key wasn't specified
  """
  key: String @deprecated(reason: "Specify your own public key")
}

"""
The connection type for Issue.
"""
type IssueConnection {
  """
  A list of edges.
  """
  edges: [IssueEdge]

  """
  A list of nodes.
  """
  nodes: [Issue]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

"""
An edge in a connection.
"""
type IssueEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: Issue
}

"""
Untyped JSON data
"""
scalar JSON

"""
Autogenerated input type of KillMachine
"""
input KillMachineInput {
  """
  The ID of the app
  """
  appId: ID

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  machine id
  """
  id: String!
}

"""
Autogenerated return type of KillMachine.
"""
type KillMachinePayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  machine: Machine!
}

"""
Autogenerated input type of LaunchMachine
"""
input LaunchMachineInput {
  """
  The ID of the app
  """
  appId: ID

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  Configuration
  """
  config: JSON!

  """
  The ID of the machine
  """
  id: String

  """
  The name of the machine
  """
  name: String

  """
  The node ID of the organization
  """
  organizationId: ID

  """
  Region for the machine
  """
  region: String
}

"""
Autogenerated return type of LaunchMachine.
"""
type LaunchMachinePayload {
  app: App!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  machine: Machine!
}

type LimitedAccessToken implements Node {
  createdAt: ISO8601DateTime!
  expiresAt: ISO8601DateTime!
  id: ID!
  name: String!
  organization: Organization!
  profile: String!
  profileParams: JSON
  revokedAt: ISO8601DateTime
  revokedBy: String
  token: String!
  tokenHeader: String
  user: User!
}

"""
The connection type for LimitedAccessToken.
"""
type LimitedAccessTokenConnection {
  """
  A list of edges.
  """
  edges: [LimitedAccessTokenEdge]

  """
  A list of nodes.
  """
  nodes: [LimitedAccessToken]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

"""
An edge in a connection.
"""
type LimitedAccessTokenEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: LimitedAccessToken
}

"""
Autogenerated input type of LockApp
"""
input LockAppInput {
  """
  The ID of the app
  """
  appId: ID!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated return type of LockApp.
"""
type LockAppPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  When this lock automatically expires
  """
  expiration: ISO8601DateTime

  """
  Unique lock ID
  """
  lockId: ID
}

type LogEntry {
  id: String!
  instanceId: String!
  level: String!
  message: String!
  region: String!
  timestamp: ISO8601DateTime!
}

"""
Autogenerated input type of LogOut
"""
input LogOutInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated return type of LogOut.
"""
type LogOutPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  ok: Boolean!
}

type LoggedCertificate implements Node {
  cert: String!
  id: ID!
  root: Boolean!
}

"""
The connection type for LoggedCertificate.
"""
type LoggedCertificateConnection {
  """
  A list of edges.
  """
  edges: [LoggedCertificateEdge]

  """
  A list of nodes.
  """
  nodes: [LoggedCertificate]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

"""
An edge in a connection.
"""
type LoggedCertificateEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: LoggedCertificate
}

type Macaroon implements Principal {
  """
  URL for avatar or placeholder
  """
  avatarUrl: String!
  blankPassword: Boolean @deprecated(reason: "Use User fragment on Viewer instead")
  createdAt: ISO8601DateTime @deprecated(reason: "Use User fragment on Viewer instead")

  """
  Email address for principal
  """
  email: String!
  emailVerified: Boolean @deprecated(reason: "Use User fragment on Viewer instead")
  featureFlags: [String!] @deprecated(reason: "Use User fragment on Viewer instead")
  hasNodeproxyApps: Boolean @deprecated(reason: "Use User fragment on Viewer instead")
  hasRecoveryCodes: Boolean @deprecated(reason: "Use User fragment on Viewer instead")
  highRisk: Boolean @deprecated(reason: "Use User fragment on Viewer instead")
  id: ID @deprecated(reason: "Use User fragment on Viewer instead")
  identities: [Identity!] @deprecated(reason: "Use User fragment on Viewer instead")

  """
  Find an identity by provider
  """
  identity(provider: String!): Identity @deprecated(reason: "Use User fragment on Viewer instead")
  lastRegion: String @deprecated(reason: "Use User fragment on Viewer instead")

  """
  Display name of principal
  """
  name: String
  organizations(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): OrganizationConnection @deprecated(reason: "Use User fragment on Viewer instead")
  personalOrganization: Organization @deprecated(reason: "Use User fragment on Viewer instead")
  stripeVerificationStatus: String @deprecated(reason: "Use User fragment on Viewer instead")
  trust: OrganizationTrust!
  trustScore: Int @deprecated(reason: "Use User fragment on Viewer instead")
  twoFactorProtection: Boolean @deprecated(reason: "Use User fragment on Viewer instead")
  username: String @deprecated(reason: "Use User fragment on Viewer instead")
}

type Machine implements Node {
  app: App!
  config: JSON!
  createdAt: ISO8601DateTime!
  egressIpAddresses(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): EgressIPAddressConnection!
  events(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int
    kind: String

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): MachineEventConnection!
  host: Host!
  id: ID!
  instanceId: String!
  ips(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): MachineIPConnection!
  name: String!
  region: String!
  state: String!
  updatedAt: ISO8601DateTime!
}

"""
The connection type for Machine.
"""
type MachineConnection {
  """
  A list of edges.
  """
  edges: [MachineEdge]

  """
  A list of nodes.
  """
  nodes: [Machine]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

"""
An edge in a connection.
"""
type MachineEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: Machine
}

"""
A machine state change event
"""
interface MachineEvent {
  id: ID!
  kind: String!
  timestamp: ISO8601DateTime!
}

"""
The connection type for MachineEvent.
"""
type MachineEventConnection {
  """
  A list of edges.
  """
  edges: [MachineEventEdge]

  """
  A list of nodes.
  """
  nodes: [MachineEvent]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
}

type MachineEventDestroy implements MachineEvent {
  id: ID!
  kind: String!
  timestamp: ISO8601DateTime!
}

"""
An edge in a connection.
"""
type MachineEventEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: MachineEvent
}

type MachineEventExit implements MachineEvent {
  exitCode: Int!
  id: ID!
  kind: String!
  metadata: JSON!
  oomKilled: Boolean!
  requestedStop: Boolean!
  timestamp: ISO8601DateTime!
}

type MachineEventGeneric implements MachineEvent {
  id: ID!
  kind: String!
  timestamp: ISO8601DateTime!
}

type MachineEventStart implements MachineEvent {
  id: ID!
  kind: String!
  timestamp: ISO8601DateTime!
}

type MachineIP implements Node {
  family: String!

  """
  ID of the object.
  """
  id: ID!
  ip: String!
  kind: String!
  maskSize: Int!
}

"""
The connection type for MachineIP.
"""
type MachineIPConnection {
  """
  A list of edges.
  """
  edges: [MachineIPEdge]

  """
  A list of nodes.
  """
  nodes: [MachineIP]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

"""
An edge in a connection.
"""
type MachineIPEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: MachineIP
}

"""
Autogenerated input type of MigrateVolume
"""
input MigrateVolumeInput {
  """
  The ID of the application to attach the new volume to
  """
  appId: Int!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  Provision volume in a redundancy zone not already in use by this app
  """
  requireUniqueZone: Boolean

  """
  The target Volume ID to be migrated
  """
  targetVolumeId: String!

  """
  Use the latest snapshot instead of creating a new one.
  """
  useLatestSnapshot: Boolean
}

"""
Autogenerated return type of MigrateVolume.
"""
type MigrateVolumePayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  hostId: Int!
  hostname: String!
  volumeGraphId: String!
  volumeId: Int!
}

"""
Autogenerated input type of MoveApp
"""
input MoveAppInput {
  """
  The application to move
  """
  appId: ID!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The node ID of the organization to move the app to
  """
  organizationId: ID!
}

"""
Autogenerated return type of MoveApp.
"""
type MoveAppPayload {
  app: App!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

type Mutations {
  addCertificate(
    """
    The application to attach the new hostname to
    """
    appId: ID!

    """
    Certificate's hostname
    """
    hostname: String!
  ): AddCertificatePayload
  addDevrelCoupon(
    """
    Parameters for AddDevrelCoupon
    """
    input: AddDevrelCouponInput!
  ): AddDevrelCouponPayload
  addStripePaymentMethod(
    """
    Parameters for AddStripePaymentMethod
    """
    input: AddStripePaymentMethodInput!
  ): AddStripePaymentMethodPayload
  addWireGuardPeer(
    """
    Parameters for AddWireGuardPeer
    """
    input: AddWireGuardPeerInput!
  ): AddWireGuardPeerPayload
  allocateEgressIpAddress(
    """
    Parameters for AllocateEgressIPAddress
    """
    input: AllocateEgressIPAddressInput!
  ): AllocateEgressIPAddressPayload
  allocateIpAddress(
    """
    Parameters for AllocateIPAddress
    """
    input: AllocateIPAddressInput!
  ): AllocateIPAddressPayload
  attachPostgresCluster(
    """
    Parameters for AttachPostgresCluster
    """
    input: AttachPostgresClusterInput!
  ): AttachPostgresClusterPayload
  buildMachine(
    """
    Parameters for BuildMachine
    """
    input: BuildMachineInput!
  ): BuildMachinePayload
  buildVolume(
    """
    Parameters for BuildVolume
    """
    input: BuildVolumeInput!
  ): BuildVolumePayload
  cancelBuild(
    """
    The node ID of the build
    """
    buildId: ID!
  ): CancelBuildPayload
  changeOrganizationPlan(
    """
    Parameters for ChangeOrganizationPlan
    """
    input: ChangeOrganizationPlanInput!
  ): ChangeOrganizationPlanPayload
  checkCertificate(
    """
    Parameters for CheckCertificate
    """
    input: CheckCertificateInput!
  ): CheckCertificatePayload
  checkDomain(
    """
    Parameters for CheckDomain
    """
    input: CheckDomainInput!
  ): CheckDomainPayload
  configureRegions(
    """
    Parameters for ConfigureRegions
    """
    input: ConfigureRegionsInput!
  ): ConfigureRegionsPayload
  confirmCliSession(
    """
    Parameters for ConfirmCliSession
    """
    input: ConfirmCliSessionInput!
  ): ConfirmCliSessionPayload
  createAccessToken(
    """
    Parameters for CreateAccessToken
    """
    input: CreateAccessTokenInput!
  ): CreateAccessTokenPayload
  createAddOn(
    """
    Parameters for CreateAddOn
    """
    input: CreateAddOnInput!
  ): CreateAddOnPayload
  createAndRegisterDomain(
    """
    Parameters for CreateAndRegisterDomain
    """
    input: CreateAndRegisterDomainInput!
  ): CreateAndRegisterDomainPayload
  createAndTransferDomain(
    """
    Parameters for CreateAndTransferDomain
    """
    input: CreateAndTransferDomainInput!
  ): CreateAndTransferDomainPayload
  createApp(
    """
    Parameters for CreateApp
    """
    input: CreateAppInput!
  ): CreateAppPayload
  createBuild(
    """
    Parameters for CreateBuild
    """
    input: CreateBuildInput!
  ): CreateBuildPayload
  createCheckJob(
    """
    Parameters for CreateCheckJob
    """
    input: CreateCheckJobInput!
  ): CreateCheckJobPayload
  createCheckJobRun(
    """
    Parameters for CreateCheckJobRun
    """
    input: CreateCheckJobRunInput!
  ): CreateCheckJobRunPayload
  createDelegatedWireGuardToken(
    """
    Parameters for CreateDelegatedWireGuardToken
    """
    input: CreateDelegatedWireGuardTokenInput!
  ): CreateDelegatedWireGuardTokenPayload
  createDnsPortal(
    """
    Parameters for CreateDNSPortal
    """
    input: CreateDNSPortalInput!
  ): CreateDNSPortalPayload
  createDnsPortalSession(
    """
    Parameters for CreateDNSPortalSession
    """
    input: CreateDNSPortalSessionInput!
  ): CreateDNSPortalSessionPayload
  createDnsRecord(
    """
    Parameters for CreateDNSRecord
    """
    input: CreateDNSRecordInput!
  ): CreateDNSRecordPayload
  createDoctorReport(
    """
    Parameters for CreateDoctorReport
    """
    input: CreateDoctorReportInput!
  ): CreateDoctorReportPayload
  createDoctorUrl: CreateDoctorUrlPayload
  createDomain(
    """
    Parameters for CreateDomain
    """
    input: CreateDomainInput!
  ): CreateDomainPayload
  createExtensionTosAgreement(
    """
    Parameters for CreateExtensionTosAgreement
    """
    input: CreateExtensionTosAgreementInput!
  ): CreateExtensionTosAgreementPayload
  createLimitedAccessToken(
    """
    Parameters for CreateLimitedAccessToken
    """
    input: CreateLimitedAccessTokenInput!
  ): CreateLimitedAccessTokenPayload
  createMachineApp(
    """
    Parameters for CreateMachineApp
    """
    input: CreateMachineAppInput!
  ): CreateMachineAppPayload
  createOrganization(
    """
    Parameters for CreateOrganization
    """
    input: CreateOrganizationInput!
  ): CreateOrganizationPayload
  createOrganizationInvitation(
    """
    Parameters for CreateOrganizationInvitation
    """
    input: CreateOrganizationInvitationInput!
  ): CreateOrganizationInvitationPayload
  createPasswordReset(
    """
    Parameters for CreatePasswordReset
    """
    input: CreatePasswordResetInput!
  ): CreatePasswordResetPayload
  createPostgresClusterDatabase(
    """
    Parameters for CreatePostgresClusterDatabase
    """
    input: CreatePostgresClusterDatabaseInput!
  ): CreatePostgresClusterDatabasePayload
  createPostgresClusterUser(
    """
    Parameters for CreatePostgresClusterUser
    """
    input: CreatePostgresClusterUserInput!
  ): CreatePostgresClusterUserPayload
  createRelease(
    """
    Parameters for CreateRelease
    """
    input: CreateReleaseInput!
  ): CreateReleasePayload
  createStripeVerificationSession(
    """
    Parameters for CreateStripeVerificationSession
    """
    input: CreateStripeVerificationSessionInput!
  ): CreateStripeVerificationSessionPayload
  createTemplateDeployment(
    """
    Parameters for CreateTemplateDeployment
    """
    input: CreateTemplateDeploymentInput!
  ): CreateTemplateDeploymentPayload
  createThirdPartyConfiguration(
    """
    Parameters for CreateThirdPartyConfiguration
    """
    input: CreateThirdPartyConfigurationInput!
  ): CreateThirdPartyConfigurationPayload
  createUserSignup(
    """
    Parameters for CreateUserSignup
    """
    input: CreateUserSignupInput!
  ): CreateUserSignupPayload
  createVolume(
    """
    Parameters for CreateVolume
    """
    input: CreateVolumeInput!
  ): CreateVolumePayload
  createVolumeSnapshot(
    """
    Parameters for CreateVolumeSnapshot
    """
    input: CreateVolumeSnapshotInput!
  ): CreateVolumeSnapshotPayload
  deleteAccessToken(
    """
    Parameters for DeleteAccessToken
    """
    input: DeleteAccessTokenInput!
  ): DeleteAccessTokenPayload
  deleteAddOn(
    """
    Parameters for DeleteAddOn
    """
    input: DeleteAddOnInput!
  ): DeleteAddOnPayload

  """
  Delete an app
  """
  deleteApp(
    """
    The application to delete
    """
    appId: ID!
  ): DeleteAppPayload
  deleteCertificate(
    """
    Application to remove hostname from
    """
    appId: ID!

    """
    Certificate hostname to delete
    """
    hostname: String!
  ): DeleteCertificatePayload
  deleteDelegatedWireGuardToken(
    """
    Parameters for DeleteDelegatedWireGuardToken
    """
    input: DeleteDelegatedWireGuardTokenInput!
  ): DeleteDelegatedWireGuardTokenPayload
  deleteDeploymentSource(
    """
    Parameters for DeleteDeploymentSource
    """
    input: DeleteDeploymentSourceInput!
  ): DeleteDeploymentSourcePayload
  deleteDnsPortal(
    """
    Parameters for DeleteDNSPortal
    """
    input: DeleteDNSPortalInput!
  ): DeleteDNSPortalPayload
  deleteDnsPortalSession(
    """
    Parameters for DeleteDNSPortalSession
    """
    input: DeleteDNSPortalSessionInput!
  ): DeleteDNSPortalSessionPayload
  deleteDnsRecord(
    """
    Parameters for DeleteDNSRecord
    """
    input: DeleteDNSRecordInput!
  ): DeleteDNSRecordPayload
  deleteDomain(
    """
    Parameters for DeleteDomain
    """
    input: DeleteDomainInput!
  ): DeleteDomainPayload
  deleteHealthCheckHandler(
    """
    Parameters for DeleteHealthCheckHandler
    """
    input: DeleteHealthCheckHandlerInput!
  ): DeleteHealthCheckHandlerPayload
  deleteIdentity(
    """
    Parameters for DeleteIdentity
    """
    input: DeleteIdentityInput!
  ): DeleteIdentityPayload
  deleteLimitedAccessToken(
    """
    Parameters for DeleteLimitedAccessToken
    """
    input: DeleteLimitedAccessTokenInput!
  ): DeleteLimitedAccessTokenPayload
  deleteOrganization(
    """
    Parameters for DeleteOrganization
    """
    input: DeleteOrganizationInput!
  ): DeleteOrganizationPayload
  deleteOrganizationInvitation(
    """
    Parameters for DeleteOrganizationInvitation
    """
    input: DeleteOrganizationInvitationInput!
  ): DeleteOrganizationInvitationPayload
  deleteOrganizationMembership(
    """
    Parameters for DeleteOrganizationMembership
    """
    input: DeleteOrganizationMembershipInput!
  ): DeleteOrganizationMembershipPayload
  deleteRemoteBuilder(
    """
    Parameters for DeleteRemoteBuilder
    """
    input: DeleteRemoteBuilderInput!
  ): DeleteRemoteBuilderPayload
  deleteThirdPartyConfiguration(
    """
    Parameters for DeleteThirdPartyConfiguration
    """
    input: DeleteThirdPartyConfigurationInput!
  ): DeleteThirdPartyConfigurationPayload
  deleteUser(
    """
    Parameters for DeleteUser
    """
    input: DeleteUserInput!
  ): DeleteUserPayload
  deleteVolume(
    """
    Parameters for DeleteVolume
    """
    input: DeleteVolumeInput!
  ): DeleteVolumePayload
  deployImage(
    """
    Parameters for DeployImage
    """
    input: DeployImageInput!
  ): DeployImagePayload
  destroyIdentities(
    """
    Parameters for DestroyIdentities
    """
    input: DestroyIdentitiesInput!
  ): DestroyIdentitiesPayload
  detachPostgresCluster(
    """
    Parameters for DetachPostgresCluster
    """
    input: DetachPostgresClusterInput!
  ): DetachPostgresClusterPayload
  disableOneTimePassword(
    """
    Parameters for DisableOneTimePassword
    """
    input: DisableOneTimePasswordInput!
  ): DisableOneTimePasswordPayload
  dischargeRootToken(
    """
    Parameters for DischargeRootToken
    """
    input: DischargeRootTokenInput!
  ): DischargeRootTokenPayload
  dummyWireGuardPeer(
    """
    Parameters for DummyWireGuardPeer
    """
    input: DummyWireGuardPeerInput!
  ): DummyWireGuardPeerPayload
  enableDedicatedVms(
    """
    Parameters for EnableDedicatedVms
    """
    input: EnableDedicatedVmsInput!
  ): EnableDedicatedVmsPayload
  enableOneTimePassword(
    """
    Parameters for EnableOneTimePassword
    """
    input: EnableOneTimePasswordInput!
  ): EnableOneTimePasswordPayload
  enablePostgresConsul(
    """
    Parameters for EnablePostgresConsul
    """
    input: EnablePostgresConsulInput!
  ): EnablePostgresConsulPayload
  ensureDepotRemoteBuilder(
    """
    Parameters for EnsureDepotRemoteBuilder
    """
    input: EnsureDepotRemoteBuilderInput!
  ): EnsureDepotRemoteBuilderPayload
  ensureFlyctlMachineHost(
    """
    Parameters for EnsureFlyctlMachineHost
    """
    input: EnsureFlyctlMachineHostInput!
  ): EnsureFlyctlMachineHostPayload
  ensureMachineRemoteBuilder(
    """
    Parameters for EnsureMachineRemoteBuilder
    """
    input: EnsureMachineRemoteBuilderInput!
  ): EnsureMachineRemoteBuilderPayload
  ensureOrganizationStripeContainerSubscription(
    """
    Parameters for EnsureOrganizationStripeContainerSubscription
    """
    input: EnsureOrganizationStripeContainerSubscriptionInput!
  ): EnsureOrganizationStripeContainerSubscriptionPayload
  establishSshKey(
    """
    Parameters for EstablishSSHKey
    """
    input: EstablishSSHKeyInput!
  ): EstablishSSHKeyPayload
  exportDnsZone(
    """
    Parameters for ExportDNSZone
    """
    input: ExportDNSZoneInput!
  ): ExportDNSZonePayload
  extendVolume(
    """
    Parameters for ExtendVolume
    """
    input: ExtendVolumeInput!
  ): ExtendVolumePayload
  finishBuild(
    """
    Parameters for FinishBuild
    """
    input: FinishBuildInput!
  ): FinishBuildPayload
  forkVolume(
    """
    Parameters for ForkVolume
    """
    input: ForkVolumeInput!
  ): ForkVolumePayload
  generateTwoFactorRecoveryCodes(
    """
    Parameters for GenerateTwoFactorRecoveryCodes
    """
    input: GenerateTwoFactorRecoveryCodesInput!
  ): GenerateTwoFactorRecoveryCodesPayload
  grantPostgresClusterUserAccess(
    """
    Parameters for GrantPostgresClusterUserAccess
    """
    input: GrantPostgresClusterUserAccessInput!
  ): GrantPostgresClusterUserAccessPayload
  importCertificate(
    """
    The application to attach the new hostname to
    """
    appId: ID!

    """
    Full chain for certificate
    """
    fullchain: String!

    """
    Hostname for certificate (certificate Common Name by default)
    """
    hostname: String

    """
    Private signing key for certificate
    """
    privateKey: String!
  ): ImportCertificatePayload
  importDnsZone(
    """
    Parameters for ImportDNSZone
    """
    input: ImportDNSZoneInput!
  ): ImportDNSZonePayload
  issueCertificate(
    """
    Parameters for IssueCertificate
    """
    input: IssueCertificateInput!
  ): IssueCertificatePayload
  killMachine(
    """
    Parameters for KillMachine
    """
    input: KillMachineInput!
  ): KillMachinePayload
  launchMachine(
    """
    Parameters for LaunchMachine
    """
    input: LaunchMachineInput!
  ): LaunchMachinePayload
  lockApp(
    """
    Parameters for LockApp
    """
    input: LockAppInput!
  ): LockAppPayload
  logOut(
    """
    Parameters for LogOut
    """
    input: LogOutInput!
  ): LogOutPayload
  migrateVolume(
    """
    Parameters for MigrateVolume
    """
    input: MigrateVolumeInput!
  ): MigrateVolumePayload
  moveApp(
    """
    Parameters for MoveApp
    """
    input: MoveAppInput!
  ): MoveAppPayload
  nomadToMachinesMigration(
    """
    Parameters for NomadToMachinesMigration
    """
    input: NomadToMachinesMigrationInput!
  ): NomadToMachinesMigrationPayload
  nomadToMachinesMigrationPrep(
    """
    Parameters for NomadToMachinesMigrationPrep
    """
    input: NomadToMachinesMigrationPrepInput!
  ): NomadToMachinesMigrationPrepPayload
  pauseApp(
    """
    Parameters for PauseApp
    """
    input: PauseAppInput!
  ): PauseAppPayload
  prepareUserForHiring(
    """
    Parameters for PrepareUserForHiring
    """
    input: PrepareUserForHiringInput!
  ): PrepareUserForHiringPayload
  redeemOrganizationInvitation(
    """
    Parameters for RedeemOrganizationInvitation
    """
    input: RedeemOrganizationInvitationInput!
  ): RedeemOrganizationInvitationPayload
  registerDomain(
    """
    Parameters for RegisterDomain
    """
    input: RegisterDomainInput!
  ): RegisterDomainPayload
  registerMachine(
    """
    Parameters for RegisterMachine
    """
    input: RegisterMachineInput!
  ): RegisterMachinePayload
  registerVolume(
    """
    Parameters for RegisterVolume
    """
    input: RegisterVolumeInput!
  ): RegisterVolumePayload
  releaseEgressIpAddress(
    """
    Parameters for ReleaseEgressIPAddress
    """
    input: ReleaseEgressIPAddressInput!
  ): ReleaseEgressIPAddressPayload
  releaseIpAddress(
    """
    Parameters for ReleaseIPAddress
    """
    input: ReleaseIPAddressInput!
  ): ReleaseIPAddressPayload
  removeMachine(
    """
    Parameters for RemoveMachine
    """
    input: RemoveMachineInput!
  ): RemoveMachinePayload
  removeWireGuardPeer(
    """
    Parameters for RemoveWireGuardPeer
    """
    input: RemoveWireGuardPeerInput!
  ): RemoveWireGuardPeerPayload
  requestSignedDocument(
    """
    Parameters for RequestSignedDocument
    """
    input: RequestSignedDocumentInput!
  ): RequestSignedDocumentPayload
  resetAddOnPassword(
    """
    Parameters for ResetAddOnPassword
    """
    input: ResetAddOnPasswordInput!
  ): ResetAddOnPasswordPayload
  resolvePasswordReset(
    """
    Parameters for ResolvePasswordReset
    """
    input: ResolvePasswordResetInput!
  ): ResolvePasswordResetPayload
  restartAllocation(
    """
    Parameters for RestartAllocation
    """
    input: RestartAllocationInput!
  ): RestartAllocationPayload
  restartApp(
    """
    Parameters for RestartApp
    """
    input: RestartAppInput!
  ): RestartAppPayload
  restoreVolumeSnapshot(
    """
    Parameters for RestoreVolumeSnapshot
    """
    input: RestoreVolumeSnapshotInput!
  ): RestoreVolumeSnapshotPayload
  resumeApp(
    """
    Parameters for ResumeApp
    """
    input: ResumeAppInput!
  ): ResumeAppPayload
  revokePostgresClusterUserAccess(
    """
    Parameters for RevokePostgresClusterUserAccess
    """
    input: RevokePostgresClusterUserAccessInput!
  ): RevokePostgresClusterUserAccessPayload
  saveDeploymentSource(
    """
    Parameters for SaveDeploymentSource
    """
    input: SaveDeploymentSourceInput!
  ): SaveDeploymentSourcePayload
  scaleApp(
    """
    Parameters for ScaleApp
    """
    input: ScaleAppInput!
  ): ScaleAppPayload
  sendVerificationEmail(
    """
    Parameters for SendVerificationEmail
    """
    input: SendVerificationEmailInput!
  ): SendVerificationEmailPayload
  setAppsV2DefaultOn(
    """
    Parameters for SetAppsv2DefaultOn
    """
    input: SetAppsv2DefaultOnInput!
  ): SetAppsv2DefaultOnPayload
  setPagerdutyHandler(
    """
    Parameters for SetPagerdutyHandler
    """
    input: SetPagerdutyHandlerInput!
  ): SetPagerdutyHandlerPayload
  setPlatformVersion(
    """
    Parameters for SetPlatformVersion
    """
    input: SetPlatformVersionInput!
  ): SetPlatformVersionPayload
  setSecrets(
    """
    Parameters for SetSecrets
    """
    input: SetSecretsInput!
  ): SetSecretsPayload
  setSlackHandler(
    """
    Parameters for SetSlackHandler
    """
    input: SetSlackHandlerInput!
  ): SetSlackHandlerPayload
  setVmCount(
    """
    Parameters for SetVMCount
    """
    input: SetVMCountInput!
  ): SetVMCountPayload
  setVmSize(
    """
    Parameters for SetVMSize
    """
    input: SetVMSizeInput!
  ): SetVMSizePayload
  startBuild(
    """
    Parameters for StartBuild
    """
    input: StartBuildInput!
  ): StartBuildPayload
  startMachine(
    """
    Parameters for StartMachine
    """
    input: StartMachineInput!
  ): StartMachinePayload
  stopAllocation(
    """
    Parameters for StopAllocation
    """
    input: StopAllocationInput!
  ): StopAllocationPayload
  stopMachine(
    """
    Parameters for StopMachine
    """
    input: StopMachineInput!
  ): StopMachinePayload
  stopPlanDowngrade(
    """
    Parameters for StopPlanDowngrade
    """
    input: StopPlanDowngradeInput!
  ): StopPlanDowngradePayload
  syncTokensForOrg(
    """
    Parameters for SyncTokensForOrg
    """
    input: SyncTokensForOrgInput!
  ): SyncTokensForOrgPayload
  unlockApp(
    """
    Parameters for UnlockApp
    """
    input: UnlockAppInput!
  ): UnlockAppPayload
  unsetSecrets(
    """
    Parameters for UnsetSecrets
    """
    input: UnsetSecretsInput!
  ): UnsetSecretsPayload
  updateAddOn(
    """
    Parameters for UpdateAddOn
    """
    input: UpdateAddOnInput!
  ): UpdateAddOnPayload
  updateAutoscaleConfig(
    """
    Parameters for UpdateAutoscaleConfig
    """
    input: UpdateAutoscaleConfigInput!
  ): UpdateAutoscaleConfigPayload
  updateDnsPortal(
    """
    Parameters for UpdateDNSPortal
    """
    input: UpdateDNSPortalInput!
  ): UpdateDNSPortalPayload
  updateDnsRecord(
    """
    Parameters for UpdateDNSRecord
    """
    input: UpdateDNSRecordInput!
  ): UpdateDNSRecordPayload
  updateDnsRecords(
    """
    Parameters for UpdateDNSRecords
    """
    input: UpdateDNSRecordsInput!
  ): UpdateDNSRecordsPayload
  updateOrganizationMembership(
    """
    Parameters for UpdateOrganizationMembership
    """
    input: UpdateOrganizationMembershipInput!
  ): UpdateOrganizationMembershipPayload
  updateRelease(
    """
    Parameters for UpdateRelease
    """
    input: UpdateReleaseInput!
  ): UpdateReleasePayload
  updateRemoteBuilder(
    """
    Parameters for UpdateRemoteBuilder
    """
    input: UpdateRemoteBuilderInput!
  ): UpdateRemoteBuilderPayload
  updateRiskFromStripe(
    """
    Parameters for UpdateRiskFromStripe
    """
    input: UpdateRiskFromStripeInput!
  ): UpdateRiskFromStripePayload
  updateThirdPartyConfiguration(
    """
    Parameters for UpdateThirdPartyConfiguration
    """
    input: UpdateThirdPartyConfigurationInput!
  ): UpdateThirdPartyConfigurationPayload
  updateUserCoupon(
    """
    Parameters for UpdateUserCoupon
    """
    input: UpdateUserCouponInput!
  ): UpdateUserCouponPayload
  updateUserPassword(
    """
    Parameters for UpdateUserPassword
    """
    input: UpdateUserPasswordInput!
  ): UpdateUserPasswordPayload
  updateUserProfile(
    """
    Parameters for UpdateUserProfile
    """
    input: UpdateUserProfileInput!
  ): UpdateUserProfilePayload
  updateVolume(
    """
    Parameters for UpdateVolume
    """
    input: UpdateVolumeInput!
  ): UpdateVolumePayload
  validateWireGuardPeers(
    """
    Parameters for ValidateWireGuardPeers
    """
    input: ValidateWireGuardPeersInput!
  ): ValidateWireGuardPeersPayload
  verifyEmail(
    """
    Parameters for VerifyEmail
    """
    input: VerifyEmailInput!
  ): VerifyEmailPayload
  verifyUserPassword(
    """
    Parameters for VerifyUserPassword
    """
    input: VerifyUserPasswordInput!
  ): VerifyUserPasswordPayload
}

type Network implements Node {
  createdAt: ISO8601DateTime!

  """
  ID of the object.
  """
  id: ID!
  name: String!
  organization: Organization!
  updatedAt: ISO8601DateTime!
}

"""
An object with an ID.
"""
interface Node {
  """
  ID of the object.
  """
  id: ID!
}

"""
Autogenerated input type of NomadToMachinesMigration
"""
input NomadToMachinesMigrationInput {
  """
  The application to move
  """
  appId: ID!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated return type of NomadToMachinesMigration.
"""
type NomadToMachinesMigrationPayload {
  app: App!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated input type of NomadToMachinesMigrationPrep
"""
input NomadToMachinesMigrationPrepInput {
  """
  The application to move
  """
  appId: ID!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated return type of NomadToMachinesMigrationPrep.
"""
type NomadToMachinesMigrationPrepPayload {
  app: App!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

type Onboarding {
  isFlyctlInstalled: Boolean!
}

type Organization implements Node {
  activeDiscountName: String

  """
  Single sign-on link for the given integration type
  """
  addOnSsoLink: String

  """
  List third party integrations associated with an organization
  """
  addOns(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
    type: AddOnType
  ): AddOnConnection!

  """
  Check if the organization has agreed to the extension provider terms of service
  """
  agreedToProviderTos(providerName: String!): Boolean!
  apps(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): AppConnection!
  billable: Boolean!
  billingStatus: BillingStatus!

  """
  The account credits in cents
  """
  creditBalance: Int!

  """
  The formatted account credits
  """
  creditBalanceFormatted: String! @deprecated(reason: "Use credit_balance instead")
  delegatedWireGuardTokens(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): DelegatedWireGuardTokenConnection!

  """
  Find a dns portal by name
  """
  dnsPortal(name: String!): DNSPortal!
  dnsPortals(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): DNSPortalConnection!

  """
  Find a domain by name
  """
  domain(name: String!): Domain
  domains(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): DomainConnection!

  """
  Single sign-on link for the given extension type
  """
  extensionSsoLink(provider: String!): String
  healthCheckHandlers(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): HealthCheckHandlerConnection!
  healthChecks(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): HealthCheckConnection!
  highRisk: Boolean!
  id: ID!
  internalNumericId: BigInt!
  invitations(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): OrganizationInvitationConnection!
  isCreditCardSaved: Boolean!
  limitedAccessTokens(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): LimitedAccessTokenConnection!
  loggedCertificates(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): LoggedCertificateConnection
  members(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): OrganizationMembershipsConnection!

  """
  Organization name
  """
  name: String!
  paidPlan: Boolean!

  """
  Email for paid plan support if available
  """
  paidSupportEmail: String

  """
  Whether the organization can provision beta extensions
  """
  provisionsBetaExtensions: Boolean!

  """
  Unmodified unique org slug
  """
  rawSlug: String!
  remoteBuilderApp: App
  remoteBuilderImage: String!
  settings: JSON
  signedDocuments(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): SignedDocumentConnection!

  """
  Unique organization slug
  """
  slug: String!
  sshCertificate: String
  stripeContainerPlanId: String
  stripeContainerSubscriptionId: String
  stripeCustomerId: String
  stripeSubscriptionId: String

  """
  Configurations for third-party caveats to be issued on user macaroons
  """
  thirdPartyConfigurations(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): ThirdPartyConfigurationConnection!
  trialEndedAt: ISO8601DateTime
  trialStartedAt: ISO8601DateTime
  trust: OrganizationTrust!

  """
  The type of organization
  """
  type: OrganizationType!

  """
  The current user's role in the org
  """
  viewerRole: String!

  """
  Find a peer by name
  """
  wireGuardPeer(name: String!): WireGuardPeer!
  wireGuardPeers(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): WireGuardPeerConnection!
}

enum OrganizationAlertsEnabled {
  """
  The user has alerts enabled
  """
  ENABLED

  """
  The user does not have alerts enabled
  """
  NOT_ENABLED
}

"""
The connection type for Organization.
"""
type OrganizationConnection {
  """
  A list of edges.
  """
  edges: [OrganizationEdge]

  """
  A list of nodes.
  """
  nodes: [Organization]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

"""
An edge in a connection.
"""
type OrganizationEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: Organization
}

type OrganizationInvitation implements Node {
  createdAt: ISO8601DateTime!
  email: String!
  id: ID!

  """
  The user who created the invitation
  """
  inviter: User!
  organization: Organization!
  redeemed: Boolean!
  redeemedAt: ISO8601DateTime
}

"""
The connection type for OrganizationInvitation.
"""
type OrganizationInvitationConnection {
  """
  A list of edges.
  """
  edges: [OrganizationInvitationEdge]

  """
  A list of nodes.
  """
  nodes: [OrganizationInvitation]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

"""
An edge in a connection.
"""
type OrganizationInvitationEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: OrganizationInvitation
}

enum OrganizationMemberRole {
  """
  The user is an administrator of the organization
  """
  ADMIN

  """
  The user is a member of the organization
  """
  MEMBER
}

"""
The connection type for User.
"""
type OrganizationMembershipsConnection {
  """
  A list of edges.
  """
  edges: [OrganizationMembershipsEdge]

  """
  A list of nodes.
  """
  nodes: [User]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

"""
An edge in a connection.
"""
type OrganizationMembershipsEdge {
  """
  The alerts settings the user has in this organization
  """
  alertsEnabled: OrganizationAlertsEnabled!

  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The date the user joined the organization
  """
  joinedAt: ISO8601DateTime!

  """
  The item at the end of the edge.
  """
  node: User

  """
  The role the user has in this organization
  """
  role: OrganizationMemberRole!
}

enum OrganizationTrust {
  """
  Organization cannot use our services
  """
  BANNED

  """
  Organization proved that it's safe to use our services
  """
  HIGH

  """
  Organization has to prove that is not fraud over time but can use our services
  """
  LOW

  """
  Organization has limited access to our service
  """
  RESTRICTED

  """
  We haven't set a trust level yet
  """
  UNKNOWN
}

enum OrganizationType {
  """
  A user's personal organization
  """
  PERSONAL

  """
  An organization shared between one or more users
  """
  SHARED
}

"""
Information about pagination in a connection.
"""
type PageInfo {
  """
  When paginating forwards, the cursor to continue.
  """
  endCursor: String

  """
  When paginating forwards, are there more items?
  """
  hasNextPage: Boolean!

  """
  When paginating backwards, are there more items?
  """
  hasPreviousPage: Boolean!

  """
  When paginating backwards, the cursor to continue.
  """
  startCursor: String
}

"""
Autogenerated input type of PauseApp
"""
input PauseAppInput {
  """
  The ID of the app
  """
  appId: ID!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated return type of PauseApp.
"""
type PauseAppPayload {
  app: App!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

enum PlatformVersionEnum {
  """
  App in migration between nomad and machines
  """
  detached

  """
  App with only machines
  """
  machines

  """
  Nomad managed application
  """
  nomad
}

type PostgresClusterAppRole implements AppRole {
  databases: [PostgresClusterDatabase!]!

  """
  The name of this role
  """
  name: String!
  users: [PostgresClusterUser!]!
}

type PostgresClusterAttachment implements Node {
  databaseName: String!
  databaseUser: String!
  environmentVariableName: String!
  id: ID!
}

"""
The connection type for PostgresClusterAttachment.
"""
type PostgresClusterAttachmentConnection {
  """
  A list of edges.
  """
  edges: [PostgresClusterAttachmentEdge]

  """
  A list of nodes.
  """
  nodes: [PostgresClusterAttachment]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

"""
An edge in a connection.
"""
type PostgresClusterAttachmentEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: PostgresClusterAttachment
}

type PostgresClusterDatabase {
  name: String!
  users: [String!]!
}

type PostgresClusterUser {
  databases: [String!]!
  isSuperuser: Boolean!
  username: String!
}

"""
Autogenerated input type of PrepareUserForHiring
"""
input PrepareUserForHiringInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated return type of PrepareUserForHiring.
"""
type PrepareUserForHiringPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  success: Boolean
}

type PriceTier {
  unitAmount: String
  upTo: BigInt
}

interface Principal {
  """
  URL for avatar or placeholder
  """
  avatarUrl: String!
  blankPassword: Boolean @deprecated(reason: "Use User fragment on Viewer instead")
  createdAt: ISO8601DateTime @deprecated(reason: "Use User fragment on Viewer instead")

  """
  Email address for principal
  """
  email: String!
  emailVerified: Boolean @deprecated(reason: "Use User fragment on Viewer instead")
  featureFlags: [String!] @deprecated(reason: "Use User fragment on Viewer instead")
  hasNodeproxyApps: Boolean @deprecated(reason: "Use User fragment on Viewer instead")
  hasRecoveryCodes: Boolean @deprecated(reason: "Use User fragment on Viewer instead")
  highRisk: Boolean @deprecated(reason: "Use User fragment on Viewer instead")
  id: ID @deprecated(reason: "Use User fragment on Viewer instead")
  identities: [Identity!] @deprecated(reason: "Use User fragment on Viewer instead")

  """
  Find an identity by provider
  """
  identity(provider: String!): Identity @deprecated(reason: "Use User fragment on Viewer instead")
  lastRegion: String @deprecated(reason: "Use User fragment on Viewer instead")

  """
  Display name of principal
  """
  name: String
  organizations(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): OrganizationConnection @deprecated(reason: "Use User fragment on Viewer instead")
  personalOrganization: Organization @deprecated(reason: "Use User fragment on Viewer instead")
  stripeVerificationStatus: String @deprecated(reason: "Use User fragment on Viewer instead")
  trust: OrganizationTrust!
  trustScore: Int @deprecated(reason: "Use User fragment on Viewer instead")
  twoFactorProtection: Boolean @deprecated(reason: "Use User fragment on Viewer instead")
  username: String @deprecated(reason: "Use User fragment on Viewer instead")
}

type ProcessGroup {
  maxPerRegion: Int!
  name: String!
  regions: [String!]!
  vmSize: VMSize!
}

type Product {
  name: String!
  tiers: [PriceTier!]!
  type: String!
  unitLabel: String
}

input PropertyInput {
  """
  The name of the property
  """
  name: String!

  """
  The value of the property
  """
  value: String
}

type Queries {
  accessTokens(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
    type: AccessTokenType = pat
  ): AccessTokenConnection!

  """
  Find an add-on by ID or name
  """
  addOn(id: ID, name: String, provider: String): AddOn

  """
  List add-on service plans
  """
  addOnPlans(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
    type: AddOnType
  ): AddOnPlanConnection!
  addOnProvider(name: String!): AddOnProvider!

  """
  List add-ons associated with an organization
  """
  addOns(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
    type: AddOnType
  ): AddOnConnection!

  """
  Find an app by name
  """
  app(id: String @deprecated(reason: "will be removed"), internalId: String, key: String @deprecated(reason: "will be removed"), name: String): App

  """
  Validates an app name for app creation
  """
  appNameAvailable(
    """
    The app name to be validated
    """
    name: String!
  ): Boolean!

  """
  List apps
  """
  apps(
    active: Boolean

    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int
    ids: [String!] @deprecated(reason: "will be removed")

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
    organizationId: ID
    platform: String
    role: String
    type: String @deprecated(reason: "will be removed")
  ): AppConnection!

  """
  Verifies if an app can undergo a bluegreen deployment
  """
  canPerformBluegreenDeployment(
    """
    The name of the app
    """
    name: String!
  ): Boolean!

  """
  Find a certificate by ID
  """
  certificate(id: ID!): AppCertificate
  checkJobs(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): CheckJobConnection!
  checkLocations: [CheckLocation!]!

  """
  Show CLI session
  """
  cliSession(id: String): CliSession
  currentUser: User! @deprecated(reason: "use viewer instead")

  """
  Find a domain by name
  """
  domain(name: String!): Domain

  """
  internal field for inter-service authz check
  """
  flapsAuthzCheck: Boolean!
  githubIntegration: GithubIntegration! @deprecated(reason: "deprecated")
  herokuIntegration: HerokuIntegration!

  """
  Find an ip address by ID
  """
  ipAddress(id: ID!): IPAddress

  """
  Check if a Password Reset exists
  """
  isExistingResetToken(token: String): Boolean!

  """
  Returns the latest available tag for a given image repository
  """
  latestImageDetails(
    """
    <repositry>/<name>:<tag>
    """
    image: String!
  ): ImageVersion!

  """
  Returns the latest available tag for a given image repository
  """
  latestImageTag(repository: String!, snapshotId: ID): String!

  """
  Get a single machine
  """
  machine(machineId: String!): Machine!

  """
  List machines
  """
  machines(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String
    appId: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
    state: String
    version: Int
  ): MachineConnection!
  nearestRegion(wireguardGateway: Boolean): Region!

  """
  Fetches an object given its ID.
  """
  node(
    """
    ID of the object.
    """
    id: ID!
  ): Node

  """
  Fetches a list of objects given a list of IDs.
  """
  nodes(
    """
    IDs of the objects.
    """
    ids: [ID!]!
  ): [Node]!
  onboarding: Onboarding!

  """
  Find an organization by ID
  """
  organization(id: ID, name: String, slug: String): Organization
  organizations(
    admin: Boolean

    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
    type: OrganizationType
    withBillingIssuesOnly: Boolean
  ): OrganizationConnection!
  personalOrganization: Organization!

  """
  fly.io platform information
  """
  platform: FlyPlatform!

  """
  List postgres attachments
  """
  postgresAttachments(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String
    appName: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
    postgresAppName: String!
  ): PostgresClusterAttachmentConnection!

  """
  Fly.io product and price information
  """
  products: [Product!]!

  """
  Whether the authentication token only allows for user access
  """
  userOnlyToken: Boolean!
  validateConfig(definition: JSON!): AppConfig!
  viewer: Principal!

  """
  Find a persistent volume by ID
  """
  volume(id: ID!): Volume
  volumeAttachments(appId: ID!, volumeId: ID): JSON
  volumeSnapshots(appId: Int!, volumeId: BigInt!): [VolumeSnapshot!]
}

"""
Autogenerated input type of RedeemOrganizationInvitation
"""
input RedeemOrganizationInvitationInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The token of the invitation
  """
  token: String!
}

"""
Autogenerated return type of RedeemOrganizationInvitation.
"""
type RedeemOrganizationInvitationPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  organization: Organization!
  token: String!
}

type Region {
  """
  The IATA airport code for this region
  """
  code: String!
  gatewayAvailable: Boolean!

  """
  The latitude of this region
  """
  latitude: Float

  """
  The longitude of this region
  """
  longitude: Float

  """
  The name of this region
  """
  name: String!
  processGroup: String
  requiresPaidPlan: Boolean!
}

type RegionPlacement {
  """
  The desired number of allocations
  """
  count: Int

  """
  The region code
  """
  region: String!
}

"""
Autogenerated input type of RegisterDomain
"""
input RegisterDomainInput {
  """
  Enable auto renew on the registration
  """
  autoRenew: Boolean

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The node ID of the domain
  """
  domainId: ID!

  """
  Enable whois privacy on the registration
  """
  whoisPrivacy: Boolean
}

"""
Autogenerated return type of RegisterDomain.
"""
type RegisterDomainPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  domain: Domain!
}

"""
Autogenerated input type of RegisterMachine
"""
input RegisterMachineInput {
  """
  The ID of the app
  """
  appId: ID!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  Machine configuration
  """
  config: JSON!

  """
  Host ID machine is running on
  """
  hostId: Int

  """
  Host name machine is running on
  """
  hostName: ID

  """
  The ID of the machine
  """
  id: String!

  """
  Machine network configuration
  """
  machineIps: JSON!

  """
  Name of the machine
  """
  name: String!

  """
  Region of the machine
  """
  region: String!

  """
  User provided configuration
  """
  userConfig: JSON!

  """
  The machine version
  """
  version: String!
}

"""
Autogenerated return type of RegisterMachine.
"""
type RegisterMachinePayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  id: ID!
}

"""
Autogenerated input type of RegisterVolume
"""
input RegisterVolumeInput {
  """
  The application to attach the new volume to
  """
  appId: Int!
  autoBackupEnabled: Boolean!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  Volume should be encrypted at rest
  """
  encrypted: Boolean! = true
  host: String!

  """
  Volume name
  """
  name: String!

  """
  Desired region for volume
  """
  region: String!

  """
  Desired volume size, in GB
  """
  sizeGb: Int!
  snapshot: ID

  """
  Number of days to retain snapshots
  """
  snapshotRetentionDays: Int
  volumeId: String!
}

"""
Autogenerated return type of RegisterVolume.
"""
type RegisterVolumePayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  ok: Boolean!
}

type Release implements Node {
  config: AppConfig
  createdAt: ISO8601DateTime!
  deploymentStrategy: DeploymentStrategy!

  """
  A description of the release
  """
  description: String!
  evaluationId: String

  """
  Unique ID
  """
  id: ID!

  """
  Docker image
  """
  image: Image

  """
  Docker image URI
  """
  imageRef: String
  inProgress: Boolean! @deprecated(reason: "use deployment.inProgress")
  metadata: JSON

  """
  The reason for the release
  """
  reason: String!

  """
  Version release reverted to
  """
  revertedTo: Int
  stable: Boolean!

  """
  The status of the release
  """
  status: String!
  updatedAt: ISO8601DateTime!

  """
  The user who created the release
  """
  user: User

  """
  The version of the release
  """
  version: Int!
}

type ReleaseCommand implements Node {
  app: App!
  command: String!
  evaluationId: String
  exitCode: Int
  failed: Boolean!
  id: ID!
  inProgress: Boolean!
  instanceId: String
  status: String!
  succeeded: Boolean!
}

"""
The connection type for Release.
"""
type ReleaseConnection {
  """
  A list of edges.
  """
  edges: [ReleaseEdge]

  """
  A list of nodes.
  """
  nodes: [Release]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

"""
An edge in a connection.
"""
type ReleaseEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: Release
}

"""
Autogenerated input type of ReleaseEgressIPAddress
"""
input ReleaseEgressIPAddressInput {
  """
  The ID of the app
  """
  appId: ID!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The ID of the machine
  """
  machineId: ID!
}

"""
Autogenerated return type of ReleaseEgressIPAddress.
"""
type ReleaseEgressIPAddressPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  v4: String
  v6: String
}

"""
Autogenerated input type of ReleaseIPAddress
"""
input ReleaseIPAddressInput {
  """
  The ID of the app
  """
  appId: ID

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  ip: String

  """
  The id of the ip address to release
  """
  ipAddressId: ID

  """
  The name of the associated service
  """
  serviceName: String
}

"""
Autogenerated return type of ReleaseIPAddress.
"""
type ReleaseIPAddressPayload {
  app: App!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

type ReleaseUnprocessed implements Node {
  configDefinition: JSON
  createdAt: ISO8601DateTime!
  deploymentStrategy: DeploymentStrategy!

  """
  A description of the release
  """
  description: String!
  evaluationId: String

  """
  Unique ID
  """
  id: ID!

  """
  Docker image
  """
  image: Image

  """
  Docker image URI
  """
  imageRef: String
  inProgress: Boolean! @deprecated(reason: "use deployment.inProgress")

  """
  The reason for the release
  """
  reason: String!

  """
  Version release reverted to
  """
  revertedTo: Int
  stable: Boolean!

  """
  The status of the release
  """
  status: String!
  updatedAt: ISO8601DateTime!

  """
  The user who created the release
  """
  user: User

  """
  The version of the release
  """
  version: Int!
}

"""
The connection type for ReleaseUnprocessed.
"""
type ReleaseUnprocessedConnection {
  """
  A list of edges.
  """
  edges: [ReleaseUnprocessedEdge]

  """
  A list of nodes.
  """
  nodes: [ReleaseUnprocessed]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

"""
An edge in a connection.
"""
type ReleaseUnprocessedEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: ReleaseUnprocessed
}

type RemoteDockerBuilderAppRole implements AppRole {
  """
  The name of this role
  """
  name: String!
}

"""
Autogenerated input type of RemoveMachine
"""
input RemoveMachineInput {
  """
  The ID of the app
  """
  appId: ID

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  machine id
  """
  id: String!

  """
  force kill machine if it's running
  """
  kill: Boolean
}

"""
Autogenerated return type of RemoveMachine.
"""
type RemoveMachinePayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  machine: Machine!
}

"""
Autogenerated input type of RemoveWireGuardPeer
"""
input RemoveWireGuardPeerInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The name of the peer to remove
  """
  name: String!

  """
  Add via NATS transaction (for testing only, nosy users)
  """
  nats: Boolean

  """
  The node ID of the organization
  """
  organizationId: ID!
}

"""
Autogenerated return type of RemoveWireGuardPeer.
"""
type RemoveWireGuardPeerPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The organization that owned the peer
  """
  organization: Organization!
}

"""
Autogenerated input type of RequestSignedDocument
"""
input RequestSignedDocumentInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The key of the document to be sent
  """
  documentType: String!

  """
  The email to invite
  """
  email: String

  """
  The node ID of the organization
  """
  organizationId: ID!
}

"""
Autogenerated return type of RequestSignedDocument.
"""
type RequestSignedDocumentPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  signedDocument: SignedDocument!
}

"""
Autogenerated input type of ResetAddOnPassword
"""
input ResetAddOnPasswordInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The ID of the add-on whose password should be reset
  """
  name: String!
}

"""
Autogenerated return type of ResetAddOnPassword.
"""
type ResetAddOnPasswordPayload {
  addOn: AddOn!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated input type of ResolvePasswordReset
"""
input ResolvePasswordResetInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  New password
  """
  password: String!

  """
  Password reset token
  """
  token: String!
}

"""
Autogenerated return type of ResolvePasswordReset.
"""
type ResolvePasswordResetPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  success: Boolean!
}

"""
Autogenerated input type of RestartAllocation
"""
input RestartAllocationInput {
  """
  The ID of the app
  """
  allocId: ID!

  """
  The ID of the app
  """
  appId: ID!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated return type of RestartAllocation.
"""
type RestartAllocationPayload {
  allocation: Allocation!
  app: App!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated input type of RestartApp
"""
input RestartAppInput {
  """
  The ID of the app
  """
  appId: ID!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated return type of RestartApp.
"""
type RestartAppPayload {
  app: App!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated input type of RestoreVolumeSnapshot
"""
input RestoreVolumeSnapshotInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  snapshotId: ID!
  volumeId: ID!
}

"""
Autogenerated return type of RestoreVolumeSnapshot.
"""
type RestoreVolumeSnapshotPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  snapshot: VolumeSnapshot!
  volume: Volume!
}

"""
Autogenerated input type of ResumeApp
"""
input ResumeAppInput {
  """
  The ID of the app
  """
  appId: ID!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated return type of ResumeApp.
"""
type ResumeAppPayload {
  app: App!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated input type of RevokePostgresClusterUserAccess
"""
input RevokePostgresClusterUserAccessInput {
  """
  The name of the postgres cluster app
  """
  appName: String!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The database to revoke access to
  """
  databaseName: String!

  """
  The username to revoke
  """
  username: String!
}

"""
Autogenerated return type of RevokePostgresClusterUserAccess.
"""
type RevokePostgresClusterUserAccessPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  database: PostgresClusterDatabase!
  postgresClusterRole: PostgresClusterAppRole!
  user: PostgresClusterUser!
}

enum RuntimeType {
  """
  Fly Container Runtime
  """
  FIRECRACKER

  """
  Fly JavaScript Runtime
  """
  NODEPROXY
}

"""
Autogenerated input type of SaveDeploymentSource
"""
input SaveDeploymentSourceInput {
  """
  The application to update
  """
  appId: String!
  baseDir: String

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  provider: String!
  ref: String
  repositoryId: String!
  skipBuild: Boolean
}

"""
Autogenerated return type of SaveDeploymentSource.
"""
type SaveDeploymentSourcePayload {
  app: App
  build: Build

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated input type of ScaleApp
"""
input ScaleAppInput {
  """
  The ID of the app
  """
  appId: ID!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  Regions to scale
  """
  regions: [ScaleRegionInput!]!
}

"""
Autogenerated return type of ScaleApp.
"""
type ScaleAppPayload {
  app: App!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  delta: [ScaleRegionChange!]!
  placement: [RegionPlacement!]!
}

type ScaleRegionChange {
  """
  The original value
  """
  fromCount: Int!

  """
  The region code
  """
  region: String!

  """
  The new value
  """
  toCount: Int
}

"""
Region placement configuration
"""
input ScaleRegionInput {
  """
  The value to change by
  """
  count: Int!

  """
  The region to configure
  """
  region: String!
}

type Secret implements Node {
  createdAt: ISO8601DateTime!

  """
  The digest of the secret value
  """
  digest: String!
  id: ID!

  """
  The name of the secret
  """
  name: String!

  """
  The user who initiated the deployment
  """
  user: User
}

"""
A secure configuration value
"""
input SecretInput {
  """
  The unqiue key for this secret
  """
  key: String!

  """
  The value of this secret
  """
  value: String!
}

"""
Autogenerated input type of SendVerificationEmail
"""
input SendVerificationEmailInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  Return to address to attach to the confirmation
  """
  returnTo: String
}

"""
Autogenerated return type of SendVerificationEmail.
"""
type SendVerificationEmailPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  success: Boolean
}

"""
Global port routing
"""
type Service {
  """
  Health checks
  """
  checks: [Check!]!
  description: String!

  """
  Hard concurrency limit
  """
  hardConcurrency: Int!

  """
  Application port to forward traffic to
  """
  internalPort: Int!

  """
  Ports to listen on
  """
  ports: [ServicePort!]!

  """
  Protocol to listen on
  """
  protocol: ServiceProtocolType!

  """
  Soft concurrency limit
  """
  softConcurrency: Int!
}

enum ServiceHandlerType {
  """
  Convert TCP connection to HTTP (at the edge)
  """
  EDGE_HTTP

  """
  Convert TCP connection to HTTP
  """
  HTTP

  """
  Handle TLS for PostgreSQL connections
  """
  PG_TLS

  """
  Wrap TCP connection in PROXY protocol
  """
  PROXY_PROTO

  """
  Convert TLS connection to unencrypted TCP
  """
  TLS
}

"""
Global port routing
"""
input ServiceInput {
  """
  Health checks
  """
  checks: [CheckInput!]

  """
  Hard concurrency limit
  """
  hardConcurrency: Int

  """
  Application port to forward traffic to
  """
  internalPort: Int!

  """
  Ports to listen on
  """
  ports: [ServiceInputPort!]

  """
  Protocol to listen on
  """
  protocol: ServiceProtocolType!

  """
  Soft concurrency limit
  """
  softConcurrency: Int
}

"""
Service port
"""
input ServiceInputPort {
  """
  Handlers to apply before forwarding service traffic
  """
  handlers: [ServiceHandlerType!]

  """
  Port to listen on
  """
  port: Int!

  """
  tls options
  """
  tlsOptions: ServicePortTlsOptionsInput
}

"""
Service port
"""
type ServicePort {
  """
  End port for range
  """
  endPort: Int

  """
  Handlers to apply before forwarding service traffic
  """
  handlers: [ServiceHandlerType!]!

  """
  Port to listen on
  """
  port: Int

  """
  Start port for range
  """
  startPort: Int
}

"""
TLS handshakes options for a port
"""
input ServicePortTlsOptionsInput {
  defaultSelfSigned: Boolean
}

enum ServiceProtocolType {
  """
  TCP protocol
  """
  TCP

  """
  UDP protocl
  """
  UDP
}

"""
Autogenerated input type of SetAppsv2DefaultOn
"""
input SetAppsv2DefaultOnInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  Whether or not new apps in this org use Apps V2 by default
  """
  defaultOn: Boolean!

  """
  The organization slug
  """
  organizationSlug: String!
}

"""
Autogenerated return type of SetAppsv2DefaultOn.
"""
type SetAppsv2DefaultOnPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  organization: Organization!
}

"""
Autogenerated input type of SetPagerdutyHandler
"""
input SetPagerdutyHandlerInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  Handler name
  """
  name: String!

  """
  The node ID of the organization
  """
  organizationId: ID!

  """
  Map of alert severity levels to PagerDuty severity levels
  """
  pagerdutyStatusMap: JSON

  """
  PagerDuty API token
  """
  pagerdutyToken: String!
}

"""
Autogenerated return type of SetPagerdutyHandler.
"""
type SetPagerdutyHandlerPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  handler: HealthCheckHandler!
}

"""
Autogenerated input type of SetPlatformVersion
"""
input SetPlatformVersionInput {
  """
  The ID of the app
  """
  appId: ID!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  Unique lock ID
  """
  lockId: ID

  """
  nomad or machines
  """
  platformVersion: String!
}

"""
Autogenerated return type of SetPlatformVersion.
"""
type SetPlatformVersionPayload {
  app: App!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated input type of SetSecrets
"""
input SetSecretsInput {
  """
  The ID of the app
  """
  appId: ID!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  By default, we set only the secrets you specify. Set this to true to replace all secrets.
  """
  replaceAll: Boolean

  """
  Secrets to set
  """
  secrets: [SecretInput!]!
}

"""
Autogenerated return type of SetSecrets.
"""
type SetSecretsPayload {
  app: App!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  release: Release
}

"""
Autogenerated input type of SetSlackHandler
"""
input SetSlackHandlerInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  Handler name
  """
  name: String!

  """
  The node ID of the organization
  """
  organizationId: ID!

  """
  Slack channel to send messages to, defaults to #general
  """
  slackChannel: String

  """
  Icon to show with Slack messages
  """
  slackIconUrl: String

  """
  User name to display on Slack Messages (defaults to Fly)
  """
  slackUsername: String

  """
  Slack Webhook URL to use for health check notifications
  """
  slackWebhookUrl: String!
}

"""
Autogenerated return type of SetSlackHandler.
"""
type SetSlackHandlerPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  handler: HealthCheckHandler!
}

"""
Autogenerated input type of SetVMCount
"""
input SetVMCountInput {
  """
  The ID of the app
  """
  appId: ID!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  Counts for VM groups
  """
  groupCounts: [VMCountInput!]!

  """
  Unique lock ID
  """
  lockId: ID
}

"""
Autogenerated return type of SetVMCount.
"""
type SetVMCountPayload {
  app: App!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  release: Release
  taskGroupCounts: [TaskGroupCount!]!
  warnings: [String!]!
}

"""
Autogenerated input type of SetVMSize
"""
input SetVMSizeInput {
  """
  The ID of the app
  """
  appId: ID!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  Process group to modify
  """
  group: String

  """
  Optionally request more memory
  """
  memoryMb: Int

  """
  The name of the vm size to set
  """
  sizeName: String!
}

"""
Autogenerated return type of SetVMSize.
"""
type SetVMSizePayload {
  app: App!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  Process Group scale change applied to (if any)
  """
  processGroup: ProcessGroup

  """
  Default app vm size
  """
  vmSize: VMSize
}

type SignedDocument {
  canRemind: Boolean!

  """
  Document type
  """
  documentType: String!

  """
  Request ID on Hellosign
  """
  signatureRequestId: String
  signed: Boolean!

  """
  Email Address responsible for signing document
  """
  signerEmailAddress: String
}

"""
The connection type for SignedDocument.
"""
type SignedDocumentConnection {
  """
  A list of edges.
  """
  edges: [SignedDocumentEdge]

  """
  A list of nodes.
  """
  nodes: [SignedDocument]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

"""
An edge in a connection.
"""
type SignedDocumentEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: SignedDocument
}

"""
Autogenerated input type of StartBuild
"""
input StartBuildInput {
  """
  The ID of the app
  """
  appId: ID!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated return type of StartBuild.
"""
type StartBuildPayload {
  build: Build!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated input type of StartMachine
"""
input StartMachineInput {
  """
  The ID of the app
  """
  appId: ID

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  machine id
  """
  id: String!
}

"""
Autogenerated return type of StartMachine.
"""
type StartMachinePayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  machine: Machine!
}

"""
Autogenerated input type of StopAllocation
"""
input StopAllocationInput {
  """
  The ID of the app
  """
  allocId: ID!

  """
  The ID of the app
  """
  appId: ID!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated return type of StopAllocation.
"""
type StopAllocationPayload {
  allocation: Allocation!
  app: App!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated input type of StopMachine
"""
input StopMachineInput {
  """
  The ID of the app
  """
  appId: ID

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  machine id
  """
  id: String!

  """
  how long to wait before force killing the machine
  """
  killTimeoutSecs: Int

  """
  signal to send the machine
  """
  signal: String
}

"""
Autogenerated return type of StopMachine.
"""
type StopMachinePayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  machine: Machine!
}

"""
Autogenerated input type of StopPlanDowngrade
"""
input StopPlanDowngradeInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The node ID of the organization
  """
  organizationId: ID!
}

"""
Autogenerated return type of StopPlanDowngrade.
"""
type StopPlanDowngradePayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  organization: Organization!
}

"""
Autogenerated input type of SyncTokensForOrg
"""
input SyncTokensForOrgInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The organization slug
  """
  organizationSlug: String!
}

"""
Autogenerated return type of SyncTokensForOrg.
"""
type SyncTokensForOrgPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

type TaskGroupCount {
  count: Int!
  name: String!
}

type TemplateDeployment implements Node {
  apps(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): AppConnection!
  id: ID!
  organization: Organization!
  status: String!
}

"""
Configuration for third-party caveats to be added to user macaroons
"""
type ThirdPartyConfiguration implements Node {
  """
  Restrictions to be placed on third-party caveats
  """
  caveats: CaveatSet
  createdAt: ISO8601DateTime!

  """
  Whether to add this third-party caveat on tokens issued via `flyctl tokens create`
  """
  customLevel: ThirdPartyConfigurationLevel!

  """
  Whether to add this third-party caveat on session tokens issued to flyctl
  """
  flyctlLevel: ThirdPartyConfigurationLevel!
  id: ID!

  """
  Location URL of the third-party service capable of discharging
  """
  location: String!

  """
  Friendly name for this configuration
  """
  name: String!

  """
  Organization that owns this third party configuration
  """
  organization: Organization!

  """
  Whether to add this third-party caveat on Fly.io session tokens
  """
  uiexLevel: ThirdPartyConfigurationLevel!
  updatedAt: ISO8601DateTime!
}

"""
The connection type for ThirdPartyConfiguration.
"""
type ThirdPartyConfigurationConnection {
  """
  A list of edges.
  """
  edges: [ThirdPartyConfigurationEdge]

  """
  A list of nodes.
  """
  nodes: [ThirdPartyConfiguration]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

"""
An edge in a connection.
"""
type ThirdPartyConfigurationEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: ThirdPartyConfiguration
}

enum ThirdPartyConfigurationLevel {
  """
  Configuration is enabled by default. Admins can opt out
  """
  ADMIN_OPT_OUT

  """
  Configuration is disabled and cannot be opted into
  """
  DISABLED

  """
  Configuration is enabled by default. All members can opt out
  """
  MEMBER_OPT_OUT

  """
  Configuration can be manually opted into
  """
  OPT_IN

  """
  Configuration is enabled by default. No one can opt out
  """
  REQUIRED
}

"""
Autogenerated input type of UnlockApp
"""
input UnlockAppInput {
  """
  The ID of the app
  """
  appId: ID!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  Unique lock ID
  """
  lockId: ID!
}

"""
Autogenerated return type of UnlockApp.
"""
type UnlockAppPayload {
  app: App!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated input type of UnsetSecrets
"""
input UnsetSecretsInput {
  """
  The ID of the app
  """
  appId: ID!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  Secret keys to unset
  """
  keys: [String!]!
}

"""
Autogenerated return type of UnsetSecrets.
"""
type UnsetSecretsPayload {
  app: App!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  release: Release
}

"""
Autogenerated input type of UpdateAddOn
"""
input UpdateAddOnInput {
  """
  The add-on ID to update
  """
  addOnId: ID

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  Metadata for the add-on
  """
  metadata: JSON

  """
  The add-on name to update
  """
  name: String

  """
  Options specific to the add-on
  """
  options: JSON

  """
  The add-on plan ID
  """
  planId: ID

  """
  The add-on service provider type
  """
  provider: String

  """
  Desired regions to place replicas in
  """
  readRegions: [String!]
}

"""
Autogenerated return type of UpdateAddOn.
"""
type UpdateAddOnPayload {
  addOn: AddOn!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated input type of UpdateAutoscaleConfig
"""
input UpdateAutoscaleConfigInput {
  """
  The ID of the app
  """
  appId: ID!
  balanceRegions: Boolean

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  enabled: Boolean
  maxCount: Int
  minCount: Int

  """
  Region configs
  """
  regions: [AutoscaleRegionConfigInput!]
  resetRegions: Boolean
}

"""
Autogenerated return type of UpdateAutoscaleConfig.
"""
type UpdateAutoscaleConfigPayload {
  app: App!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
}

"""
Autogenerated input type of UpdateDNSPortal
"""
input UpdateDNSPortalInput {
  """
  The secondary branding color
  """
  accentColor: String

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The node ID of the organization
  """
  dnsPortalId: ID!

  """
  The unique name of this portal.
  """
  name: String

  """
  The primary branding color
  """
  primaryColor: String

  """
  The return url for this portal
  """
  returnUrl: String

  """
  The text to display for the return url link
  """
  returnUrlText: String

  """
  The support url for this portal
  """
  supportUrl: String

  """
  The text to display for the support url link
  """
  supportUrlText: String

  """
  The title of this portal
  """
  title: String
}

"""
Autogenerated return type of UpdateDNSPortal.
"""
type UpdateDNSPortalPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  dnsPortal: DNSPortal!
}

"""
Autogenerated input type of UpdateDNSRecord
"""
input UpdateDNSRecordInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The dns record name
  """
  name: String

  """
  The content of the record
  """
  rdata: String

  """
  The node ID of the DNS record
  """
  recordId: ID!

  """
  The TTL in seconds
  """
  ttl: Int
}

"""
Autogenerated return type of UpdateDNSRecord.
"""
type UpdateDNSRecordPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  record: DNSRecord!
}

"""
Autogenerated input type of UpdateDNSRecords
"""
input UpdateDNSRecordsInput {
  changes: [DNSRecordChangeInput!]!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The node ID of the domain
  """
  domainId: ID!
}

"""
Autogenerated return type of UpdateDNSRecords.
"""
type UpdateDNSRecordsPayload {
  changes: [DNSRecordDiff!]!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  domain: Domain!
  warnings: [DNSRecordWarning!]!
}

"""
Autogenerated input type of UpdateOrganizationMembership
"""
input UpdateOrganizationMembershipInput {
  """
  The new alert settings for the user
  """
  alertsEnabled: OrganizationAlertsEnabled

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The node ID of the organization
  """
  organizationId: ID!

  """
  The new role for the user
  """
  role: OrganizationMemberRole!

  """
  The node ID of the user
  """
  userId: ID!
}

"""
Autogenerated return type of UpdateOrganizationMembership.
"""
type UpdateOrganizationMembershipPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  organization: Organization!
  user: User!
}

"""
Autogenerated input type of UpdateRelease
"""
input UpdateReleaseInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The metadata for the release
  """
  metadata: JSON

  """
  The ID of the release
  """
  releaseId: ID!

  """
  The new status for the release
  """
  status: String
}

"""
Autogenerated return type of UpdateRelease.
"""
type UpdateReleasePayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  release: Release!
}

"""
Autogenerated input type of UpdateRemoteBuilder
"""
input UpdateRemoteBuilderInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  Docker image reference
  """
  image: String!

  """
  The node ID of the organization
  """
  organizationId: ID!
}

"""
Autogenerated return type of UpdateRemoteBuilder.
"""
type UpdateRemoteBuilderPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  organization: Organization!
}

"""
Autogenerated input type of UpdateRiskFromStripe
"""
input UpdateRiskFromStripeInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  Raw response from Stripe Radar
  """
  outcome: JSON!
}

"""
Autogenerated return type of UpdateRiskFromStripe.
"""
type UpdateRiskFromStripePayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  highRisk: Boolean!
}

"""
Autogenerated input type of UpdateThirdPartyConfiguration
"""
input UpdateThirdPartyConfigurationInput {
  """
  Restrictions to be placed on third-party caveats
  """
  caveats: CaveatSet

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  Whether to add this third-party caveat on tokens issued via `flyctl tokens create`
  """
  customLevel: ThirdPartyConfigurationLevel

  """
  Whether to add this third-party caveat on session tokens issued to flyctl
  """
  flyctlLevel: ThirdPartyConfigurationLevel

  """
  Location URL of the third-party service capable of discharging
  """
  location: String

  """
  Friendly name for this configuration
  """
  name: String

  """
  The node ID of the configuration
  """
  thirdPartyConfigurationId: ID!

  """
  Whether to add this third-party caveat on Fly.io session tokens
  """
  uiexLevel: ThirdPartyConfigurationLevel
}

"""
Autogenerated return type of UpdateThirdPartyConfiguration.
"""
type UpdateThirdPartyConfigurationPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  thirdPartyConfiguration: ThirdPartyConfiguration!
}

"""
Autogenerated input type of UpdateUserCoupon
"""
input UpdateUserCouponInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  The node ID of the organization
  """
  organizationId: ID!
}

"""
Autogenerated return type of UpdateUserCoupon.
"""
type UpdateUserCouponPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  userCoupon: UserCoupon!
}

"""
Autogenerated input type of UpdateUserPassword
"""
input UpdateUserPasswordInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  currentPassword: String!
  newPassword: String!
}

"""
Autogenerated return type of UpdateUserPassword.
"""
type UpdateUserPasswordPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  user: User!
}

"""
Autogenerated input type of UpdateUserProfile
"""
input UpdateUserProfileInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  email: String
  fullName: String
  username: String
}

"""
Autogenerated return type of UpdateUserProfile.
"""
type UpdateUserProfilePayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  user: User!
}

"""
Autogenerated input type of UpdateVolume
"""
input UpdateVolumeInput {
  """
  The application to attach the new volume to
  """
  appId: ID!

  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String

  """
  Number of days to retain snapshots
  """
  snapshotRetentionDays: Int
  volumeId: String!
}

"""
Autogenerated return type of UpdateVolume.
"""
type UpdateVolumePayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  ok: Boolean!
}

type User implements Node & Principal {
  """
  Check if the organization has agreed to the extension provider terms of service
  """
  agreedToProviderTos(providerName: String!): Boolean!

  """
  URL for avatar or placeholder
  """
  avatarUrl: String!
  blankPassword: Boolean
  createdAt: ISO8601DateTime!

  """
  Email address for user (private)
  """
  email: String!
  emailVerified: Boolean!

  """
  Whether to create new organizations under Hobby plan
  """
  enablePaidHobby: Boolean!
  featureFlags: [String!]!
  hasNodeproxyApps: Boolean!
  hasRecoveryCodes: Boolean!
  highRisk: Boolean
  id: ID!
  identities: [Identity!]

  """
  Find an identity by provider
  """
  identity(provider: String!): Identity
  internalNumericId: Int!
  lastRegion: String

  """
  Display / full name for user (private)
  """
  name: String
  organizations(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): OrganizationConnection! @deprecated(reason: "Use query.organizations instead")
  personalOrganization: Organization! @deprecated(reason: "Use query.personalOrganization instead")
  stripeVerificationStatus: String
  trust: OrganizationTrust!
  trustScore: Int
  twoFactorProtection: Boolean!

  """
  Public username for user
  """
  username: String
}

type UserCoupon implements Node {
  createdAt: ISO8601DateTime!
  id: ID!

  """
  Organization that owns this app
  """
  organization: Organization!
  stripeCouponId: String
  updatedAt: ISO8601DateTime!
}

type VM implements Node {
  attachedVolumes(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): VolumeConnection!
  canary: Boolean!
  checks(
    """
    Filter checks by name
    """
    name: String
  ): [CheckState!]!
  createdAt: ISO8601DateTime!
  criticalCheckCount: Int!

  """
  Desired status
  """
  desiredStatus: String!
  events: [AllocationEvent!]!
  failed: Boolean!
  healthy: Boolean!

  """
  Unique ID for this instance
  """
  id: ID!

  """
  Short unique ID for this instance
  """
  idShort: ID!

  """
  Indicates if this instance is from the latest job version
  """
  latestVersion: Boolean!
  passingCheckCount: Int!

  """
  Private IPv6 address for this instance
  """
  privateIP: String
  recentLogs(
    """
    Max number of entries to return
    """
    limit: Int = 10

    """
    Max age of log entries in seconds
    """
    range: Int = 300
  ): [LogEntry!]!

  """
  Region this allocation is running in
  """
  region: String!
  restarts: Int!

  """
  Current status
  """
  status: String!
  taskName: String!
  totalCheckCount: Int!
  transitioning: Boolean!
  updatedAt: ISO8601DateTime!

  """
  The configuration version of this instance
  """
  version: Int!
  warningCheckCount: Int!
}

"""
The connection type for VM.
"""
type VMConnection {
  activeCount: Int!
  completeCount: Int!

  """
  A list of edges.
  """
  edges: [VMEdge]
  failedCount: Int!
  inactiveCount: Int!
  lostCount: Int!

  """
  A list of nodes.
  """
  nodes: [VM]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  pendingCount: Int!
  runningCount: Int!
  totalCount: Int!
}

input VMCountInput {
  """
  The desired count
  """
  count: Int

  """
  VM group name
  """
  group: String

  """
  Max number of VMs to allow per region
  """
  maxPerRegion: Int
}

"""
An edge in a connection.
"""
type VMEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: VM
}

type VMSize {
  cpuCores: Float!
  maxMemoryMb: Int!
  memoryGb: Float!
  memoryIncrementsMb: [Int!]!
  memoryMb: Int!
  name: String!
  priceMonth: Float!
  priceSecond: Float!
}

"""
Autogenerated input type of ValidateWireGuardPeers
"""
input ValidateWireGuardPeersInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  peerIps: [String!]!
}

"""
Autogenerated return type of ValidateWireGuardPeers.
"""
type ValidateWireGuardPeersPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  invalidPeerIps: [String!]!
  validPeerIps: [String!]!
}

"""
Autogenerated input type of VerifyEmail
"""
input VerifyEmailInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  token: String!
}

"""
Autogenerated return type of VerifyEmail.
"""
type VerifyEmailPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  verified: Boolean!
}

"""
Autogenerated input type of VerifyUserPassword
"""
input VerifyUserPasswordInput {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  password: String!
}

"""
Autogenerated return type of VerifyUserPassword.
"""
type VerifyUserPasswordPayload {
  """
  A unique identifier for the client performing the mutation.
  """
  clientMutationId: String
  success: Boolean!
}

type Volume implements Node {
  app: App!
  attachedAllocation: Allocation
  attachedAllocationId: String
  attachedMachine: Machine
  createdAt: ISO8601DateTime!
  encrypted: Boolean!
  host: Host!
  id: ID!
  internalId: String!
  name: String!
  region: String!
  sizeGb: Int!
  snapshotRetentionDays: Int
  snapshots(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): VolumeSnapshotConnection!
  state: String!
  status: String!
  usedBytes: BigInt!
}

"""
The connection type for Volume.
"""
type VolumeConnection {
  """
  A list of edges.
  """
  edges: [VolumeEdge]

  """
  A list of nodes.
  """
  nodes: [Volume]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

"""
An edge in a connection.
"""
type VolumeEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: Volume
}

type VolumeSnapshot implements Node {
  createdAt: ISO8601DateTime!
  digest: String!
  id: ID!
  retentionDays: Int
  size: BigInt!
  volume: Volume!
}

"""
The connection type for VolumeSnapshot.
"""
type VolumeSnapshotConnection {
  """
  A list of edges.
  """
  edges: [VolumeSnapshotEdge]

  """
  A list of nodes.
  """
  nodes: [VolumeSnapshot]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

"""
An edge in a connection.
"""
type VolumeSnapshotEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: VolumeSnapshot
}

type WireGuardPeer implements Node {
  id: ID!
  name: String!
  network: String
  peerip: String!
  pubkey: String!
  region: String!
}

"""
The connection type for WireGuardPeer.
"""
type WireGuardPeerConnection {
  """
  A list of edges.
  """
  edges: [WireGuardPeerEdge]

  """
  A list of nodes.
  """
  nodes: [WireGuardPeer]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
  totalCount: Int!
}

"""
An edge in a connection.
"""
type WireGuardPeerEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: WireGuardPeer
}

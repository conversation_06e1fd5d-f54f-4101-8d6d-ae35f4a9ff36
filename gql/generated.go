// Code generated by github.com/Khan/genqlient, DO NOT EDIT.

package gql

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/Khan/genqlient/graphql"
)

// AddOnData includes the GraphQL fields of AddOn requested by the fragment AddOnData.
type AddOnData struct {
	Id string `json:"id"`
	// The service name according to the provider
	Name string `json:"name"`
	// Region where the primary instance is deployed
	PrimaryRegion string `json:"primaryRegion"`
	// Status of the add-on
	Status string `json:"status"`
	// Optional error message when `status` is `error`
	ErrorMessage string `json:"errorMessage"`
	// Add-on metadata
	Metadata interface{} `json:"metadata"`
	// Add-on options
	Options interface{} `json:"options"`
}

// GetId returns AddOnData.Id, and is useful for accessing the field via an interface.
func (v *AddOnData) GetId() string { return v.Id }

// GetName returns AddOnData.Name, and is useful for accessing the field via an interface.
func (v *AddOnData) GetName() string { return v.Name }

// GetPrimaryRegion returns AddOnData.PrimaryRegion, and is useful for accessing the field via an interface.
func (v *AddOnData) GetPrimaryRegion() string { return v.PrimaryRegion }

// GetStatus returns AddOnData.Status, and is useful for accessing the field via an interface.
func (v *AddOnData) GetStatus() string { return v.Status }

// GetErrorMessage returns AddOnData.ErrorMessage, and is useful for accessing the field via an interface.
func (v *AddOnData) GetErrorMessage() string { return v.ErrorMessage }

// GetMetadata returns AddOnData.Metadata, and is useful for accessing the field via an interface.
func (v *AddOnData) GetMetadata() interface{} { return v.Metadata }

// GetOptions returns AddOnData.Options, and is useful for accessing the field via an interface.
func (v *AddOnData) GetOptions() interface{} { return v.Options }

type AddOnType string

const (
	// An Arcjet site
	AddOnTypeArcjet AddOnType = "arcjet"
	// An Enveloop project
	AddOnTypeEnveloop AddOnType = "enveloop"
	// A MySQL database
	AddOnTypeFlyMysql AddOnType = "fly_mysql"
	// A Kubernetes cluster
	AddOnTypeKubernetes AddOnType = "kubernetes"
	// An Upstash Redis database
	AddOnTypeRedis AddOnType = "redis"
	// A Sentry project endpoint
	AddOnTypeSentry AddOnType = "sentry"
	// A Supabase database
	AddOnTypeSupabase AddOnType = "supabase"
	// A Tigris Data bucket
	AddOnTypeTigris AddOnType = "tigris"
	// An Upstash Kafka cluster
	AddOnTypeUpstashKafka AddOnType = "upstash_kafka"
	// An Upstash Redis database
	AddOnTypeUpstashRedis AddOnType = "upstash_redis"
	// An Upstash Vector cluster
	AddOnTypeUpstashVector AddOnType = "upstash_vector"
	// A Wafris firewall
	AddOnTypeWafris AddOnType = "wafris"
)

var AllAddOnType = []AddOnType{
	AddOnTypeArcjet,
	AddOnTypeEnveloop,
	AddOnTypeFlyMysql,
	AddOnTypeKubernetes,
	AddOnTypeRedis,
	AddOnTypeSentry,
	AddOnTypeSupabase,
	AddOnTypeTigris,
	AddOnTypeUpstashKafka,
	AddOnTypeUpstashRedis,
	AddOnTypeUpstashVector,
	AddOnTypeWafris,
}

// AgentGetInstancesApp includes the requested fields of the GraphQL type App.
type AgentGetInstancesApp struct {
	// Organization that owns this app
	Organization AgentGetInstancesAppOrganization `json:"organization"`
	// Unique application ID
	Id string `json:"id"`
	// The unique application name
	Name        string                                        `json:"name"`
	Allocations []AgentGetInstancesAppAllocationsAllocation   `json:"allocations"`
	Machines    AgentGetInstancesAppMachinesMachineConnection `json:"machines"`
}

// GetOrganization returns AgentGetInstancesApp.Organization, and is useful for accessing the field via an interface.
func (v *AgentGetInstancesApp) GetOrganization() AgentGetInstancesAppOrganization {
	return v.Organization
}

// GetId returns AgentGetInstancesApp.Id, and is useful for accessing the field via an interface.
func (v *AgentGetInstancesApp) GetId() string { return v.Id }

// GetName returns AgentGetInstancesApp.Name, and is useful for accessing the field via an interface.
func (v *AgentGetInstancesApp) GetName() string { return v.Name }

// GetAllocations returns AgentGetInstancesApp.Allocations, and is useful for accessing the field via an interface.
func (v *AgentGetInstancesApp) GetAllocations() []AgentGetInstancesAppAllocationsAllocation {
	return v.Allocations
}

// GetMachines returns AgentGetInstancesApp.Machines, and is useful for accessing the field via an interface.
func (v *AgentGetInstancesApp) GetMachines() AgentGetInstancesAppMachinesMachineConnection {
	return v.Machines
}

// AgentGetInstancesAppAllocationsAllocation includes the requested fields of the GraphQL type Allocation.
type AgentGetInstancesAppAllocationsAllocation struct {
	// Unique ID for this instance
	Id string `json:"id"`
	// Region this allocation is running in
	Region string `json:"region"`
	// Private IPv6 address for this instance
	PrivateIP string `json:"privateIP"`
}

// GetId returns AgentGetInstancesAppAllocationsAllocation.Id, and is useful for accessing the field via an interface.
func (v *AgentGetInstancesAppAllocationsAllocation) GetId() string { return v.Id }

// GetRegion returns AgentGetInstancesAppAllocationsAllocation.Region, and is useful for accessing the field via an interface.
func (v *AgentGetInstancesAppAllocationsAllocation) GetRegion() string { return v.Region }

// GetPrivateIP returns AgentGetInstancesAppAllocationsAllocation.PrivateIP, and is useful for accessing the field via an interface.
func (v *AgentGetInstancesAppAllocationsAllocation) GetPrivateIP() string { return v.PrivateIP }

// AgentGetInstancesAppMachinesMachineConnection includes the requested fields of the GraphQL type MachineConnection.
// The GraphQL type's documentation follows.
//
// The connection type for Machine.
type AgentGetInstancesAppMachinesMachineConnection struct {
	// A list of nodes.
	Nodes []AgentGetInstancesAppMachinesMachineConnectionNodesMachine `json:"nodes"`
}

// GetNodes returns AgentGetInstancesAppMachinesMachineConnection.Nodes, and is useful for accessing the field via an interface.
func (v *AgentGetInstancesAppMachinesMachineConnection) GetNodes() []AgentGetInstancesAppMachinesMachineConnectionNodesMachine {
	return v.Nodes
}

// AgentGetInstancesAppMachinesMachineConnectionNodesMachine includes the requested fields of the GraphQL type Machine.
type AgentGetInstancesAppMachinesMachineConnectionNodesMachine struct {
	State  string                                                                          `json:"state"`
	Id     string                                                                          `json:"id"`
	Region string                                                                          `json:"region"`
	Ips    AgentGetInstancesAppMachinesMachineConnectionNodesMachineIpsMachineIPConnection `json:"ips"`
}

// GetState returns AgentGetInstancesAppMachinesMachineConnectionNodesMachine.State, and is useful for accessing the field via an interface.
func (v *AgentGetInstancesAppMachinesMachineConnectionNodesMachine) GetState() string { return v.State }

// GetId returns AgentGetInstancesAppMachinesMachineConnectionNodesMachine.Id, and is useful for accessing the field via an interface.
func (v *AgentGetInstancesAppMachinesMachineConnectionNodesMachine) GetId() string { return v.Id }

// GetRegion returns AgentGetInstancesAppMachinesMachineConnectionNodesMachine.Region, and is useful for accessing the field via an interface.
func (v *AgentGetInstancesAppMachinesMachineConnectionNodesMachine) GetRegion() string {
	return v.Region
}

// GetIps returns AgentGetInstancesAppMachinesMachineConnectionNodesMachine.Ips, and is useful for accessing the field via an interface.
func (v *AgentGetInstancesAppMachinesMachineConnectionNodesMachine) GetIps() AgentGetInstancesAppMachinesMachineConnectionNodesMachineIpsMachineIPConnection {
	return v.Ips
}

// AgentGetInstancesAppMachinesMachineConnectionNodesMachineIpsMachineIPConnection includes the requested fields of the GraphQL type MachineIPConnection.
// The GraphQL type's documentation follows.
//
// The connection type for MachineIP.
type AgentGetInstancesAppMachinesMachineConnectionNodesMachineIpsMachineIPConnection struct {
	// A list of nodes.
	Nodes []AgentGetInstancesAppMachinesMachineConnectionNodesMachineIpsMachineIPConnectionNodesMachineIP `json:"nodes"`
}

// GetNodes returns AgentGetInstancesAppMachinesMachineConnectionNodesMachineIpsMachineIPConnection.Nodes, and is useful for accessing the field via an interface.
func (v *AgentGetInstancesAppMachinesMachineConnectionNodesMachineIpsMachineIPConnection) GetNodes() []AgentGetInstancesAppMachinesMachineConnectionNodesMachineIpsMachineIPConnectionNodesMachineIP {
	return v.Nodes
}

// AgentGetInstancesAppMachinesMachineConnectionNodesMachineIpsMachineIPConnectionNodesMachineIP includes the requested fields of the GraphQL type MachineIP.
type AgentGetInstancesAppMachinesMachineConnectionNodesMachineIpsMachineIPConnectionNodesMachineIP struct {
	Kind   string `json:"kind"`
	Family string `json:"family"`
	Ip     string `json:"ip"`
}

// GetKind returns AgentGetInstancesAppMachinesMachineConnectionNodesMachineIpsMachineIPConnectionNodesMachineIP.Kind, and is useful for accessing the field via an interface.
func (v *AgentGetInstancesAppMachinesMachineConnectionNodesMachineIpsMachineIPConnectionNodesMachineIP) GetKind() string {
	return v.Kind
}

// GetFamily returns AgentGetInstancesAppMachinesMachineConnectionNodesMachineIpsMachineIPConnectionNodesMachineIP.Family, and is useful for accessing the field via an interface.
func (v *AgentGetInstancesAppMachinesMachineConnectionNodesMachineIpsMachineIPConnectionNodesMachineIP) GetFamily() string {
	return v.Family
}

// GetIp returns AgentGetInstancesAppMachinesMachineConnectionNodesMachineIpsMachineIPConnectionNodesMachineIP.Ip, and is useful for accessing the field via an interface.
func (v *AgentGetInstancesAppMachinesMachineConnectionNodesMachineIpsMachineIPConnectionNodesMachineIP) GetIp() string {
	return v.Ip
}

// AgentGetInstancesAppOrganization includes the requested fields of the GraphQL type Organization.
type AgentGetInstancesAppOrganization struct {
	// Unique organization slug
	Slug string `json:"slug"`
}

// GetSlug returns AgentGetInstancesAppOrganization.Slug, and is useful for accessing the field via an interface.
func (v *AgentGetInstancesAppOrganization) GetSlug() string { return v.Slug }

// AgentGetInstancesResponse is returned by AgentGetInstances on success.
type AgentGetInstancesResponse struct {
	// Find an app by name
	App AgentGetInstancesApp `json:"app"`
}

// GetApp returns AgentGetInstancesResponse.App, and is useful for accessing the field via an interface.
func (v *AgentGetInstancesResponse) GetApp() AgentGetInstancesApp { return v.App }

// AgreedToProviderTosResponse is returned by AgreedToProviderTos on success.
type AgreedToProviderTosResponse struct {
	Viewer AgreedToProviderTosViewerPrincipal `json:"-"`
}

// GetViewer returns AgreedToProviderTosResponse.Viewer, and is useful for accessing the field via an interface.
func (v *AgreedToProviderTosResponse) GetViewer() AgreedToProviderTosViewerPrincipal { return v.Viewer }

func (v *AgreedToProviderTosResponse) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*AgreedToProviderTosResponse
		Viewer json.RawMessage `json:"viewer"`
		graphql.NoUnmarshalJSON
	}
	firstPass.AgreedToProviderTosResponse = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	{
		dst := &v.Viewer
		src := firstPass.Viewer
		if len(src) != 0 && string(src) != "null" {
			err = __unmarshalAgreedToProviderTosViewerPrincipal(
				src, dst)
			if err != nil {
				return fmt.Errorf(
					"unable to unmarshal AgreedToProviderTosResponse.Viewer: %w", err)
			}
		}
	}
	return nil
}

type __premarshalAgreedToProviderTosResponse struct {
	Viewer json.RawMessage `json:"viewer"`
}

func (v *AgreedToProviderTosResponse) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *AgreedToProviderTosResponse) __premarshalJSON() (*__premarshalAgreedToProviderTosResponse, error) {
	var retval __premarshalAgreedToProviderTosResponse

	{

		dst := &retval.Viewer
		src := v.Viewer
		var err error
		*dst, err = __marshalAgreedToProviderTosViewerPrincipal(
			&src)
		if err != nil {
			return nil, fmt.Errorf(
				"unable to marshal AgreedToProviderTosResponse.Viewer: %w", err)
		}
	}
	return &retval, nil
}

// AgreedToProviderTosViewerMacaroon includes the requested fields of the GraphQL type Macaroon.
type AgreedToProviderTosViewerMacaroon struct {
	Typename string `json:"__typename"`
}

// GetTypename returns AgreedToProviderTosViewerMacaroon.Typename, and is useful for accessing the field via an interface.
func (v *AgreedToProviderTosViewerMacaroon) GetTypename() string { return v.Typename }

// AgreedToProviderTosViewerPrincipal includes the requested fields of the GraphQL interface Principal.
//
// AgreedToProviderTosViewerPrincipal is implemented by the following types:
// AgreedToProviderTosViewerMacaroon
// AgreedToProviderTosViewerUser
type AgreedToProviderTosViewerPrincipal interface {
	implementsGraphQLInterfaceAgreedToProviderTosViewerPrincipal()
	// GetTypename returns the receiver's concrete GraphQL type-name (see interface doc for possible values).
	GetTypename() string
}

func (v *AgreedToProviderTosViewerMacaroon) implementsGraphQLInterfaceAgreedToProviderTosViewerPrincipal() {
}
func (v *AgreedToProviderTosViewerUser) implementsGraphQLInterfaceAgreedToProviderTosViewerPrincipal() {
}

func __unmarshalAgreedToProviderTosViewerPrincipal(b []byte, v *AgreedToProviderTosViewerPrincipal) error {
	if string(b) == "null" {
		return nil
	}

	var tn struct {
		TypeName string `json:"__typename"`
	}
	err := json.Unmarshal(b, &tn)
	if err != nil {
		return err
	}

	switch tn.TypeName {
	case "Macaroon":
		*v = new(AgreedToProviderTosViewerMacaroon)
		return json.Unmarshal(b, *v)
	case "User":
		*v = new(AgreedToProviderTosViewerUser)
		return json.Unmarshal(b, *v)
	case "":
		return fmt.Errorf(
			"response was missing Principal.__typename")
	default:
		return fmt.Errorf(
			`unexpected concrete type for AgreedToProviderTosViewerPrincipal: "%v"`, tn.TypeName)
	}
}

func __marshalAgreedToProviderTosViewerPrincipal(v *AgreedToProviderTosViewerPrincipal) ([]byte, error) {

	var typename string
	switch v := (*v).(type) {
	case *AgreedToProviderTosViewerMacaroon:
		typename = "Macaroon"

		result := struct {
			TypeName string `json:"__typename"`
			*AgreedToProviderTosViewerMacaroon
		}{typename, v}
		return json.Marshal(result)
	case *AgreedToProviderTosViewerUser:
		typename = "User"

		result := struct {
			TypeName string `json:"__typename"`
			*AgreedToProviderTosViewerUser
		}{typename, v}
		return json.Marshal(result)
	case nil:
		return []byte("null"), nil
	default:
		return nil, fmt.Errorf(
			`unexpected concrete type for AgreedToProviderTosViewerPrincipal: "%T"`, v)
	}
}

// AgreedToProviderTosViewerUser includes the requested fields of the GraphQL type User.
type AgreedToProviderTosViewerUser struct {
	Typename string `json:"__typename"`
	// Check if the organization has agreed to the extension provider terms of service
	AgreedToProviderTos bool `json:"agreedToProviderTos"`
}

// GetTypename returns AgreedToProviderTosViewerUser.Typename, and is useful for accessing the field via an interface.
func (v *AgreedToProviderTosViewerUser) GetTypename() string { return v.Typename }

// GetAgreedToProviderTos returns AgreedToProviderTosViewerUser.AgreedToProviderTos, and is useful for accessing the field via an interface.
func (v *AgreedToProviderTosViewerUser) GetAgreedToProviderTos() bool { return v.AgreedToProviderTos }

// AllAppsOrganization includes the requested fields of the GraphQL type Organization.
type AllAppsOrganization struct {
	Apps AllAppsOrganizationAppsAppConnection `json:"apps"`
}

// GetApps returns AllAppsOrganization.Apps, and is useful for accessing the field via an interface.
func (v *AllAppsOrganization) GetApps() AllAppsOrganizationAppsAppConnection { return v.Apps }

// AllAppsOrganizationAppsAppConnection includes the requested fields of the GraphQL type AppConnection.
// The GraphQL type's documentation follows.
//
// The connection type for App.
type AllAppsOrganizationAppsAppConnection struct {
	// A list of nodes.
	Nodes []AllAppsOrganizationAppsAppConnectionNodesApp `json:"nodes"`
}

// GetNodes returns AllAppsOrganizationAppsAppConnection.Nodes, and is useful for accessing the field via an interface.
func (v *AllAppsOrganizationAppsAppConnection) GetNodes() []AllAppsOrganizationAppsAppConnectionNodesApp {
	return v.Nodes
}

// AllAppsOrganizationAppsAppConnectionNodesApp includes the requested fields of the GraphQL type App.
type AllAppsOrganizationAppsAppConnectionNodesApp struct {
	// Unique application ID
	Id        string    `json:"id"`
	CreatedAt time.Time `json:"createdAt"`
}

// GetId returns AllAppsOrganizationAppsAppConnectionNodesApp.Id, and is useful for accessing the field via an interface.
func (v *AllAppsOrganizationAppsAppConnectionNodesApp) GetId() string { return v.Id }

// GetCreatedAt returns AllAppsOrganizationAppsAppConnectionNodesApp.CreatedAt, and is useful for accessing the field via an interface.
func (v *AllAppsOrganizationAppsAppConnectionNodesApp) GetCreatedAt() time.Time { return v.CreatedAt }

// AllAppsResponse is returned by AllApps on success.
type AllAppsResponse struct {
	// Find an organization by ID
	Organization AllAppsOrganization `json:"organization"`
}

// GetOrganization returns AllAppsResponse.Organization, and is useful for accessing the field via an interface.
func (v *AllAppsResponse) GetOrganization() AllAppsOrganization { return v.Organization }

// AppData includes the GraphQL fields of App requested by the fragment AppData.
type AppData struct {
	// Unique application ID
	Id string `json:"id"`
	// The unique application name
	Name     string `json:"name"`
	Deployed bool   `json:"deployed"`
	// Fly platform version
	PlatformVersion PlatformVersionEnum `json:"platformVersion"`
	// Secrets set on the application
	Secrets []AppDataSecretsSecret `json:"secrets"`
	// Organization that owns this app
	Organization AppDataOrganization `json:"organization"`
}

// GetId returns AppData.Id, and is useful for accessing the field via an interface.
func (v *AppData) GetId() string { return v.Id }

// GetName returns AppData.Name, and is useful for accessing the field via an interface.
func (v *AppData) GetName() string { return v.Name }

// GetDeployed returns AppData.Deployed, and is useful for accessing the field via an interface.
func (v *AppData) GetDeployed() bool { return v.Deployed }

// GetPlatformVersion returns AppData.PlatformVersion, and is useful for accessing the field via an interface.
func (v *AppData) GetPlatformVersion() PlatformVersionEnum { return v.PlatformVersion }

// GetSecrets returns AppData.Secrets, and is useful for accessing the field via an interface.
func (v *AppData) GetSecrets() []AppDataSecretsSecret { return v.Secrets }

// GetOrganization returns AppData.Organization, and is useful for accessing the field via an interface.
func (v *AppData) GetOrganization() AppDataOrganization { return v.Organization }

// AppDataOrganization includes the requested fields of the GraphQL type Organization.
type AppDataOrganization struct {
	OrganizationData `json:"-"`
}

// GetId returns AppDataOrganization.Id, and is useful for accessing the field via an interface.
func (v *AppDataOrganization) GetId() string { return v.OrganizationData.Id }

// GetSlug returns AppDataOrganization.Slug, and is useful for accessing the field via an interface.
func (v *AppDataOrganization) GetSlug() string { return v.OrganizationData.Slug }

// GetRawSlug returns AppDataOrganization.RawSlug, and is useful for accessing the field via an interface.
func (v *AppDataOrganization) GetRawSlug() string { return v.OrganizationData.RawSlug }

// GetPaidPlan returns AppDataOrganization.PaidPlan, and is useful for accessing the field via an interface.
func (v *AppDataOrganization) GetPaidPlan() bool { return v.OrganizationData.PaidPlan }

// GetAddOnSsoLink returns AppDataOrganization.AddOnSsoLink, and is useful for accessing the field via an interface.
func (v *AppDataOrganization) GetAddOnSsoLink() string { return v.OrganizationData.AddOnSsoLink }

// GetProvisionsBetaExtensions returns AppDataOrganization.ProvisionsBetaExtensions, and is useful for accessing the field via an interface.
func (v *AppDataOrganization) GetProvisionsBetaExtensions() bool {
	return v.OrganizationData.ProvisionsBetaExtensions
}

func (v *AppDataOrganization) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*AppDataOrganization
		graphql.NoUnmarshalJSON
	}
	firstPass.AppDataOrganization = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	err = json.Unmarshal(
		b, &v.OrganizationData)
	if err != nil {
		return err
	}
	return nil
}

type __premarshalAppDataOrganization struct {
	Id string `json:"id"`

	Slug string `json:"slug"`

	RawSlug string `json:"rawSlug"`

	PaidPlan bool `json:"paidPlan"`

	AddOnSsoLink string `json:"addOnSsoLink"`

	ProvisionsBetaExtensions bool `json:"provisionsBetaExtensions"`
}

func (v *AppDataOrganization) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *AppDataOrganization) __premarshalJSON() (*__premarshalAppDataOrganization, error) {
	var retval __premarshalAppDataOrganization

	retval.Id = v.OrganizationData.Id
	retval.Slug = v.OrganizationData.Slug
	retval.RawSlug = v.OrganizationData.RawSlug
	retval.PaidPlan = v.OrganizationData.PaidPlan
	retval.AddOnSsoLink = v.OrganizationData.AddOnSsoLink
	retval.ProvisionsBetaExtensions = v.OrganizationData.ProvisionsBetaExtensions
	return &retval, nil
}

// AppDataSecretsSecret includes the requested fields of the GraphQL type Secret.
type AppDataSecretsSecret struct {
	// The name of the secret
	Name string `json:"name"`
}

// GetName returns AppDataSecretsSecret.Name, and is useful for accessing the field via an interface.
func (v *AppDataSecretsSecret) GetName() string { return v.Name }

// CreateAddOnCreateAddOnCreateAddOnPayload includes the requested fields of the GraphQL type CreateAddOnPayload.
// The GraphQL type's documentation follows.
//
// Autogenerated return type of CreateAddOn.
type CreateAddOnCreateAddOnCreateAddOnPayload struct {
	AddOn CreateAddOnCreateAddOnCreateAddOnPayloadAddOn `json:"addOn"`
}

// GetAddOn returns CreateAddOnCreateAddOnCreateAddOnPayload.AddOn, and is useful for accessing the field via an interface.
func (v *CreateAddOnCreateAddOnCreateAddOnPayload) GetAddOn() CreateAddOnCreateAddOnCreateAddOnPayloadAddOn {
	return v.AddOn
}

// CreateAddOnCreateAddOnCreateAddOnPayloadAddOn includes the requested fields of the GraphQL type AddOn.
type CreateAddOnCreateAddOnCreateAddOnPayloadAddOn struct {
	// The service name according to the provider
	Name string `json:"name"`
	// Public URL for this service
	PublicUrl string `json:"publicUrl"`
	// Single sign-on link to the add-on dashboard
	SsoLink string `json:"ssoLink"`
	// Environment variables for the add-on
	Environment interface{} `json:"environment"`
	// Region where the primary instance is deployed
	PrimaryRegion string `json:"primaryRegion"`
}

// GetName returns CreateAddOnCreateAddOnCreateAddOnPayloadAddOn.Name, and is useful for accessing the field via an interface.
func (v *CreateAddOnCreateAddOnCreateAddOnPayloadAddOn) GetName() string { return v.Name }

// GetPublicUrl returns CreateAddOnCreateAddOnCreateAddOnPayloadAddOn.PublicUrl, and is useful for accessing the field via an interface.
func (v *CreateAddOnCreateAddOnCreateAddOnPayloadAddOn) GetPublicUrl() string { return v.PublicUrl }

// GetSsoLink returns CreateAddOnCreateAddOnCreateAddOnPayloadAddOn.SsoLink, and is useful for accessing the field via an interface.
func (v *CreateAddOnCreateAddOnCreateAddOnPayloadAddOn) GetSsoLink() string { return v.SsoLink }

// GetEnvironment returns CreateAddOnCreateAddOnCreateAddOnPayloadAddOn.Environment, and is useful for accessing the field via an interface.
func (v *CreateAddOnCreateAddOnCreateAddOnPayloadAddOn) GetEnvironment() interface{} {
	return v.Environment
}

// GetPrimaryRegion returns CreateAddOnCreateAddOnCreateAddOnPayloadAddOn.PrimaryRegion, and is useful for accessing the field via an interface.
func (v *CreateAddOnCreateAddOnCreateAddOnPayloadAddOn) GetPrimaryRegion() string {
	return v.PrimaryRegion
}

// Autogenerated input type of CreateAddOn
type CreateAddOnInput struct {
	// An optional application ID to attach the add-on to after provisioning
	AppId string `json:"appId"`
	// A unique identifier for the client performing the mutation.
	ClientMutationId string `json:"clientMutationId"`
	// An optional name for the add-on
	Name string `json:"name"`
	// Options specific to the add-on
	Options interface{} `json:"options"`
	// The organization which owns the add-on
	OrganizationId string `json:"organizationId"`
	// A provider organization plan to set along with provisioning
	OrganizationPlanId string `json:"organizationPlanId"`
	// The add-on plan ID
	PlanId string `json:"planId"`
	// Desired primary region for the add-on
	PrimaryRegion string `json:"primaryRegion"`
	// Desired regions to place replicas in
	ReadRegions []string `json:"readRegions"`
	// The add-on type to provision
	Type AddOnType `json:"type"`
}

// GetAppId returns CreateAddOnInput.AppId, and is useful for accessing the field via an interface.
func (v *CreateAddOnInput) GetAppId() string { return v.AppId }

// GetClientMutationId returns CreateAddOnInput.ClientMutationId, and is useful for accessing the field via an interface.
func (v *CreateAddOnInput) GetClientMutationId() string { return v.ClientMutationId }

// GetName returns CreateAddOnInput.Name, and is useful for accessing the field via an interface.
func (v *CreateAddOnInput) GetName() string { return v.Name }

// GetOptions returns CreateAddOnInput.Options, and is useful for accessing the field via an interface.
func (v *CreateAddOnInput) GetOptions() interface{} { return v.Options }

// GetOrganizationId returns CreateAddOnInput.OrganizationId, and is useful for accessing the field via an interface.
func (v *CreateAddOnInput) GetOrganizationId() string { return v.OrganizationId }

// GetOrganizationPlanId returns CreateAddOnInput.OrganizationPlanId, and is useful for accessing the field via an interface.
func (v *CreateAddOnInput) GetOrganizationPlanId() string { return v.OrganizationPlanId }

// GetPlanId returns CreateAddOnInput.PlanId, and is useful for accessing the field via an interface.
func (v *CreateAddOnInput) GetPlanId() string { return v.PlanId }

// GetPrimaryRegion returns CreateAddOnInput.PrimaryRegion, and is useful for accessing the field via an interface.
func (v *CreateAddOnInput) GetPrimaryRegion() string { return v.PrimaryRegion }

// GetReadRegions returns CreateAddOnInput.ReadRegions, and is useful for accessing the field via an interface.
func (v *CreateAddOnInput) GetReadRegions() []string { return v.ReadRegions }

// GetType returns CreateAddOnInput.Type, and is useful for accessing the field via an interface.
func (v *CreateAddOnInput) GetType() AddOnType { return v.Type }

// CreateAddOnResponse is returned by CreateAddOn on success.
type CreateAddOnResponse struct {
	CreateAddOn CreateAddOnCreateAddOnCreateAddOnPayload `json:"createAddOn"`
}

// GetCreateAddOn returns CreateAddOnResponse.CreateAddOn, and is useful for accessing the field via an interface.
func (v *CreateAddOnResponse) GetCreateAddOn() CreateAddOnCreateAddOnCreateAddOnPayload {
	return v.CreateAddOn
}

// CreateAppCreateAppCreateAppPayload includes the requested fields of the GraphQL type CreateAppPayload.
// The GraphQL type's documentation follows.
//
// Autogenerated return type of CreateApp.
type CreateAppCreateAppCreateAppPayload struct {
	App CreateAppCreateAppCreateAppPayloadApp `json:"app"`
}

// GetApp returns CreateAppCreateAppCreateAppPayload.App, and is useful for accessing the field via an interface.
func (v *CreateAppCreateAppCreateAppPayload) GetApp() CreateAppCreateAppCreateAppPayloadApp {
	return v.App
}

// CreateAppCreateAppCreateAppPayloadApp includes the requested fields of the GraphQL type App.
type CreateAppCreateAppCreateAppPayloadApp struct {
	AppData `json:"-"`
	Config  CreateAppCreateAppCreateAppPayloadAppConfig          `json:"config"`
	Regions []CreateAppCreateAppCreateAppPayloadAppRegionsRegion `json:"regions"`
}

// GetConfig returns CreateAppCreateAppCreateAppPayloadApp.Config, and is useful for accessing the field via an interface.
func (v *CreateAppCreateAppCreateAppPayloadApp) GetConfig() CreateAppCreateAppCreateAppPayloadAppConfig {
	return v.Config
}

// GetRegions returns CreateAppCreateAppCreateAppPayloadApp.Regions, and is useful for accessing the field via an interface.
func (v *CreateAppCreateAppCreateAppPayloadApp) GetRegions() []CreateAppCreateAppCreateAppPayloadAppRegionsRegion {
	return v.Regions
}

// GetId returns CreateAppCreateAppCreateAppPayloadApp.Id, and is useful for accessing the field via an interface.
func (v *CreateAppCreateAppCreateAppPayloadApp) GetId() string { return v.AppData.Id }

// GetName returns CreateAppCreateAppCreateAppPayloadApp.Name, and is useful for accessing the field via an interface.
func (v *CreateAppCreateAppCreateAppPayloadApp) GetName() string { return v.AppData.Name }

// GetDeployed returns CreateAppCreateAppCreateAppPayloadApp.Deployed, and is useful for accessing the field via an interface.
func (v *CreateAppCreateAppCreateAppPayloadApp) GetDeployed() bool { return v.AppData.Deployed }

// GetPlatformVersion returns CreateAppCreateAppCreateAppPayloadApp.PlatformVersion, and is useful for accessing the field via an interface.
func (v *CreateAppCreateAppCreateAppPayloadApp) GetPlatformVersion() PlatformVersionEnum {
	return v.AppData.PlatformVersion
}

// GetSecrets returns CreateAppCreateAppCreateAppPayloadApp.Secrets, and is useful for accessing the field via an interface.
func (v *CreateAppCreateAppCreateAppPayloadApp) GetSecrets() []AppDataSecretsSecret {
	return v.AppData.Secrets
}

// GetOrganization returns CreateAppCreateAppCreateAppPayloadApp.Organization, and is useful for accessing the field via an interface.
func (v *CreateAppCreateAppCreateAppPayloadApp) GetOrganization() AppDataOrganization {
	return v.AppData.Organization
}

func (v *CreateAppCreateAppCreateAppPayloadApp) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*CreateAppCreateAppCreateAppPayloadApp
		graphql.NoUnmarshalJSON
	}
	firstPass.CreateAppCreateAppCreateAppPayloadApp = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	err = json.Unmarshal(
		b, &v.AppData)
	if err != nil {
		return err
	}
	return nil
}

type __premarshalCreateAppCreateAppCreateAppPayloadApp struct {
	Config CreateAppCreateAppCreateAppPayloadAppConfig `json:"config"`

	Regions []CreateAppCreateAppCreateAppPayloadAppRegionsRegion `json:"regions"`

	Id string `json:"id"`

	Name string `json:"name"`

	Deployed bool `json:"deployed"`

	PlatformVersion PlatformVersionEnum `json:"platformVersion"`

	Secrets []AppDataSecretsSecret `json:"secrets"`

	Organization AppDataOrganization `json:"organization"`
}

func (v *CreateAppCreateAppCreateAppPayloadApp) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *CreateAppCreateAppCreateAppPayloadApp) __premarshalJSON() (*__premarshalCreateAppCreateAppCreateAppPayloadApp, error) {
	var retval __premarshalCreateAppCreateAppCreateAppPayloadApp

	retval.Config = v.Config
	retval.Regions = v.Regions
	retval.Id = v.AppData.Id
	retval.Name = v.AppData.Name
	retval.Deployed = v.AppData.Deployed
	retval.PlatformVersion = v.AppData.PlatformVersion
	retval.Secrets = v.AppData.Secrets
	retval.Organization = v.AppData.Organization
	return &retval, nil
}

// CreateAppCreateAppCreateAppPayloadAppConfig includes the requested fields of the GraphQL type AppConfig.
type CreateAppCreateAppCreateAppPayloadAppConfig struct {
	Definition interface{} `json:"definition"`
}

// GetDefinition returns CreateAppCreateAppCreateAppPayloadAppConfig.Definition, and is useful for accessing the field via an interface.
func (v *CreateAppCreateAppCreateAppPayloadAppConfig) GetDefinition() interface{} {
	return v.Definition
}

// CreateAppCreateAppCreateAppPayloadAppRegionsRegion includes the requested fields of the GraphQL type Region.
type CreateAppCreateAppCreateAppPayloadAppRegionsRegion struct {
	// The name of this region
	Name string `json:"name"`
	// The IATA airport code for this region
	Code string `json:"code"`
}

// GetName returns CreateAppCreateAppCreateAppPayloadAppRegionsRegion.Name, and is useful for accessing the field via an interface.
func (v *CreateAppCreateAppCreateAppPayloadAppRegionsRegion) GetName() string { return v.Name }

// GetCode returns CreateAppCreateAppCreateAppPayloadAppRegionsRegion.Code, and is useful for accessing the field via an interface.
func (v *CreateAppCreateAppCreateAppPayloadAppRegionsRegion) GetCode() string { return v.Code }

// Autogenerated input type of CreateApp
type CreateAppInput struct {
	AppRoleId string `json:"appRoleId"`
	// A unique identifier for the client performing the mutation.
	ClientMutationId string `json:"clientMutationId"`
	EnableSubdomains bool   `json:"enableSubdomains"`
	Heroku           bool   `json:"heroku"`
	Machines         bool   `json:"machines"`
	// The name of the new application. Defaults to a random name.
	Name    string `json:"name"`
	Network string `json:"network"`
	// The node ID of the organization
	OrganizationId  string `json:"organizationId"`
	PreferredRegion string `json:"preferredRegion"`
	// The application runtime
	Runtime RuntimeType `json:"runtime"`
}

// GetAppRoleId returns CreateAppInput.AppRoleId, and is useful for accessing the field via an interface.
func (v *CreateAppInput) GetAppRoleId() string { return v.AppRoleId }

// GetClientMutationId returns CreateAppInput.ClientMutationId, and is useful for accessing the field via an interface.
func (v *CreateAppInput) GetClientMutationId() string { return v.ClientMutationId }

// GetEnableSubdomains returns CreateAppInput.EnableSubdomains, and is useful for accessing the field via an interface.
func (v *CreateAppInput) GetEnableSubdomains() bool { return v.EnableSubdomains }

// GetHeroku returns CreateAppInput.Heroku, and is useful for accessing the field via an interface.
func (v *CreateAppInput) GetHeroku() bool { return v.Heroku }

// GetMachines returns CreateAppInput.Machines, and is useful for accessing the field via an interface.
func (v *CreateAppInput) GetMachines() bool { return v.Machines }

// GetName returns CreateAppInput.Name, and is useful for accessing the field via an interface.
func (v *CreateAppInput) GetName() string { return v.Name }

// GetNetwork returns CreateAppInput.Network, and is useful for accessing the field via an interface.
func (v *CreateAppInput) GetNetwork() string { return v.Network }

// GetOrganizationId returns CreateAppInput.OrganizationId, and is useful for accessing the field via an interface.
func (v *CreateAppInput) GetOrganizationId() string { return v.OrganizationId }

// GetPreferredRegion returns CreateAppInput.PreferredRegion, and is useful for accessing the field via an interface.
func (v *CreateAppInput) GetPreferredRegion() string { return v.PreferredRegion }

// GetRuntime returns CreateAppInput.Runtime, and is useful for accessing the field via an interface.
func (v *CreateAppInput) GetRuntime() RuntimeType { return v.Runtime }

// CreateAppResponse is returned by CreateApp on success.
type CreateAppResponse struct {
	CreateApp CreateAppCreateAppCreateAppPayload `json:"createApp"`
}

// GetCreateApp returns CreateAppResponse.CreateApp, and is useful for accessing the field via an interface.
func (v *CreateAppResponse) GetCreateApp() CreateAppCreateAppCreateAppPayload { return v.CreateApp }

// CreateExtensionCreateAddOnCreateAddOnPayload includes the requested fields of the GraphQL type CreateAddOnPayload.
// The GraphQL type's documentation follows.
//
// Autogenerated return type of CreateAddOn.
type CreateExtensionCreateAddOnCreateAddOnPayload struct {
	AddOn CreateExtensionCreateAddOnCreateAddOnPayloadAddOn `json:"addOn"`
}

// GetAddOn returns CreateExtensionCreateAddOnCreateAddOnPayload.AddOn, and is useful for accessing the field via an interface.
func (v *CreateExtensionCreateAddOnCreateAddOnPayload) GetAddOn() CreateExtensionCreateAddOnCreateAddOnPayloadAddOn {
	return v.AddOn
}

// CreateExtensionCreateAddOnCreateAddOnPayloadAddOn includes the requested fields of the GraphQL type AddOn.
type CreateExtensionCreateAddOnCreateAddOnPayloadAddOn struct {
	ExtensionData `json:"-"`
}

// GetName returns CreateExtensionCreateAddOnCreateAddOnPayloadAddOn.Name, and is useful for accessing the field via an interface.
func (v *CreateExtensionCreateAddOnCreateAddOnPayloadAddOn) GetName() string {
	return v.ExtensionData.Name
}

// GetSsoLink returns CreateExtensionCreateAddOnCreateAddOnPayloadAddOn.SsoLink, and is useful for accessing the field via an interface.
func (v *CreateExtensionCreateAddOnCreateAddOnPayloadAddOn) GetSsoLink() string {
	return v.ExtensionData.SsoLink
}

// GetEnvironment returns CreateExtensionCreateAddOnCreateAddOnPayloadAddOn.Environment, and is useful for accessing the field via an interface.
func (v *CreateExtensionCreateAddOnCreateAddOnPayloadAddOn) GetEnvironment() interface{} {
	return v.ExtensionData.Environment
}

// GetPrimaryRegion returns CreateExtensionCreateAddOnCreateAddOnPayloadAddOn.PrimaryRegion, and is useful for accessing the field via an interface.
func (v *CreateExtensionCreateAddOnCreateAddOnPayloadAddOn) GetPrimaryRegion() string {
	return v.ExtensionData.PrimaryRegion
}

func (v *CreateExtensionCreateAddOnCreateAddOnPayloadAddOn) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*CreateExtensionCreateAddOnCreateAddOnPayloadAddOn
		graphql.NoUnmarshalJSON
	}
	firstPass.CreateExtensionCreateAddOnCreateAddOnPayloadAddOn = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	err = json.Unmarshal(
		b, &v.ExtensionData)
	if err != nil {
		return err
	}
	return nil
}

type __premarshalCreateExtensionCreateAddOnCreateAddOnPayloadAddOn struct {
	Name string `json:"name"`

	SsoLink string `json:"ssoLink"`

	Environment interface{} `json:"environment"`

	PrimaryRegion string `json:"primaryRegion"`
}

func (v *CreateExtensionCreateAddOnCreateAddOnPayloadAddOn) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *CreateExtensionCreateAddOnCreateAddOnPayloadAddOn) __premarshalJSON() (*__premarshalCreateExtensionCreateAddOnCreateAddOnPayloadAddOn, error) {
	var retval __premarshalCreateExtensionCreateAddOnCreateAddOnPayloadAddOn

	retval.Name = v.ExtensionData.Name
	retval.SsoLink = v.ExtensionData.SsoLink
	retval.Environment = v.ExtensionData.Environment
	retval.PrimaryRegion = v.ExtensionData.PrimaryRegion
	return &retval, nil
}

// CreateExtensionResponse is returned by CreateExtension on success.
type CreateExtensionResponse struct {
	CreateAddOn CreateExtensionCreateAddOnCreateAddOnPayload `json:"createAddOn"`
}

// GetCreateAddOn returns CreateExtensionResponse.CreateAddOn, and is useful for accessing the field via an interface.
func (v *CreateExtensionResponse) GetCreateAddOn() CreateExtensionCreateAddOnCreateAddOnPayload {
	return v.CreateAddOn
}

// CreateLimitedAccessTokenCreateLimitedAccessTokenCreateLimitedAccessTokenPayload includes the requested fields of the GraphQL type CreateLimitedAccessTokenPayload.
// The GraphQL type's documentation follows.
//
// Autogenerated return type of CreateLimitedAccessToken.
type CreateLimitedAccessTokenCreateLimitedAccessTokenCreateLimitedAccessTokenPayload struct {
	LimitedAccessToken CreateLimitedAccessTokenCreateLimitedAccessTokenCreateLimitedAccessTokenPayloadLimitedAccessToken `json:"limitedAccessToken"`
}

// GetLimitedAccessToken returns CreateLimitedAccessTokenCreateLimitedAccessTokenCreateLimitedAccessTokenPayload.LimitedAccessToken, and is useful for accessing the field via an interface.
func (v *CreateLimitedAccessTokenCreateLimitedAccessTokenCreateLimitedAccessTokenPayload) GetLimitedAccessToken() CreateLimitedAccessTokenCreateLimitedAccessTokenCreateLimitedAccessTokenPayloadLimitedAccessToken {
	return v.LimitedAccessToken
}

// CreateLimitedAccessTokenCreateLimitedAccessTokenCreateLimitedAccessTokenPayloadLimitedAccessToken includes the requested fields of the GraphQL type LimitedAccessToken.
type CreateLimitedAccessTokenCreateLimitedAccessTokenCreateLimitedAccessTokenPayloadLimitedAccessToken struct {
	TokenHeader string `json:"tokenHeader"`
}

// GetTokenHeader returns CreateLimitedAccessTokenCreateLimitedAccessTokenCreateLimitedAccessTokenPayloadLimitedAccessToken.TokenHeader, and is useful for accessing the field via an interface.
func (v *CreateLimitedAccessTokenCreateLimitedAccessTokenCreateLimitedAccessTokenPayloadLimitedAccessToken) GetTokenHeader() string {
	return v.TokenHeader
}

// CreateLimitedAccessTokenResponse is returned by CreateLimitedAccessToken on success.
type CreateLimitedAccessTokenResponse struct {
	CreateLimitedAccessToken CreateLimitedAccessTokenCreateLimitedAccessTokenCreateLimitedAccessTokenPayload `json:"createLimitedAccessToken"`
}

// GetCreateLimitedAccessToken returns CreateLimitedAccessTokenResponse.CreateLimitedAccessToken, and is useful for accessing the field via an interface.
func (v *CreateLimitedAccessTokenResponse) GetCreateLimitedAccessToken() CreateLimitedAccessTokenCreateLimitedAccessTokenCreateLimitedAccessTokenPayload {
	return v.CreateLimitedAccessToken
}

// CreateTosAgreementCreateExtensionTosAgreementCreateExtensionTosAgreementPayload includes the requested fields of the GraphQL type CreateExtensionTosAgreementPayload.
// The GraphQL type's documentation follows.
//
// Autogenerated return type of CreateExtensionTosAgreement.
type CreateTosAgreementCreateExtensionTosAgreementCreateExtensionTosAgreementPayload struct {
	// A unique identifier for the client performing the mutation.
	ClientMutationId string `json:"clientMutationId"`
}

// GetClientMutationId returns CreateTosAgreementCreateExtensionTosAgreementCreateExtensionTosAgreementPayload.ClientMutationId, and is useful for accessing the field via an interface.
func (v *CreateTosAgreementCreateExtensionTosAgreementCreateExtensionTosAgreementPayload) GetClientMutationId() string {
	return v.ClientMutationId
}

// CreateTosAgreementResponse is returned by CreateTosAgreement on success.
type CreateTosAgreementResponse struct {
	CreateExtensionTosAgreement CreateTosAgreementCreateExtensionTosAgreementCreateExtensionTosAgreementPayload `json:"createExtensionTosAgreement"`
}

// GetCreateExtensionTosAgreement returns CreateTosAgreementResponse.CreateExtensionTosAgreement, and is useful for accessing the field via an interface.
func (v *CreateTosAgreementResponse) GetCreateExtensionTosAgreement() CreateTosAgreementCreateExtensionTosAgreementCreateExtensionTosAgreementPayload {
	return v.CreateExtensionTosAgreement
}

// DeleteAddOnDeleteAddOnDeleteAddOnPayload includes the requested fields of the GraphQL type DeleteAddOnPayload.
// The GraphQL type's documentation follows.
//
// Autogenerated return type of DeleteAddOn.
type DeleteAddOnDeleteAddOnDeleteAddOnPayload struct {
	DeletedAddOnName string `json:"deletedAddOnName"`
}

// GetDeletedAddOnName returns DeleteAddOnDeleteAddOnDeleteAddOnPayload.DeletedAddOnName, and is useful for accessing the field via an interface.
func (v *DeleteAddOnDeleteAddOnDeleteAddOnPayload) GetDeletedAddOnName() string {
	return v.DeletedAddOnName
}

// DeleteAddOnResponse is returned by DeleteAddOn on success.
type DeleteAddOnResponse struct {
	DeleteAddOn DeleteAddOnDeleteAddOnDeleteAddOnPayload `json:"deleteAddOn"`
}

// GetDeleteAddOn returns DeleteAddOnResponse.DeleteAddOn, and is useful for accessing the field via an interface.
func (v *DeleteAddOnResponse) GetDeleteAddOn() DeleteAddOnDeleteAddOnDeleteAddOnPayload {
	return v.DeleteAddOn
}

// ExtensionData includes the GraphQL fields of AddOn requested by the fragment ExtensionData.
type ExtensionData struct {
	// The service name according to the provider
	Name string `json:"name"`
	// Single sign-on link to the add-on dashboard
	SsoLink string `json:"ssoLink"`
	// Environment variables for the add-on
	Environment interface{} `json:"environment"`
	// Region where the primary instance is deployed
	PrimaryRegion string `json:"primaryRegion"`
}

// GetName returns ExtensionData.Name, and is useful for accessing the field via an interface.
func (v *ExtensionData) GetName() string { return v.Name }

// GetSsoLink returns ExtensionData.SsoLink, and is useful for accessing the field via an interface.
func (v *ExtensionData) GetSsoLink() string { return v.SsoLink }

// GetEnvironment returns ExtensionData.Environment, and is useful for accessing the field via an interface.
func (v *ExtensionData) GetEnvironment() interface{} { return v.Environment }

// GetPrimaryRegion returns ExtensionData.PrimaryRegion, and is useful for accessing the field via an interface.
func (v *ExtensionData) GetPrimaryRegion() string { return v.PrimaryRegion }

// ExtensionProviderData includes the GraphQL fields of AddOnProvider requested by the fragment ExtensionProviderData.
type ExtensionProviderData struct {
	Id                       string                                       `json:"id"`
	Name                     string                                       `json:"name"`
	DisplayName              string                                       `json:"displayName"`
	TosUrl                   string                                       `json:"tosUrl"`
	AsyncProvisioning        bool                                         `json:"asyncProvisioning"`
	AutoProvision            bool                                         `json:"autoProvision"`
	SelectName               bool                                         `json:"selectName"`
	SelectRegion             bool                                         `json:"selectRegion"`
	SelectReplicaRegions     bool                                         `json:"selectReplicaRegions"`
	DetectPlatform           bool                                         `json:"detectPlatform"`
	ResourceName             string                                       `json:"resourceName"`
	NameSuffix               string                                       `json:"nameSuffix"`
	Beta                     bool                                         `json:"beta"`
	TosAgreement             string                                       `json:"tosAgreement"`
	Internal                 bool                                         `json:"internal"`
	ProvisioningInstructions string                                       `json:"provisioningInstructions"`
	ExcludedRegions          []ExtensionProviderDataExcludedRegionsRegion `json:"excludedRegions"`
}

// GetId returns ExtensionProviderData.Id, and is useful for accessing the field via an interface.
func (v *ExtensionProviderData) GetId() string { return v.Id }

// GetName returns ExtensionProviderData.Name, and is useful for accessing the field via an interface.
func (v *ExtensionProviderData) GetName() string { return v.Name }

// GetDisplayName returns ExtensionProviderData.DisplayName, and is useful for accessing the field via an interface.
func (v *ExtensionProviderData) GetDisplayName() string { return v.DisplayName }

// GetTosUrl returns ExtensionProviderData.TosUrl, and is useful for accessing the field via an interface.
func (v *ExtensionProviderData) GetTosUrl() string { return v.TosUrl }

// GetAsyncProvisioning returns ExtensionProviderData.AsyncProvisioning, and is useful for accessing the field via an interface.
func (v *ExtensionProviderData) GetAsyncProvisioning() bool { return v.AsyncProvisioning }

// GetAutoProvision returns ExtensionProviderData.AutoProvision, and is useful for accessing the field via an interface.
func (v *ExtensionProviderData) GetAutoProvision() bool { return v.AutoProvision }

// GetSelectName returns ExtensionProviderData.SelectName, and is useful for accessing the field via an interface.
func (v *ExtensionProviderData) GetSelectName() bool { return v.SelectName }

// GetSelectRegion returns ExtensionProviderData.SelectRegion, and is useful for accessing the field via an interface.
func (v *ExtensionProviderData) GetSelectRegion() bool { return v.SelectRegion }

// GetSelectReplicaRegions returns ExtensionProviderData.SelectReplicaRegions, and is useful for accessing the field via an interface.
func (v *ExtensionProviderData) GetSelectReplicaRegions() bool { return v.SelectReplicaRegions }

// GetDetectPlatform returns ExtensionProviderData.DetectPlatform, and is useful for accessing the field via an interface.
func (v *ExtensionProviderData) GetDetectPlatform() bool { return v.DetectPlatform }

// GetResourceName returns ExtensionProviderData.ResourceName, and is useful for accessing the field via an interface.
func (v *ExtensionProviderData) GetResourceName() string { return v.ResourceName }

// GetNameSuffix returns ExtensionProviderData.NameSuffix, and is useful for accessing the field via an interface.
func (v *ExtensionProviderData) GetNameSuffix() string { return v.NameSuffix }

// GetBeta returns ExtensionProviderData.Beta, and is useful for accessing the field via an interface.
func (v *ExtensionProviderData) GetBeta() bool { return v.Beta }

// GetTosAgreement returns ExtensionProviderData.TosAgreement, and is useful for accessing the field via an interface.
func (v *ExtensionProviderData) GetTosAgreement() string { return v.TosAgreement }

// GetInternal returns ExtensionProviderData.Internal, and is useful for accessing the field via an interface.
func (v *ExtensionProviderData) GetInternal() bool { return v.Internal }

// GetProvisioningInstructions returns ExtensionProviderData.ProvisioningInstructions, and is useful for accessing the field via an interface.
func (v *ExtensionProviderData) GetProvisioningInstructions() string {
	return v.ProvisioningInstructions
}

// GetExcludedRegions returns ExtensionProviderData.ExcludedRegions, and is useful for accessing the field via an interface.
func (v *ExtensionProviderData) GetExcludedRegions() []ExtensionProviderDataExcludedRegionsRegion {
	return v.ExcludedRegions
}

// ExtensionProviderDataExcludedRegionsRegion includes the requested fields of the GraphQL type Region.
type ExtensionProviderDataExcludedRegionsRegion struct {
	// The IATA airport code for this region
	Code string `json:"code"`
}

// GetCode returns ExtensionProviderDataExcludedRegionsRegion.Code, and is useful for accessing the field via an interface.
func (v *ExtensionProviderDataExcludedRegionsRegion) GetCode() string { return v.Code }

// FlyctlConfigCurrentReleaseApp includes the requested fields of the GraphQL type App.
type FlyctlConfigCurrentReleaseApp struct {
	// The latest release of this application, without any config processing
	CurrentReleaseUnprocessed FlyctlConfigCurrentReleaseAppCurrentReleaseUnprocessed `json:"currentReleaseUnprocessed"`
}

// GetCurrentReleaseUnprocessed returns FlyctlConfigCurrentReleaseApp.CurrentReleaseUnprocessed, and is useful for accessing the field via an interface.
func (v *FlyctlConfigCurrentReleaseApp) GetCurrentReleaseUnprocessed() FlyctlConfigCurrentReleaseAppCurrentReleaseUnprocessed {
	return v.CurrentReleaseUnprocessed
}

// FlyctlConfigCurrentReleaseAppCurrentReleaseUnprocessed includes the requested fields of the GraphQL type ReleaseUnprocessed.
type FlyctlConfigCurrentReleaseAppCurrentReleaseUnprocessed struct {
	ConfigDefinition interface{} `json:"configDefinition"`
}

// GetConfigDefinition returns FlyctlConfigCurrentReleaseAppCurrentReleaseUnprocessed.ConfigDefinition, and is useful for accessing the field via an interface.
func (v *FlyctlConfigCurrentReleaseAppCurrentReleaseUnprocessed) GetConfigDefinition() interface{} {
	return v.ConfigDefinition
}

// FlyctlConfigCurrentReleaseResponse is returned by FlyctlConfigCurrentRelease on success.
type FlyctlConfigCurrentReleaseResponse struct {
	// Find an app by name
	App FlyctlConfigCurrentReleaseApp `json:"app"`
}

// GetApp returns FlyctlConfigCurrentReleaseResponse.App, and is useful for accessing the field via an interface.
func (v *FlyctlConfigCurrentReleaseResponse) GetApp() FlyctlConfigCurrentReleaseApp { return v.App }

// GetAddOnAddOn includes the requested fields of the GraphQL type AddOn.
type GetAddOnAddOn struct {
	AddOnData `json:"-"`
	// Public URL for this service
	PublicUrl string `json:"publicUrl"`
	// Private flycast IP address of the add-on
	PrivateIp string `json:"privateIp"`
	// Password for the add-on
	Password string `json:"password"`
	// Status of the add-on
	Status string `json:"status"`
	// Region where the primary instance is deployed
	PrimaryRegion string `json:"primaryRegion"`
	// Regions where replica instances are deployed
	ReadRegions []string `json:"readRegions"`
	// Add-on options
	Options interface{} `json:"options"`
	// Add-on metadata
	Metadata interface{} `json:"metadata"`
	// Single sign-on link to the add-on dashboard
	SsoLink string `json:"ssoLink"`
	// Organization that owns this service
	Organization GetAddOnAddOnOrganization `json:"organization"`
	// The add-on provider
	AddOnProvider GetAddOnAddOnAddOnProvider `json:"addOnProvider"`
	// An app associated with this add-on
	App GetAddOnAddOnApp `json:"app"`
	// The add-on plan
	AddOnPlan GetAddOnAddOnAddOnPlan `json:"addOnPlan"`
}

// GetPublicUrl returns GetAddOnAddOn.PublicUrl, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOn) GetPublicUrl() string { return v.PublicUrl }

// GetPrivateIp returns GetAddOnAddOn.PrivateIp, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOn) GetPrivateIp() string { return v.PrivateIp }

// GetPassword returns GetAddOnAddOn.Password, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOn) GetPassword() string { return v.Password }

// GetStatus returns GetAddOnAddOn.Status, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOn) GetStatus() string { return v.Status }

// GetPrimaryRegion returns GetAddOnAddOn.PrimaryRegion, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOn) GetPrimaryRegion() string { return v.PrimaryRegion }

// GetReadRegions returns GetAddOnAddOn.ReadRegions, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOn) GetReadRegions() []string { return v.ReadRegions }

// GetOptions returns GetAddOnAddOn.Options, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOn) GetOptions() interface{} { return v.Options }

// GetMetadata returns GetAddOnAddOn.Metadata, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOn) GetMetadata() interface{} { return v.Metadata }

// GetSsoLink returns GetAddOnAddOn.SsoLink, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOn) GetSsoLink() string { return v.SsoLink }

// GetOrganization returns GetAddOnAddOn.Organization, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOn) GetOrganization() GetAddOnAddOnOrganization { return v.Organization }

// GetAddOnProvider returns GetAddOnAddOn.AddOnProvider, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOn) GetAddOnProvider() GetAddOnAddOnAddOnProvider { return v.AddOnProvider }

// GetApp returns GetAddOnAddOn.App, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOn) GetApp() GetAddOnAddOnApp { return v.App }

// GetAddOnPlan returns GetAddOnAddOn.AddOnPlan, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOn) GetAddOnPlan() GetAddOnAddOnAddOnPlan { return v.AddOnPlan }

// GetId returns GetAddOnAddOn.Id, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOn) GetId() string { return v.AddOnData.Id }

// GetName returns GetAddOnAddOn.Name, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOn) GetName() string { return v.AddOnData.Name }

// GetErrorMessage returns GetAddOnAddOn.ErrorMessage, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOn) GetErrorMessage() string { return v.AddOnData.ErrorMessage }

func (v *GetAddOnAddOn) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*GetAddOnAddOn
		graphql.NoUnmarshalJSON
	}
	firstPass.GetAddOnAddOn = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	err = json.Unmarshal(
		b, &v.AddOnData)
	if err != nil {
		return err
	}
	return nil
}

type __premarshalGetAddOnAddOn struct {
	PublicUrl string `json:"publicUrl"`

	PrivateIp string `json:"privateIp"`

	Password string `json:"password"`

	Status string `json:"status"`

	PrimaryRegion string `json:"primaryRegion"`

	ReadRegions []string `json:"readRegions"`

	Options interface{} `json:"options"`

	Metadata interface{} `json:"metadata"`

	SsoLink string `json:"ssoLink"`

	Organization GetAddOnAddOnOrganization `json:"organization"`

	AddOnProvider GetAddOnAddOnAddOnProvider `json:"addOnProvider"`

	App GetAddOnAddOnApp `json:"app"`

	AddOnPlan GetAddOnAddOnAddOnPlan `json:"addOnPlan"`

	Id string `json:"id"`

	Name string `json:"name"`

	ErrorMessage string `json:"errorMessage"`
}

func (v *GetAddOnAddOn) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *GetAddOnAddOn) __premarshalJSON() (*__premarshalGetAddOnAddOn, error) {
	var retval __premarshalGetAddOnAddOn

	retval.PublicUrl = v.PublicUrl
	retval.PrivateIp = v.PrivateIp
	retval.Password = v.Password
	retval.Status = v.Status
	retval.PrimaryRegion = v.PrimaryRegion
	retval.ReadRegions = v.ReadRegions
	retval.Options = v.Options
	retval.Metadata = v.Metadata
	retval.SsoLink = v.SsoLink
	retval.Organization = v.Organization
	retval.AddOnProvider = v.AddOnProvider
	retval.App = v.App
	retval.AddOnPlan = v.AddOnPlan
	retval.Id = v.AddOnData.Id
	retval.Name = v.AddOnData.Name
	retval.ErrorMessage = v.AddOnData.ErrorMessage
	return &retval, nil
}

// GetAddOnAddOnAddOnPlan includes the requested fields of the GraphQL type AddOnPlan.
type GetAddOnAddOnAddOnPlan struct {
	Id          string `json:"id"`
	Name        string `json:"name"`
	DisplayName string `json:"displayName"`
	Description string `json:"description"`
}

// GetId returns GetAddOnAddOnAddOnPlan.Id, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOnAddOnPlan) GetId() string { return v.Id }

// GetName returns GetAddOnAddOnAddOnPlan.Name, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOnAddOnPlan) GetName() string { return v.Name }

// GetDisplayName returns GetAddOnAddOnAddOnPlan.DisplayName, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOnAddOnPlan) GetDisplayName() string { return v.DisplayName }

// GetDescription returns GetAddOnAddOnAddOnPlan.Description, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOnAddOnPlan) GetDescription() string { return v.Description }

// GetAddOnAddOnAddOnProvider includes the requested fields of the GraphQL type AddOnProvider.
type GetAddOnAddOnAddOnProvider struct {
	ExtensionProviderData `json:"-"`
}

// GetId returns GetAddOnAddOnAddOnProvider.Id, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOnAddOnProvider) GetId() string { return v.ExtensionProviderData.Id }

// GetName returns GetAddOnAddOnAddOnProvider.Name, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOnAddOnProvider) GetName() string { return v.ExtensionProviderData.Name }

// GetDisplayName returns GetAddOnAddOnAddOnProvider.DisplayName, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOnAddOnProvider) GetDisplayName() string {
	return v.ExtensionProviderData.DisplayName
}

// GetTosUrl returns GetAddOnAddOnAddOnProvider.TosUrl, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOnAddOnProvider) GetTosUrl() string { return v.ExtensionProviderData.TosUrl }

// GetAsyncProvisioning returns GetAddOnAddOnAddOnProvider.AsyncProvisioning, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOnAddOnProvider) GetAsyncProvisioning() bool {
	return v.ExtensionProviderData.AsyncProvisioning
}

// GetAutoProvision returns GetAddOnAddOnAddOnProvider.AutoProvision, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOnAddOnProvider) GetAutoProvision() bool {
	return v.ExtensionProviderData.AutoProvision
}

// GetSelectName returns GetAddOnAddOnAddOnProvider.SelectName, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOnAddOnProvider) GetSelectName() bool { return v.ExtensionProviderData.SelectName }

// GetSelectRegion returns GetAddOnAddOnAddOnProvider.SelectRegion, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOnAddOnProvider) GetSelectRegion() bool {
	return v.ExtensionProviderData.SelectRegion
}

// GetSelectReplicaRegions returns GetAddOnAddOnAddOnProvider.SelectReplicaRegions, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOnAddOnProvider) GetSelectReplicaRegions() bool {
	return v.ExtensionProviderData.SelectReplicaRegions
}

// GetDetectPlatform returns GetAddOnAddOnAddOnProvider.DetectPlatform, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOnAddOnProvider) GetDetectPlatform() bool {
	return v.ExtensionProviderData.DetectPlatform
}

// GetResourceName returns GetAddOnAddOnAddOnProvider.ResourceName, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOnAddOnProvider) GetResourceName() string {
	return v.ExtensionProviderData.ResourceName
}

// GetNameSuffix returns GetAddOnAddOnAddOnProvider.NameSuffix, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOnAddOnProvider) GetNameSuffix() string {
	return v.ExtensionProviderData.NameSuffix
}

// GetBeta returns GetAddOnAddOnAddOnProvider.Beta, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOnAddOnProvider) GetBeta() bool { return v.ExtensionProviderData.Beta }

// GetTosAgreement returns GetAddOnAddOnAddOnProvider.TosAgreement, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOnAddOnProvider) GetTosAgreement() string {
	return v.ExtensionProviderData.TosAgreement
}

// GetInternal returns GetAddOnAddOnAddOnProvider.Internal, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOnAddOnProvider) GetInternal() bool { return v.ExtensionProviderData.Internal }

// GetProvisioningInstructions returns GetAddOnAddOnAddOnProvider.ProvisioningInstructions, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOnAddOnProvider) GetProvisioningInstructions() string {
	return v.ExtensionProviderData.ProvisioningInstructions
}

// GetExcludedRegions returns GetAddOnAddOnAddOnProvider.ExcludedRegions, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOnAddOnProvider) GetExcludedRegions() []ExtensionProviderDataExcludedRegionsRegion {
	return v.ExtensionProviderData.ExcludedRegions
}

func (v *GetAddOnAddOnAddOnProvider) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*GetAddOnAddOnAddOnProvider
		graphql.NoUnmarshalJSON
	}
	firstPass.GetAddOnAddOnAddOnProvider = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	err = json.Unmarshal(
		b, &v.ExtensionProviderData)
	if err != nil {
		return err
	}
	return nil
}

type __premarshalGetAddOnAddOnAddOnProvider struct {
	Id string `json:"id"`

	Name string `json:"name"`

	DisplayName string `json:"displayName"`

	TosUrl string `json:"tosUrl"`

	AsyncProvisioning bool `json:"asyncProvisioning"`

	AutoProvision bool `json:"autoProvision"`

	SelectName bool `json:"selectName"`

	SelectRegion bool `json:"selectRegion"`

	SelectReplicaRegions bool `json:"selectReplicaRegions"`

	DetectPlatform bool `json:"detectPlatform"`

	ResourceName string `json:"resourceName"`

	NameSuffix string `json:"nameSuffix"`

	Beta bool `json:"beta"`

	TosAgreement string `json:"tosAgreement"`

	Internal bool `json:"internal"`

	ProvisioningInstructions string `json:"provisioningInstructions"`

	ExcludedRegions []ExtensionProviderDataExcludedRegionsRegion `json:"excludedRegions"`
}

func (v *GetAddOnAddOnAddOnProvider) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *GetAddOnAddOnAddOnProvider) __premarshalJSON() (*__premarshalGetAddOnAddOnAddOnProvider, error) {
	var retval __premarshalGetAddOnAddOnAddOnProvider

	retval.Id = v.ExtensionProviderData.Id
	retval.Name = v.ExtensionProviderData.Name
	retval.DisplayName = v.ExtensionProviderData.DisplayName
	retval.TosUrl = v.ExtensionProviderData.TosUrl
	retval.AsyncProvisioning = v.ExtensionProviderData.AsyncProvisioning
	retval.AutoProvision = v.ExtensionProviderData.AutoProvision
	retval.SelectName = v.ExtensionProviderData.SelectName
	retval.SelectRegion = v.ExtensionProviderData.SelectRegion
	retval.SelectReplicaRegions = v.ExtensionProviderData.SelectReplicaRegions
	retval.DetectPlatform = v.ExtensionProviderData.DetectPlatform
	retval.ResourceName = v.ExtensionProviderData.ResourceName
	retval.NameSuffix = v.ExtensionProviderData.NameSuffix
	retval.Beta = v.ExtensionProviderData.Beta
	retval.TosAgreement = v.ExtensionProviderData.TosAgreement
	retval.Internal = v.ExtensionProviderData.Internal
	retval.ProvisioningInstructions = v.ExtensionProviderData.ProvisioningInstructions
	retval.ExcludedRegions = v.ExtensionProviderData.ExcludedRegions
	return &retval, nil
}

// GetAddOnAddOnApp includes the requested fields of the GraphQL type App.
type GetAddOnAddOnApp struct {
	AppData `json:"-"`
}

// GetId returns GetAddOnAddOnApp.Id, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOnApp) GetId() string { return v.AppData.Id }

// GetName returns GetAddOnAddOnApp.Name, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOnApp) GetName() string { return v.AppData.Name }

// GetDeployed returns GetAddOnAddOnApp.Deployed, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOnApp) GetDeployed() bool { return v.AppData.Deployed }

// GetPlatformVersion returns GetAddOnAddOnApp.PlatformVersion, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOnApp) GetPlatformVersion() PlatformVersionEnum { return v.AppData.PlatformVersion }

// GetSecrets returns GetAddOnAddOnApp.Secrets, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOnApp) GetSecrets() []AppDataSecretsSecret { return v.AppData.Secrets }

// GetOrganization returns GetAddOnAddOnApp.Organization, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOnApp) GetOrganization() AppDataOrganization { return v.AppData.Organization }

func (v *GetAddOnAddOnApp) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*GetAddOnAddOnApp
		graphql.NoUnmarshalJSON
	}
	firstPass.GetAddOnAddOnApp = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	err = json.Unmarshal(
		b, &v.AppData)
	if err != nil {
		return err
	}
	return nil
}

type __premarshalGetAddOnAddOnApp struct {
	Id string `json:"id"`

	Name string `json:"name"`

	Deployed bool `json:"deployed"`

	PlatformVersion PlatformVersionEnum `json:"platformVersion"`

	Secrets []AppDataSecretsSecret `json:"secrets"`

	Organization AppDataOrganization `json:"organization"`
}

func (v *GetAddOnAddOnApp) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *GetAddOnAddOnApp) __premarshalJSON() (*__premarshalGetAddOnAddOnApp, error) {
	var retval __premarshalGetAddOnAddOnApp

	retval.Id = v.AppData.Id
	retval.Name = v.AppData.Name
	retval.Deployed = v.AppData.Deployed
	retval.PlatformVersion = v.AppData.PlatformVersion
	retval.Secrets = v.AppData.Secrets
	retval.Organization = v.AppData.Organization
	return &retval, nil
}

// GetAddOnAddOnOrganization includes the requested fields of the GraphQL type Organization.
type GetAddOnAddOnOrganization struct {
	// Unique organization slug
	Slug     string `json:"slug"`
	PaidPlan bool   `json:"paidPlan"`
}

// GetSlug returns GetAddOnAddOnOrganization.Slug, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOnOrganization) GetSlug() string { return v.Slug }

// GetPaidPlan returns GetAddOnAddOnOrganization.PaidPlan, and is useful for accessing the field via an interface.
func (v *GetAddOnAddOnOrganization) GetPaidPlan() bool { return v.PaidPlan }

// GetAddOnProviderAddOnProvider includes the requested fields of the GraphQL type AddOnProvider.
type GetAddOnProviderAddOnProvider struct {
	ExtensionProviderData `json:"-"`
}

// GetId returns GetAddOnProviderAddOnProvider.Id, and is useful for accessing the field via an interface.
func (v *GetAddOnProviderAddOnProvider) GetId() string { return v.ExtensionProviderData.Id }

// GetName returns GetAddOnProviderAddOnProvider.Name, and is useful for accessing the field via an interface.
func (v *GetAddOnProviderAddOnProvider) GetName() string { return v.ExtensionProviderData.Name }

// GetDisplayName returns GetAddOnProviderAddOnProvider.DisplayName, and is useful for accessing the field via an interface.
func (v *GetAddOnProviderAddOnProvider) GetDisplayName() string {
	return v.ExtensionProviderData.DisplayName
}

// GetTosUrl returns GetAddOnProviderAddOnProvider.TosUrl, and is useful for accessing the field via an interface.
func (v *GetAddOnProviderAddOnProvider) GetTosUrl() string { return v.ExtensionProviderData.TosUrl }

// GetAsyncProvisioning returns GetAddOnProviderAddOnProvider.AsyncProvisioning, and is useful for accessing the field via an interface.
func (v *GetAddOnProviderAddOnProvider) GetAsyncProvisioning() bool {
	return v.ExtensionProviderData.AsyncProvisioning
}

// GetAutoProvision returns GetAddOnProviderAddOnProvider.AutoProvision, and is useful for accessing the field via an interface.
func (v *GetAddOnProviderAddOnProvider) GetAutoProvision() bool {
	return v.ExtensionProviderData.AutoProvision
}

// GetSelectName returns GetAddOnProviderAddOnProvider.SelectName, and is useful for accessing the field via an interface.
func (v *GetAddOnProviderAddOnProvider) GetSelectName() bool {
	return v.ExtensionProviderData.SelectName
}

// GetSelectRegion returns GetAddOnProviderAddOnProvider.SelectRegion, and is useful for accessing the field via an interface.
func (v *GetAddOnProviderAddOnProvider) GetSelectRegion() bool {
	return v.ExtensionProviderData.SelectRegion
}

// GetSelectReplicaRegions returns GetAddOnProviderAddOnProvider.SelectReplicaRegions, and is useful for accessing the field via an interface.
func (v *GetAddOnProviderAddOnProvider) GetSelectReplicaRegions() bool {
	return v.ExtensionProviderData.SelectReplicaRegions
}

// GetDetectPlatform returns GetAddOnProviderAddOnProvider.DetectPlatform, and is useful for accessing the field via an interface.
func (v *GetAddOnProviderAddOnProvider) GetDetectPlatform() bool {
	return v.ExtensionProviderData.DetectPlatform
}

// GetResourceName returns GetAddOnProviderAddOnProvider.ResourceName, and is useful for accessing the field via an interface.
func (v *GetAddOnProviderAddOnProvider) GetResourceName() string {
	return v.ExtensionProviderData.ResourceName
}

// GetNameSuffix returns GetAddOnProviderAddOnProvider.NameSuffix, and is useful for accessing the field via an interface.
func (v *GetAddOnProviderAddOnProvider) GetNameSuffix() string {
	return v.ExtensionProviderData.NameSuffix
}

// GetBeta returns GetAddOnProviderAddOnProvider.Beta, and is useful for accessing the field via an interface.
func (v *GetAddOnProviderAddOnProvider) GetBeta() bool { return v.ExtensionProviderData.Beta }

// GetTosAgreement returns GetAddOnProviderAddOnProvider.TosAgreement, and is useful for accessing the field via an interface.
func (v *GetAddOnProviderAddOnProvider) GetTosAgreement() string {
	return v.ExtensionProviderData.TosAgreement
}

// GetInternal returns GetAddOnProviderAddOnProvider.Internal, and is useful for accessing the field via an interface.
func (v *GetAddOnProviderAddOnProvider) GetInternal() bool { return v.ExtensionProviderData.Internal }

// GetProvisioningInstructions returns GetAddOnProviderAddOnProvider.ProvisioningInstructions, and is useful for accessing the field via an interface.
func (v *GetAddOnProviderAddOnProvider) GetProvisioningInstructions() string {
	return v.ExtensionProviderData.ProvisioningInstructions
}

// GetExcludedRegions returns GetAddOnProviderAddOnProvider.ExcludedRegions, and is useful for accessing the field via an interface.
func (v *GetAddOnProviderAddOnProvider) GetExcludedRegions() []ExtensionProviderDataExcludedRegionsRegion {
	return v.ExtensionProviderData.ExcludedRegions
}

func (v *GetAddOnProviderAddOnProvider) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*GetAddOnProviderAddOnProvider
		graphql.NoUnmarshalJSON
	}
	firstPass.GetAddOnProviderAddOnProvider = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	err = json.Unmarshal(
		b, &v.ExtensionProviderData)
	if err != nil {
		return err
	}
	return nil
}

type __premarshalGetAddOnProviderAddOnProvider struct {
	Id string `json:"id"`

	Name string `json:"name"`

	DisplayName string `json:"displayName"`

	TosUrl string `json:"tosUrl"`

	AsyncProvisioning bool `json:"asyncProvisioning"`

	AutoProvision bool `json:"autoProvision"`

	SelectName bool `json:"selectName"`

	SelectRegion bool `json:"selectRegion"`

	SelectReplicaRegions bool `json:"selectReplicaRegions"`

	DetectPlatform bool `json:"detectPlatform"`

	ResourceName string `json:"resourceName"`

	NameSuffix string `json:"nameSuffix"`

	Beta bool `json:"beta"`

	TosAgreement string `json:"tosAgreement"`

	Internal bool `json:"internal"`

	ProvisioningInstructions string `json:"provisioningInstructions"`

	ExcludedRegions []ExtensionProviderDataExcludedRegionsRegion `json:"excludedRegions"`
}

func (v *GetAddOnProviderAddOnProvider) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *GetAddOnProviderAddOnProvider) __premarshalJSON() (*__premarshalGetAddOnProviderAddOnProvider, error) {
	var retval __premarshalGetAddOnProviderAddOnProvider

	retval.Id = v.ExtensionProviderData.Id
	retval.Name = v.ExtensionProviderData.Name
	retval.DisplayName = v.ExtensionProviderData.DisplayName
	retval.TosUrl = v.ExtensionProviderData.TosUrl
	retval.AsyncProvisioning = v.ExtensionProviderData.AsyncProvisioning
	retval.AutoProvision = v.ExtensionProviderData.AutoProvision
	retval.SelectName = v.ExtensionProviderData.SelectName
	retval.SelectRegion = v.ExtensionProviderData.SelectRegion
	retval.SelectReplicaRegions = v.ExtensionProviderData.SelectReplicaRegions
	retval.DetectPlatform = v.ExtensionProviderData.DetectPlatform
	retval.ResourceName = v.ExtensionProviderData.ResourceName
	retval.NameSuffix = v.ExtensionProviderData.NameSuffix
	retval.Beta = v.ExtensionProviderData.Beta
	retval.TosAgreement = v.ExtensionProviderData.TosAgreement
	retval.Internal = v.ExtensionProviderData.Internal
	retval.ProvisioningInstructions = v.ExtensionProviderData.ProvisioningInstructions
	retval.ExcludedRegions = v.ExtensionProviderData.ExcludedRegions
	return &retval, nil
}

// GetAddOnProviderResponse is returned by GetAddOnProvider on success.
type GetAddOnProviderResponse struct {
	AddOnProvider GetAddOnProviderAddOnProvider `json:"addOnProvider"`
}

// GetAddOnProvider returns GetAddOnProviderResponse.AddOnProvider, and is useful for accessing the field via an interface.
func (v *GetAddOnProviderResponse) GetAddOnProvider() GetAddOnProviderAddOnProvider {
	return v.AddOnProvider
}

// GetAddOnResponse is returned by GetAddOn on success.
type GetAddOnResponse struct {
	// Find an add-on by ID or name
	AddOn GetAddOnAddOn `json:"addOn"`
}

// GetAddOn returns GetAddOnResponse.AddOn, and is useful for accessing the field via an interface.
func (v *GetAddOnResponse) GetAddOn() GetAddOnAddOn { return v.AddOn }

// GetAppApp includes the requested fields of the GraphQL type App.
type GetAppApp struct {
	AppData `json:"-"`
}

// GetId returns GetAppApp.Id, and is useful for accessing the field via an interface.
func (v *GetAppApp) GetId() string { return v.AppData.Id }

// GetName returns GetAppApp.Name, and is useful for accessing the field via an interface.
func (v *GetAppApp) GetName() string { return v.AppData.Name }

// GetDeployed returns GetAppApp.Deployed, and is useful for accessing the field via an interface.
func (v *GetAppApp) GetDeployed() bool { return v.AppData.Deployed }

// GetPlatformVersion returns GetAppApp.PlatformVersion, and is useful for accessing the field via an interface.
func (v *GetAppApp) GetPlatformVersion() PlatformVersionEnum { return v.AppData.PlatformVersion }

// GetSecrets returns GetAppApp.Secrets, and is useful for accessing the field via an interface.
func (v *GetAppApp) GetSecrets() []AppDataSecretsSecret { return v.AppData.Secrets }

// GetOrganization returns GetAppApp.Organization, and is useful for accessing the field via an interface.
func (v *GetAppApp) GetOrganization() AppDataOrganization { return v.AppData.Organization }

func (v *GetAppApp) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*GetAppApp
		graphql.NoUnmarshalJSON
	}
	firstPass.GetAppApp = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	err = json.Unmarshal(
		b, &v.AppData)
	if err != nil {
		return err
	}
	return nil
}

type __premarshalGetAppApp struct {
	Id string `json:"id"`

	Name string `json:"name"`

	Deployed bool `json:"deployed"`

	PlatformVersion PlatformVersionEnum `json:"platformVersion"`

	Secrets []AppDataSecretsSecret `json:"secrets"`

	Organization AppDataOrganization `json:"organization"`
}

func (v *GetAppApp) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *GetAppApp) __premarshalJSON() (*__premarshalGetAppApp, error) {
	var retval __premarshalGetAppApp

	retval.Id = v.AppData.Id
	retval.Name = v.AppData.Name
	retval.Deployed = v.AppData.Deployed
	retval.PlatformVersion = v.AppData.PlatformVersion
	retval.Secrets = v.AppData.Secrets
	retval.Organization = v.AppData.Organization
	return &retval, nil
}

// GetAppResponse is returned by GetApp on success.
type GetAppResponse struct {
	// Find an app by name
	App GetAppApp `json:"app"`
}

// GetApp returns GetAppResponse.App, and is useful for accessing the field via an interface.
func (v *GetAppResponse) GetApp() GetAppApp { return v.App }

// GetAppWithAddonsApp includes the requested fields of the GraphQL type App.
type GetAppWithAddonsApp struct {
	AppData `json:"-"`
	AddOns  GetAppWithAddonsAppAddOnsAddOnConnection `json:"addOns"`
}

// GetAddOns returns GetAppWithAddonsApp.AddOns, and is useful for accessing the field via an interface.
func (v *GetAppWithAddonsApp) GetAddOns() GetAppWithAddonsAppAddOnsAddOnConnection { return v.AddOns }

// GetId returns GetAppWithAddonsApp.Id, and is useful for accessing the field via an interface.
func (v *GetAppWithAddonsApp) GetId() string { return v.AppData.Id }

// GetName returns GetAppWithAddonsApp.Name, and is useful for accessing the field via an interface.
func (v *GetAppWithAddonsApp) GetName() string { return v.AppData.Name }

// GetDeployed returns GetAppWithAddonsApp.Deployed, and is useful for accessing the field via an interface.
func (v *GetAppWithAddonsApp) GetDeployed() bool { return v.AppData.Deployed }

// GetPlatformVersion returns GetAppWithAddonsApp.PlatformVersion, and is useful for accessing the field via an interface.
func (v *GetAppWithAddonsApp) GetPlatformVersion() PlatformVersionEnum {
	return v.AppData.PlatformVersion
}

// GetSecrets returns GetAppWithAddonsApp.Secrets, and is useful for accessing the field via an interface.
func (v *GetAppWithAddonsApp) GetSecrets() []AppDataSecretsSecret { return v.AppData.Secrets }

// GetOrganization returns GetAppWithAddonsApp.Organization, and is useful for accessing the field via an interface.
func (v *GetAppWithAddonsApp) GetOrganization() AppDataOrganization { return v.AppData.Organization }

func (v *GetAppWithAddonsApp) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*GetAppWithAddonsApp
		graphql.NoUnmarshalJSON
	}
	firstPass.GetAppWithAddonsApp = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	err = json.Unmarshal(
		b, &v.AppData)
	if err != nil {
		return err
	}
	return nil
}

type __premarshalGetAppWithAddonsApp struct {
	AddOns GetAppWithAddonsAppAddOnsAddOnConnection `json:"addOns"`

	Id string `json:"id"`

	Name string `json:"name"`

	Deployed bool `json:"deployed"`

	PlatformVersion PlatformVersionEnum `json:"platformVersion"`

	Secrets []AppDataSecretsSecret `json:"secrets"`

	Organization AppDataOrganization `json:"organization"`
}

func (v *GetAppWithAddonsApp) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *GetAppWithAddonsApp) __premarshalJSON() (*__premarshalGetAppWithAddonsApp, error) {
	var retval __premarshalGetAppWithAddonsApp

	retval.AddOns = v.AddOns
	retval.Id = v.AppData.Id
	retval.Name = v.AppData.Name
	retval.Deployed = v.AppData.Deployed
	retval.PlatformVersion = v.AppData.PlatformVersion
	retval.Secrets = v.AppData.Secrets
	retval.Organization = v.AppData.Organization
	return &retval, nil
}

// GetAppWithAddonsAppAddOnsAddOnConnection includes the requested fields of the GraphQL type AddOnConnection.
// The GraphQL type's documentation follows.
//
// The connection type for AddOn.
type GetAppWithAddonsAppAddOnsAddOnConnection struct {
	// A list of nodes.
	Nodes []GetAppWithAddonsAppAddOnsAddOnConnectionNodesAddOn `json:"nodes"`
}

// GetNodes returns GetAppWithAddonsAppAddOnsAddOnConnection.Nodes, and is useful for accessing the field via an interface.
func (v *GetAppWithAddonsAppAddOnsAddOnConnection) GetNodes() []GetAppWithAddonsAppAddOnsAddOnConnectionNodesAddOn {
	return v.Nodes
}

// GetAppWithAddonsAppAddOnsAddOnConnectionNodesAddOn includes the requested fields of the GraphQL type AddOn.
type GetAppWithAddonsAppAddOnsAddOnConnectionNodesAddOn struct {
	AddOnData `json:"-"`
}

// GetId returns GetAppWithAddonsAppAddOnsAddOnConnectionNodesAddOn.Id, and is useful for accessing the field via an interface.
func (v *GetAppWithAddonsAppAddOnsAddOnConnectionNodesAddOn) GetId() string { return v.AddOnData.Id }

// GetName returns GetAppWithAddonsAppAddOnsAddOnConnectionNodesAddOn.Name, and is useful for accessing the field via an interface.
func (v *GetAppWithAddonsAppAddOnsAddOnConnectionNodesAddOn) GetName() string {
	return v.AddOnData.Name
}

// GetPrimaryRegion returns GetAppWithAddonsAppAddOnsAddOnConnectionNodesAddOn.PrimaryRegion, and is useful for accessing the field via an interface.
func (v *GetAppWithAddonsAppAddOnsAddOnConnectionNodesAddOn) GetPrimaryRegion() string {
	return v.AddOnData.PrimaryRegion
}

// GetStatus returns GetAppWithAddonsAppAddOnsAddOnConnectionNodesAddOn.Status, and is useful for accessing the field via an interface.
func (v *GetAppWithAddonsAppAddOnsAddOnConnectionNodesAddOn) GetStatus() string {
	return v.AddOnData.Status
}

// GetErrorMessage returns GetAppWithAddonsAppAddOnsAddOnConnectionNodesAddOn.ErrorMessage, and is useful for accessing the field via an interface.
func (v *GetAppWithAddonsAppAddOnsAddOnConnectionNodesAddOn) GetErrorMessage() string {
	return v.AddOnData.ErrorMessage
}

// GetMetadata returns GetAppWithAddonsAppAddOnsAddOnConnectionNodesAddOn.Metadata, and is useful for accessing the field via an interface.
func (v *GetAppWithAddonsAppAddOnsAddOnConnectionNodesAddOn) GetMetadata() interface{} {
	return v.AddOnData.Metadata
}

// GetOptions returns GetAppWithAddonsAppAddOnsAddOnConnectionNodesAddOn.Options, and is useful for accessing the field via an interface.
func (v *GetAppWithAddonsAppAddOnsAddOnConnectionNodesAddOn) GetOptions() interface{} {
	return v.AddOnData.Options
}

func (v *GetAppWithAddonsAppAddOnsAddOnConnectionNodesAddOn) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*GetAppWithAddonsAppAddOnsAddOnConnectionNodesAddOn
		graphql.NoUnmarshalJSON
	}
	firstPass.GetAppWithAddonsAppAddOnsAddOnConnectionNodesAddOn = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	err = json.Unmarshal(
		b, &v.AddOnData)
	if err != nil {
		return err
	}
	return nil
}

type __premarshalGetAppWithAddonsAppAddOnsAddOnConnectionNodesAddOn struct {
	Id string `json:"id"`

	Name string `json:"name"`

	PrimaryRegion string `json:"primaryRegion"`

	Status string `json:"status"`

	ErrorMessage string `json:"errorMessage"`

	Metadata interface{} `json:"metadata"`

	Options interface{} `json:"options"`
}

func (v *GetAppWithAddonsAppAddOnsAddOnConnectionNodesAddOn) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *GetAppWithAddonsAppAddOnsAddOnConnectionNodesAddOn) __premarshalJSON() (*__premarshalGetAppWithAddonsAppAddOnsAddOnConnectionNodesAddOn, error) {
	var retval __premarshalGetAppWithAddonsAppAddOnsAddOnConnectionNodesAddOn

	retval.Id = v.AddOnData.Id
	retval.Name = v.AddOnData.Name
	retval.PrimaryRegion = v.AddOnData.PrimaryRegion
	retval.Status = v.AddOnData.Status
	retval.ErrorMessage = v.AddOnData.ErrorMessage
	retval.Metadata = v.AddOnData.Metadata
	retval.Options = v.AddOnData.Options
	return &retval, nil
}

// GetAppWithAddonsResponse is returned by GetAppWithAddons on success.
type GetAppWithAddonsResponse struct {
	// Find an app by name
	App GetAppWithAddonsApp `json:"app"`
}

// GetApp returns GetAppWithAddonsResponse.App, and is useful for accessing the field via an interface.
func (v *GetAppWithAddonsResponse) GetApp() GetAppWithAddonsApp { return v.App }

// GetAppsByRoleAppsAppConnection includes the requested fields of the GraphQL type AppConnection.
// The GraphQL type's documentation follows.
//
// The connection type for App.
type GetAppsByRoleAppsAppConnection struct {
	// A list of nodes.
	Nodes []GetAppsByRoleAppsAppConnectionNodesApp `json:"nodes"`
}

// GetNodes returns GetAppsByRoleAppsAppConnection.Nodes, and is useful for accessing the field via an interface.
func (v *GetAppsByRoleAppsAppConnection) GetNodes() []GetAppsByRoleAppsAppConnectionNodesApp {
	return v.Nodes
}

// GetAppsByRoleAppsAppConnectionNodesApp includes the requested fields of the GraphQL type App.
type GetAppsByRoleAppsAppConnectionNodesApp struct {
	AppData `json:"-"`
}

// GetId returns GetAppsByRoleAppsAppConnectionNodesApp.Id, and is useful for accessing the field via an interface.
func (v *GetAppsByRoleAppsAppConnectionNodesApp) GetId() string { return v.AppData.Id }

// GetName returns GetAppsByRoleAppsAppConnectionNodesApp.Name, and is useful for accessing the field via an interface.
func (v *GetAppsByRoleAppsAppConnectionNodesApp) GetName() string { return v.AppData.Name }

// GetDeployed returns GetAppsByRoleAppsAppConnectionNodesApp.Deployed, and is useful for accessing the field via an interface.
func (v *GetAppsByRoleAppsAppConnectionNodesApp) GetDeployed() bool { return v.AppData.Deployed }

// GetPlatformVersion returns GetAppsByRoleAppsAppConnectionNodesApp.PlatformVersion, and is useful for accessing the field via an interface.
func (v *GetAppsByRoleAppsAppConnectionNodesApp) GetPlatformVersion() PlatformVersionEnum {
	return v.AppData.PlatformVersion
}

// GetSecrets returns GetAppsByRoleAppsAppConnectionNodesApp.Secrets, and is useful for accessing the field via an interface.
func (v *GetAppsByRoleAppsAppConnectionNodesApp) GetSecrets() []AppDataSecretsSecret {
	return v.AppData.Secrets
}

// GetOrganization returns GetAppsByRoleAppsAppConnectionNodesApp.Organization, and is useful for accessing the field via an interface.
func (v *GetAppsByRoleAppsAppConnectionNodesApp) GetOrganization() AppDataOrganization {
	return v.AppData.Organization
}

func (v *GetAppsByRoleAppsAppConnectionNodesApp) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*GetAppsByRoleAppsAppConnectionNodesApp
		graphql.NoUnmarshalJSON
	}
	firstPass.GetAppsByRoleAppsAppConnectionNodesApp = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	err = json.Unmarshal(
		b, &v.AppData)
	if err != nil {
		return err
	}
	return nil
}

type __premarshalGetAppsByRoleAppsAppConnectionNodesApp struct {
	Id string `json:"id"`

	Name string `json:"name"`

	Deployed bool `json:"deployed"`

	PlatformVersion PlatformVersionEnum `json:"platformVersion"`

	Secrets []AppDataSecretsSecret `json:"secrets"`

	Organization AppDataOrganization `json:"organization"`
}

func (v *GetAppsByRoleAppsAppConnectionNodesApp) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *GetAppsByRoleAppsAppConnectionNodesApp) __premarshalJSON() (*__premarshalGetAppsByRoleAppsAppConnectionNodesApp, error) {
	var retval __premarshalGetAppsByRoleAppsAppConnectionNodesApp

	retval.Id = v.AppData.Id
	retval.Name = v.AppData.Name
	retval.Deployed = v.AppData.Deployed
	retval.PlatformVersion = v.AppData.PlatformVersion
	retval.Secrets = v.AppData.Secrets
	retval.Organization = v.AppData.Organization
	return &retval, nil
}

// GetAppsByRoleResponse is returned by GetAppsByRole on success.
type GetAppsByRoleResponse struct {
	// List apps
	Apps GetAppsByRoleAppsAppConnection `json:"apps"`
}

// GetApps returns GetAppsByRoleResponse.Apps, and is useful for accessing the field via an interface.
func (v *GetAppsByRoleResponse) GetApps() GetAppsByRoleAppsAppConnection { return v.Apps }

// GetExtensionSsoLinkOrganization includes the requested fields of the GraphQL type Organization.
type GetExtensionSsoLinkOrganization struct {
	// Single sign-on link for the given extension type
	ExtensionSsoLink string `json:"extensionSsoLink"`
}

// GetExtensionSsoLink returns GetExtensionSsoLinkOrganization.ExtensionSsoLink, and is useful for accessing the field via an interface.
func (v *GetExtensionSsoLinkOrganization) GetExtensionSsoLink() string { return v.ExtensionSsoLink }

// GetExtensionSsoLinkResponse is returned by GetExtensionSsoLink on success.
type GetExtensionSsoLinkResponse struct {
	// Find an organization by ID
	Organization GetExtensionSsoLinkOrganization `json:"organization"`
}

// GetOrganization returns GetExtensionSsoLinkResponse.Organization, and is useful for accessing the field via an interface.
func (v *GetExtensionSsoLinkResponse) GetOrganization() GetExtensionSsoLinkOrganization {
	return v.Organization
}

// GetNearestRegionNearestRegion includes the requested fields of the GraphQL type Region.
type GetNearestRegionNearestRegion struct {
	// The IATA airport code for this region
	Code string `json:"code"`
	// The name of this region
	Name             string `json:"name"`
	GatewayAvailable bool   `json:"gatewayAvailable"`
}

// GetCode returns GetNearestRegionNearestRegion.Code, and is useful for accessing the field via an interface.
func (v *GetNearestRegionNearestRegion) GetCode() string { return v.Code }

// GetName returns GetNearestRegionNearestRegion.Name, and is useful for accessing the field via an interface.
func (v *GetNearestRegionNearestRegion) GetName() string { return v.Name }

// GetGatewayAvailable returns GetNearestRegionNearestRegion.GatewayAvailable, and is useful for accessing the field via an interface.
func (v *GetNearestRegionNearestRegion) GetGatewayAvailable() bool { return v.GatewayAvailable }

// GetNearestRegionResponse is returned by GetNearestRegion on success.
type GetNearestRegionResponse struct {
	NearestRegion GetNearestRegionNearestRegion `json:"nearestRegion"`
}

// GetNearestRegion returns GetNearestRegionResponse.NearestRegion, and is useful for accessing the field via an interface.
func (v *GetNearestRegionResponse) GetNearestRegion() GetNearestRegionNearestRegion {
	return v.NearestRegion
}

// GetOrganizationOrganization includes the requested fields of the GraphQL type Organization.
type GetOrganizationOrganization struct {
	OrganizationData `json:"-"`
}

// GetId returns GetOrganizationOrganization.Id, and is useful for accessing the field via an interface.
func (v *GetOrganizationOrganization) GetId() string { return v.OrganizationData.Id }

// GetSlug returns GetOrganizationOrganization.Slug, and is useful for accessing the field via an interface.
func (v *GetOrganizationOrganization) GetSlug() string { return v.OrganizationData.Slug }

// GetRawSlug returns GetOrganizationOrganization.RawSlug, and is useful for accessing the field via an interface.
func (v *GetOrganizationOrganization) GetRawSlug() string { return v.OrganizationData.RawSlug }

// GetPaidPlan returns GetOrganizationOrganization.PaidPlan, and is useful for accessing the field via an interface.
func (v *GetOrganizationOrganization) GetPaidPlan() bool { return v.OrganizationData.PaidPlan }

// GetAddOnSsoLink returns GetOrganizationOrganization.AddOnSsoLink, and is useful for accessing the field via an interface.
func (v *GetOrganizationOrganization) GetAddOnSsoLink() string {
	return v.OrganizationData.AddOnSsoLink
}

// GetProvisionsBetaExtensions returns GetOrganizationOrganization.ProvisionsBetaExtensions, and is useful for accessing the field via an interface.
func (v *GetOrganizationOrganization) GetProvisionsBetaExtensions() bool {
	return v.OrganizationData.ProvisionsBetaExtensions
}

func (v *GetOrganizationOrganization) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*GetOrganizationOrganization
		graphql.NoUnmarshalJSON
	}
	firstPass.GetOrganizationOrganization = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	err = json.Unmarshal(
		b, &v.OrganizationData)
	if err != nil {
		return err
	}
	return nil
}

type __premarshalGetOrganizationOrganization struct {
	Id string `json:"id"`

	Slug string `json:"slug"`

	RawSlug string `json:"rawSlug"`

	PaidPlan bool `json:"paidPlan"`

	AddOnSsoLink string `json:"addOnSsoLink"`

	ProvisionsBetaExtensions bool `json:"provisionsBetaExtensions"`
}

func (v *GetOrganizationOrganization) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *GetOrganizationOrganization) __premarshalJSON() (*__premarshalGetOrganizationOrganization, error) {
	var retval __premarshalGetOrganizationOrganization

	retval.Id = v.OrganizationData.Id
	retval.Slug = v.OrganizationData.Slug
	retval.RawSlug = v.OrganizationData.RawSlug
	retval.PaidPlan = v.OrganizationData.PaidPlan
	retval.AddOnSsoLink = v.OrganizationData.AddOnSsoLink
	retval.ProvisionsBetaExtensions = v.OrganizationData.ProvisionsBetaExtensions
	return &retval, nil
}

// GetOrganizationResponse is returned by GetOrganization on success.
type GetOrganizationResponse struct {
	// Find an organization by ID
	Organization GetOrganizationOrganization `json:"organization"`
}

// GetOrganization returns GetOrganizationResponse.Organization, and is useful for accessing the field via an interface.
func (v *GetOrganizationResponse) GetOrganization() GetOrganizationOrganization {
	return v.Organization
}

// ListAddOnPlansAddOnPlansAddOnPlanConnection includes the requested fields of the GraphQL type AddOnPlanConnection.
// The GraphQL type's documentation follows.
//
// The connection type for AddOnPlan.
type ListAddOnPlansAddOnPlansAddOnPlanConnection struct {
	// A list of nodes.
	Nodes []ListAddOnPlansAddOnPlansAddOnPlanConnectionNodesAddOnPlan `json:"nodes"`
}

// GetNodes returns ListAddOnPlansAddOnPlansAddOnPlanConnection.Nodes, and is useful for accessing the field via an interface.
func (v *ListAddOnPlansAddOnPlansAddOnPlanConnection) GetNodes() []ListAddOnPlansAddOnPlansAddOnPlanConnectionNodesAddOnPlan {
	return v.Nodes
}

// ListAddOnPlansAddOnPlansAddOnPlanConnectionNodesAddOnPlan includes the requested fields of the GraphQL type AddOnPlan.
type ListAddOnPlansAddOnPlansAddOnPlanConnectionNodesAddOnPlan struct {
	Id            string `json:"id"`
	Description   string `json:"description"`
	DisplayName   string `json:"displayName"`
	MaxDataSize   string `json:"maxDataSize"`
	PricePerMonth int    `json:"pricePerMonth"`
}

// GetId returns ListAddOnPlansAddOnPlansAddOnPlanConnectionNodesAddOnPlan.Id, and is useful for accessing the field via an interface.
func (v *ListAddOnPlansAddOnPlansAddOnPlanConnectionNodesAddOnPlan) GetId() string { return v.Id }

// GetDescription returns ListAddOnPlansAddOnPlansAddOnPlanConnectionNodesAddOnPlan.Description, and is useful for accessing the field via an interface.
func (v *ListAddOnPlansAddOnPlansAddOnPlanConnectionNodesAddOnPlan) GetDescription() string {
	return v.Description
}

// GetDisplayName returns ListAddOnPlansAddOnPlansAddOnPlanConnectionNodesAddOnPlan.DisplayName, and is useful for accessing the field via an interface.
func (v *ListAddOnPlansAddOnPlansAddOnPlanConnectionNodesAddOnPlan) GetDisplayName() string {
	return v.DisplayName
}

// GetMaxDataSize returns ListAddOnPlansAddOnPlansAddOnPlanConnectionNodesAddOnPlan.MaxDataSize, and is useful for accessing the field via an interface.
func (v *ListAddOnPlansAddOnPlansAddOnPlanConnectionNodesAddOnPlan) GetMaxDataSize() string {
	return v.MaxDataSize
}

// GetPricePerMonth returns ListAddOnPlansAddOnPlansAddOnPlanConnectionNodesAddOnPlan.PricePerMonth, and is useful for accessing the field via an interface.
func (v *ListAddOnPlansAddOnPlansAddOnPlanConnectionNodesAddOnPlan) GetPricePerMonth() int {
	return v.PricePerMonth
}

// ListAddOnPlansResponse is returned by ListAddOnPlans on success.
type ListAddOnPlansResponse struct {
	// List add-on service plans
	AddOnPlans ListAddOnPlansAddOnPlansAddOnPlanConnection `json:"addOnPlans"`
}

// GetAddOnPlans returns ListAddOnPlansResponse.AddOnPlans, and is useful for accessing the field via an interface.
func (v *ListAddOnPlansResponse) GetAddOnPlans() ListAddOnPlansAddOnPlansAddOnPlanConnection {
	return v.AddOnPlans
}

// ListAddOnsAddOnsAddOnConnection includes the requested fields of the GraphQL type AddOnConnection.
// The GraphQL type's documentation follows.
//
// The connection type for AddOn.
type ListAddOnsAddOnsAddOnConnection struct {
	// A list of nodes.
	Nodes []ListAddOnsAddOnsAddOnConnectionNodesAddOn `json:"nodes"`
}

// GetNodes returns ListAddOnsAddOnsAddOnConnection.Nodes, and is useful for accessing the field via an interface.
func (v *ListAddOnsAddOnsAddOnConnection) GetNodes() []ListAddOnsAddOnsAddOnConnectionNodesAddOn {
	return v.Nodes
}

// ListAddOnsAddOnsAddOnConnectionNodesAddOn includes the requested fields of the GraphQL type AddOn.
type ListAddOnsAddOnsAddOnConnectionNodesAddOn struct {
	Id string `json:"id"`
	// The service name according to the provider
	Name string `json:"name"`
	// The add-on plan
	AddOnPlan ListAddOnsAddOnsAddOnConnectionNodesAddOnAddOnPlan `json:"addOnPlan"`
	// Private flycast IP address of the add-on
	PrivateIp string `json:"privateIp"`
	// Region where the primary instance is deployed
	PrimaryRegion string `json:"primaryRegion"`
	// Regions where replica instances are deployed
	ReadRegions []string `json:"readRegions"`
	// Add-on options
	Options interface{} `json:"options"`
	// Add-on metadata
	Metadata interface{} `json:"metadata"`
	// Organization that owns this service
	Organization ListAddOnsAddOnsAddOnConnectionNodesAddOnOrganization `json:"organization"`
}

// GetId returns ListAddOnsAddOnsAddOnConnectionNodesAddOn.Id, and is useful for accessing the field via an interface.
func (v *ListAddOnsAddOnsAddOnConnectionNodesAddOn) GetId() string { return v.Id }

// GetName returns ListAddOnsAddOnsAddOnConnectionNodesAddOn.Name, and is useful for accessing the field via an interface.
func (v *ListAddOnsAddOnsAddOnConnectionNodesAddOn) GetName() string { return v.Name }

// GetAddOnPlan returns ListAddOnsAddOnsAddOnConnectionNodesAddOn.AddOnPlan, and is useful for accessing the field via an interface.
func (v *ListAddOnsAddOnsAddOnConnectionNodesAddOn) GetAddOnPlan() ListAddOnsAddOnsAddOnConnectionNodesAddOnAddOnPlan {
	return v.AddOnPlan
}

// GetPrivateIp returns ListAddOnsAddOnsAddOnConnectionNodesAddOn.PrivateIp, and is useful for accessing the field via an interface.
func (v *ListAddOnsAddOnsAddOnConnectionNodesAddOn) GetPrivateIp() string { return v.PrivateIp }

// GetPrimaryRegion returns ListAddOnsAddOnsAddOnConnectionNodesAddOn.PrimaryRegion, and is useful for accessing the field via an interface.
func (v *ListAddOnsAddOnsAddOnConnectionNodesAddOn) GetPrimaryRegion() string { return v.PrimaryRegion }

// GetReadRegions returns ListAddOnsAddOnsAddOnConnectionNodesAddOn.ReadRegions, and is useful for accessing the field via an interface.
func (v *ListAddOnsAddOnsAddOnConnectionNodesAddOn) GetReadRegions() []string { return v.ReadRegions }

// GetOptions returns ListAddOnsAddOnsAddOnConnectionNodesAddOn.Options, and is useful for accessing the field via an interface.
func (v *ListAddOnsAddOnsAddOnConnectionNodesAddOn) GetOptions() interface{} { return v.Options }

// GetMetadata returns ListAddOnsAddOnsAddOnConnectionNodesAddOn.Metadata, and is useful for accessing the field via an interface.
func (v *ListAddOnsAddOnsAddOnConnectionNodesAddOn) GetMetadata() interface{} { return v.Metadata }

// GetOrganization returns ListAddOnsAddOnsAddOnConnectionNodesAddOn.Organization, and is useful for accessing the field via an interface.
func (v *ListAddOnsAddOnsAddOnConnectionNodesAddOn) GetOrganization() ListAddOnsAddOnsAddOnConnectionNodesAddOnOrganization {
	return v.Organization
}

// ListAddOnsAddOnsAddOnConnectionNodesAddOnAddOnPlan includes the requested fields of the GraphQL type AddOnPlan.
type ListAddOnsAddOnsAddOnConnectionNodesAddOnAddOnPlan struct {
	DisplayName string `json:"displayName"`
	Description string `json:"description"`
}

// GetDisplayName returns ListAddOnsAddOnsAddOnConnectionNodesAddOnAddOnPlan.DisplayName, and is useful for accessing the field via an interface.
func (v *ListAddOnsAddOnsAddOnConnectionNodesAddOnAddOnPlan) GetDisplayName() string {
	return v.DisplayName
}

// GetDescription returns ListAddOnsAddOnsAddOnConnectionNodesAddOnAddOnPlan.Description, and is useful for accessing the field via an interface.
func (v *ListAddOnsAddOnsAddOnConnectionNodesAddOnAddOnPlan) GetDescription() string {
	return v.Description
}

// ListAddOnsAddOnsAddOnConnectionNodesAddOnOrganization includes the requested fields of the GraphQL type Organization.
type ListAddOnsAddOnsAddOnConnectionNodesAddOnOrganization struct {
	Id string `json:"id"`
	// Unique organization slug
	Slug string `json:"slug"`
}

// GetId returns ListAddOnsAddOnsAddOnConnectionNodesAddOnOrganization.Id, and is useful for accessing the field via an interface.
func (v *ListAddOnsAddOnsAddOnConnectionNodesAddOnOrganization) GetId() string { return v.Id }

// GetSlug returns ListAddOnsAddOnsAddOnConnectionNodesAddOnOrganization.Slug, and is useful for accessing the field via an interface.
func (v *ListAddOnsAddOnsAddOnConnectionNodesAddOnOrganization) GetSlug() string { return v.Slug }

// ListAddOnsResponse is returned by ListAddOns on success.
type ListAddOnsResponse struct {
	// List add-ons associated with an organization
	AddOns ListAddOnsAddOnsAddOnConnection `json:"addOns"`
}

// GetAddOns returns ListAddOnsResponse.AddOns, and is useful for accessing the field via an interface.
func (v *ListAddOnsResponse) GetAddOns() ListAddOnsAddOnsAddOnConnection { return v.AddOns }

// LogOutLogOutLogOutPayload includes the requested fields of the GraphQL type LogOutPayload.
// The GraphQL type's documentation follows.
//
// Autogenerated return type of LogOut.
type LogOutLogOutLogOutPayload struct {
	Ok bool `json:"ok"`
}

// GetOk returns LogOutLogOutLogOutPayload.Ok, and is useful for accessing the field via an interface.
func (v *LogOutLogOutLogOutPayload) GetOk() bool { return v.Ok }

// LogOutResponse is returned by LogOut on success.
type LogOutResponse struct {
	LogOut LogOutLogOutLogOutPayload `json:"logOut"`
}

// GetLogOut returns LogOutResponse.LogOut, and is useful for accessing the field via an interface.
func (v *LogOutResponse) GetLogOut() LogOutLogOutLogOutPayload { return v.LogOut }

// OrganizationData includes the GraphQL fields of Organization requested by the fragment OrganizationData.
type OrganizationData struct {
	Id string `json:"id"`
	// Unique organization slug
	Slug string `json:"slug"`
	// Unmodified unique org slug
	RawSlug  string `json:"rawSlug"`
	PaidPlan bool   `json:"paidPlan"`
	// Single sign-on link for the given integration type
	AddOnSsoLink string `json:"addOnSsoLink"`
	// Whether the organization can provision beta extensions
	ProvisionsBetaExtensions bool `json:"provisionsBetaExtensions"`
}

// GetId returns OrganizationData.Id, and is useful for accessing the field via an interface.
func (v *OrganizationData) GetId() string { return v.Id }

// GetSlug returns OrganizationData.Slug, and is useful for accessing the field via an interface.
func (v *OrganizationData) GetSlug() string { return v.Slug }

// GetRawSlug returns OrganizationData.RawSlug, and is useful for accessing the field via an interface.
func (v *OrganizationData) GetRawSlug() string { return v.RawSlug }

// GetPaidPlan returns OrganizationData.PaidPlan, and is useful for accessing the field via an interface.
func (v *OrganizationData) GetPaidPlan() bool { return v.PaidPlan }

// GetAddOnSsoLink returns OrganizationData.AddOnSsoLink, and is useful for accessing the field via an interface.
func (v *OrganizationData) GetAddOnSsoLink() string { return v.AddOnSsoLink }

// GetProvisionsBetaExtensions returns OrganizationData.ProvisionsBetaExtensions, and is useful for accessing the field via an interface.
func (v *OrganizationData) GetProvisionsBetaExtensions() bool { return v.ProvisionsBetaExtensions }

type PlatformVersionEnum string

const (
	// App in migration between nomad and machines
	PlatformVersionEnumDetached PlatformVersionEnum = "detached"
	// App with only machines
	PlatformVersionEnumMachines PlatformVersionEnum = "machines"
	// Nomad managed application
	PlatformVersionEnumNomad PlatformVersionEnum = "nomad"
)

var AllPlatformVersionEnum = []PlatformVersionEnum{
	PlatformVersionEnumDetached,
	PlatformVersionEnumMachines,
	PlatformVersionEnumNomad,
}

// ResetAddOnPasswordResetAddOnPasswordResetAddOnPasswordPayload includes the requested fields of the GraphQL type ResetAddOnPasswordPayload.
// The GraphQL type's documentation follows.
//
// Autogenerated return type of ResetAddOnPassword.
type ResetAddOnPasswordResetAddOnPasswordResetAddOnPasswordPayload struct {
	AddOn ResetAddOnPasswordResetAddOnPasswordResetAddOnPasswordPayloadAddOn `json:"addOn"`
}

// GetAddOn returns ResetAddOnPasswordResetAddOnPasswordResetAddOnPasswordPayload.AddOn, and is useful for accessing the field via an interface.
func (v *ResetAddOnPasswordResetAddOnPasswordResetAddOnPasswordPayload) GetAddOn() ResetAddOnPasswordResetAddOnPasswordResetAddOnPasswordPayloadAddOn {
	return v.AddOn
}

// ResetAddOnPasswordResetAddOnPasswordResetAddOnPasswordPayloadAddOn includes the requested fields of the GraphQL type AddOn.
type ResetAddOnPasswordResetAddOnPasswordResetAddOnPasswordPayloadAddOn struct {
	// Public URL for this service
	PublicUrl string `json:"publicUrl"`
}

// GetPublicUrl returns ResetAddOnPasswordResetAddOnPasswordResetAddOnPasswordPayloadAddOn.PublicUrl, and is useful for accessing the field via an interface.
func (v *ResetAddOnPasswordResetAddOnPasswordResetAddOnPasswordPayloadAddOn) GetPublicUrl() string {
	return v.PublicUrl
}

// ResetAddOnPasswordResponse is returned by ResetAddOnPassword on success.
type ResetAddOnPasswordResponse struct {
	ResetAddOnPassword ResetAddOnPasswordResetAddOnPasswordResetAddOnPasswordPayload `json:"resetAddOnPassword"`
}

// GetResetAddOnPassword returns ResetAddOnPasswordResponse.ResetAddOnPassword, and is useful for accessing the field via an interface.
func (v *ResetAddOnPasswordResponse) GetResetAddOnPassword() ResetAddOnPasswordResetAddOnPasswordResetAddOnPasswordPayload {
	return v.ResetAddOnPassword
}

type RuntimeType string

const (
	// Fly Container Runtime
	RuntimeTypeFirecracker RuntimeType = "FIRECRACKER"
	// Fly JavaScript Runtime
	RuntimeTypeNodeproxy RuntimeType = "NODEPROXY"
)

var AllRuntimeType = []RuntimeType{
	RuntimeTypeFirecracker,
	RuntimeTypeNodeproxy,
}

// A secure configuration value
type SecretInput struct {
	// The unqiue key for this secret
	Key string `json:"key"`
	// The value of this secret
	Value string `json:"value"`
}

// GetKey returns SecretInput.Key, and is useful for accessing the field via an interface.
func (v *SecretInput) GetKey() string { return v.Key }

// GetValue returns SecretInput.Value, and is useful for accessing the field via an interface.
func (v *SecretInput) GetValue() string { return v.Value }

// SetNomadVMCountResponse is returned by SetNomadVMCount on success.
type SetNomadVMCountResponse struct {
	SetVmCount SetNomadVMCountSetVmCountSetVMCountPayload `json:"setVmCount"`
}

// GetSetVmCount returns SetNomadVMCountResponse.SetVmCount, and is useful for accessing the field via an interface.
func (v *SetNomadVMCountResponse) GetSetVmCount() SetNomadVMCountSetVmCountSetVMCountPayload {
	return v.SetVmCount
}

// SetNomadVMCountSetVmCountSetVMCountPayload includes the requested fields of the GraphQL type SetVMCountPayload.
// The GraphQL type's documentation follows.
//
// Autogenerated return type of SetVMCount.
type SetNomadVMCountSetVmCountSetVMCountPayload struct {
	TaskGroupCounts []SetNomadVMCountSetVmCountSetVMCountPayloadTaskGroupCountsTaskGroupCount `json:"taskGroupCounts"`
	Warnings        []string                                                                  `json:"warnings"`
}

// GetTaskGroupCounts returns SetNomadVMCountSetVmCountSetVMCountPayload.TaskGroupCounts, and is useful for accessing the field via an interface.
func (v *SetNomadVMCountSetVmCountSetVMCountPayload) GetTaskGroupCounts() []SetNomadVMCountSetVmCountSetVMCountPayloadTaskGroupCountsTaskGroupCount {
	return v.TaskGroupCounts
}

// GetWarnings returns SetNomadVMCountSetVmCountSetVMCountPayload.Warnings, and is useful for accessing the field via an interface.
func (v *SetNomadVMCountSetVmCountSetVMCountPayload) GetWarnings() []string { return v.Warnings }

// SetNomadVMCountSetVmCountSetVMCountPayloadTaskGroupCountsTaskGroupCount includes the requested fields of the GraphQL type TaskGroupCount.
type SetNomadVMCountSetVmCountSetVMCountPayloadTaskGroupCountsTaskGroupCount struct {
	Name  string `json:"name"`
	Count int    `json:"count"`
}

// GetName returns SetNomadVMCountSetVmCountSetVMCountPayloadTaskGroupCountsTaskGroupCount.Name, and is useful for accessing the field via an interface.
func (v *SetNomadVMCountSetVmCountSetVMCountPayloadTaskGroupCountsTaskGroupCount) GetName() string {
	return v.Name
}

// GetCount returns SetNomadVMCountSetVmCountSetVMCountPayloadTaskGroupCountsTaskGroupCount.Count, and is useful for accessing the field via an interface.
func (v *SetNomadVMCountSetVmCountSetVMCountPayloadTaskGroupCountsTaskGroupCount) GetCount() int {
	return v.Count
}

// Autogenerated input type of SetSecrets
type SetSecretsInput struct {
	// The ID of the app
	AppId string `json:"appId"`
	// A unique identifier for the client performing the mutation.
	ClientMutationId string `json:"clientMutationId"`
	// By default, we set only the secrets you specify. Set this to true to replace all secrets.
	ReplaceAll bool `json:"replaceAll"`
	// Secrets to set
	Secrets []SecretInput `json:"secrets"`
}

// GetAppId returns SetSecretsInput.AppId, and is useful for accessing the field via an interface.
func (v *SetSecretsInput) GetAppId() string { return v.AppId }

// GetClientMutationId returns SetSecretsInput.ClientMutationId, and is useful for accessing the field via an interface.
func (v *SetSecretsInput) GetClientMutationId() string { return v.ClientMutationId }

// GetReplaceAll returns SetSecretsInput.ReplaceAll, and is useful for accessing the field via an interface.
func (v *SetSecretsInput) GetReplaceAll() bool { return v.ReplaceAll }

// GetSecrets returns SetSecretsInput.Secrets, and is useful for accessing the field via an interface.
func (v *SetSecretsInput) GetSecrets() []SecretInput { return v.Secrets }

// SetSecretsResponse is returned by SetSecrets on success.
type SetSecretsResponse struct {
	SetSecrets SetSecretsSetSecretsSetSecretsPayload `json:"setSecrets"`
}

// GetSetSecrets returns SetSecretsResponse.SetSecrets, and is useful for accessing the field via an interface.
func (v *SetSecretsResponse) GetSetSecrets() SetSecretsSetSecretsSetSecretsPayload {
	return v.SetSecrets
}

// SetSecretsSetSecretsSetSecretsPayload includes the requested fields of the GraphQL type SetSecretsPayload.
// The GraphQL type's documentation follows.
//
// Autogenerated return type of SetSecrets.
type SetSecretsSetSecretsSetSecretsPayload struct {
	Release SetSecretsSetSecretsSetSecretsPayloadRelease `json:"release"`
}

// GetRelease returns SetSecretsSetSecretsSetSecretsPayload.Release, and is useful for accessing the field via an interface.
func (v *SetSecretsSetSecretsSetSecretsPayload) GetRelease() SetSecretsSetSecretsSetSecretsPayloadRelease {
	return v.Release
}

// SetSecretsSetSecretsSetSecretsPayloadRelease includes the requested fields of the GraphQL type Release.
type SetSecretsSetSecretsSetSecretsPayloadRelease struct {
	// Unique ID
	Id string `json:"id"`
	// The version of the release
	Version int `json:"version"`
	// The reason for the release
	Reason string `json:"reason"`
	// A description of the release
	Description string `json:"description"`
	// The user who created the release
	User         SetSecretsSetSecretsSetSecretsPayloadReleaseUser `json:"user"`
	EvaluationId string                                           `json:"evaluationId"`
	CreatedAt    time.Time                                        `json:"createdAt"`
}

// GetId returns SetSecretsSetSecretsSetSecretsPayloadRelease.Id, and is useful for accessing the field via an interface.
func (v *SetSecretsSetSecretsSetSecretsPayloadRelease) GetId() string { return v.Id }

// GetVersion returns SetSecretsSetSecretsSetSecretsPayloadRelease.Version, and is useful for accessing the field via an interface.
func (v *SetSecretsSetSecretsSetSecretsPayloadRelease) GetVersion() int { return v.Version }

// GetReason returns SetSecretsSetSecretsSetSecretsPayloadRelease.Reason, and is useful for accessing the field via an interface.
func (v *SetSecretsSetSecretsSetSecretsPayloadRelease) GetReason() string { return v.Reason }

// GetDescription returns SetSecretsSetSecretsSetSecretsPayloadRelease.Description, and is useful for accessing the field via an interface.
func (v *SetSecretsSetSecretsSetSecretsPayloadRelease) GetDescription() string { return v.Description }

// GetUser returns SetSecretsSetSecretsSetSecretsPayloadRelease.User, and is useful for accessing the field via an interface.
func (v *SetSecretsSetSecretsSetSecretsPayloadRelease) GetUser() SetSecretsSetSecretsSetSecretsPayloadReleaseUser {
	return v.User
}

// GetEvaluationId returns SetSecretsSetSecretsSetSecretsPayloadRelease.EvaluationId, and is useful for accessing the field via an interface.
func (v *SetSecretsSetSecretsSetSecretsPayloadRelease) GetEvaluationId() string {
	return v.EvaluationId
}

// GetCreatedAt returns SetSecretsSetSecretsSetSecretsPayloadRelease.CreatedAt, and is useful for accessing the field via an interface.
func (v *SetSecretsSetSecretsSetSecretsPayloadRelease) GetCreatedAt() time.Time { return v.CreatedAt }

// SetSecretsSetSecretsSetSecretsPayloadReleaseUser includes the requested fields of the GraphQL type User.
type SetSecretsSetSecretsSetSecretsPayloadReleaseUser struct {
	Id string `json:"id"`
	// Email address for user (private)
	Email string `json:"email"`
	// Display / full name for user (private)
	Name string `json:"name"`
}

// GetId returns SetSecretsSetSecretsSetSecretsPayloadReleaseUser.Id, and is useful for accessing the field via an interface.
func (v *SetSecretsSetSecretsSetSecretsPayloadReleaseUser) GetId() string { return v.Id }

// GetEmail returns SetSecretsSetSecretsSetSecretsPayloadReleaseUser.Email, and is useful for accessing the field via an interface.
func (v *SetSecretsSetSecretsSetSecretsPayloadReleaseUser) GetEmail() string { return v.Email }

// GetName returns SetSecretsSetSecretsSetSecretsPayloadReleaseUser.Name, and is useful for accessing the field via an interface.
func (v *SetSecretsSetSecretsSetSecretsPayloadReleaseUser) GetName() string { return v.Name }

// Autogenerated input type of SetVMCount
type SetVMCountInput struct {
	// The ID of the app
	AppId string `json:"appId"`
	// A unique identifier for the client performing the mutation.
	ClientMutationId string `json:"clientMutationId"`
	// Counts for VM groups
	GroupCounts []VMCountInput `json:"groupCounts"`
	// Unique lock ID
	LockId string `json:"lockId"`
}

// GetAppId returns SetVMCountInput.AppId, and is useful for accessing the field via an interface.
func (v *SetVMCountInput) GetAppId() string { return v.AppId }

// GetClientMutationId returns SetVMCountInput.ClientMutationId, and is useful for accessing the field via an interface.
func (v *SetVMCountInput) GetClientMutationId() string { return v.ClientMutationId }

// GetGroupCounts returns SetVMCountInput.GroupCounts, and is useful for accessing the field via an interface.
func (v *SetVMCountInput) GetGroupCounts() []VMCountInput { return v.GroupCounts }

// GetLockId returns SetVMCountInput.LockId, and is useful for accessing the field via an interface.
func (v *SetVMCountInput) GetLockId() string { return v.LockId }

// UpdateAddOnResponse is returned by UpdateAddOn on success.
type UpdateAddOnResponse struct {
	UpdateAddOn UpdateAddOnUpdateAddOnUpdateAddOnPayload `json:"updateAddOn"`
}

// GetUpdateAddOn returns UpdateAddOnResponse.UpdateAddOn, and is useful for accessing the field via an interface.
func (v *UpdateAddOnResponse) GetUpdateAddOn() UpdateAddOnUpdateAddOnUpdateAddOnPayload {
	return v.UpdateAddOn
}

// UpdateAddOnUpdateAddOnUpdateAddOnPayload includes the requested fields of the GraphQL type UpdateAddOnPayload.
// The GraphQL type's documentation follows.
//
// Autogenerated return type of UpdateAddOn.
type UpdateAddOnUpdateAddOnUpdateAddOnPayload struct {
	AddOn UpdateAddOnUpdateAddOnUpdateAddOnPayloadAddOn `json:"addOn"`
}

// GetAddOn returns UpdateAddOnUpdateAddOnUpdateAddOnPayload.AddOn, and is useful for accessing the field via an interface.
func (v *UpdateAddOnUpdateAddOnUpdateAddOnPayload) GetAddOn() UpdateAddOnUpdateAddOnUpdateAddOnPayloadAddOn {
	return v.AddOn
}

// UpdateAddOnUpdateAddOnUpdateAddOnPayloadAddOn includes the requested fields of the GraphQL type AddOn.
type UpdateAddOnUpdateAddOnUpdateAddOnPayloadAddOn struct {
	Id string `json:"id"`
}

// GetId returns UpdateAddOnUpdateAddOnUpdateAddOnPayloadAddOn.Id, and is useful for accessing the field via an interface.
func (v *UpdateAddOnUpdateAddOnUpdateAddOnPayloadAddOn) GetId() string { return v.Id }

type VMCountInput struct {
	// The desired count
	Count int `json:"count"`
	// VM group name
	Group string `json:"group"`
	// Max number of VMs to allow per region
	MaxPerRegion int `json:"maxPerRegion"`
}

// GetCount returns VMCountInput.Count, and is useful for accessing the field via an interface.
func (v *VMCountInput) GetCount() int { return v.Count }

// GetGroup returns VMCountInput.Group, and is useful for accessing the field via an interface.
func (v *VMCountInput) GetGroup() string { return v.Group }

// GetMaxPerRegion returns VMCountInput.MaxPerRegion, and is useful for accessing the field via an interface.
func (v *VMCountInput) GetMaxPerRegion() int { return v.MaxPerRegion }

// __AgentGetInstancesInput is used internally by genqlient
type __AgentGetInstancesInput struct {
	AppName string `json:"appName"`
}

// GetAppName returns __AgentGetInstancesInput.AppName, and is useful for accessing the field via an interface.
func (v *__AgentGetInstancesInput) GetAppName() string { return v.AppName }

// __AgreedToProviderTosInput is used internally by genqlient
type __AgreedToProviderTosInput struct {
	AddOnProviderName string `json:"addOnProviderName"`
}

// GetAddOnProviderName returns __AgreedToProviderTosInput.AddOnProviderName, and is useful for accessing the field via an interface.
func (v *__AgreedToProviderTosInput) GetAddOnProviderName() string { return v.AddOnProviderName }

// __AllAppsInput is used internally by genqlient
type __AllAppsInput struct {
	OrgSlug string `json:"orgSlug"`
}

// GetOrgSlug returns __AllAppsInput.OrgSlug, and is useful for accessing the field via an interface.
func (v *__AllAppsInput) GetOrgSlug() string { return v.OrgSlug }

// __CreateAddOnInput is used internally by genqlient
type __CreateAddOnInput struct {
	Input CreateAddOnInput `json:"input"`
}

// GetInput returns __CreateAddOnInput.Input, and is useful for accessing the field via an interface.
func (v *__CreateAddOnInput) GetInput() CreateAddOnInput { return v.Input }

// __CreateAppInput is used internally by genqlient
type __CreateAppInput struct {
	Input CreateAppInput `json:"input"`
}

// GetInput returns __CreateAppInput.Input, and is useful for accessing the field via an interface.
func (v *__CreateAppInput) GetInput() CreateAppInput { return v.Input }

// __CreateExtensionInput is used internally by genqlient
type __CreateExtensionInput struct {
	Input CreateAddOnInput `json:"input"`
}

// GetInput returns __CreateExtensionInput.Input, and is useful for accessing the field via an interface.
func (v *__CreateExtensionInput) GetInput() CreateAddOnInput { return v.Input }

// __CreateLimitedAccessTokenInput is used internally by genqlient
type __CreateLimitedAccessTokenInput struct {
	Name           string      `json:"name"`
	OrganizationId string      `json:"organizationId"`
	Profile        string      `json:"profile"`
	ProfileParams  interface{} `json:"profileParams"`
	Expiry         string      `json:"expiry"`
}

// GetName returns __CreateLimitedAccessTokenInput.Name, and is useful for accessing the field via an interface.
func (v *__CreateLimitedAccessTokenInput) GetName() string { return v.Name }

// GetOrganizationId returns __CreateLimitedAccessTokenInput.OrganizationId, and is useful for accessing the field via an interface.
func (v *__CreateLimitedAccessTokenInput) GetOrganizationId() string { return v.OrganizationId }

// GetProfile returns __CreateLimitedAccessTokenInput.Profile, and is useful for accessing the field via an interface.
func (v *__CreateLimitedAccessTokenInput) GetProfile() string { return v.Profile }

// GetProfileParams returns __CreateLimitedAccessTokenInput.ProfileParams, and is useful for accessing the field via an interface.
func (v *__CreateLimitedAccessTokenInput) GetProfileParams() interface{} { return v.ProfileParams }

// GetExpiry returns __CreateLimitedAccessTokenInput.Expiry, and is useful for accessing the field via an interface.
func (v *__CreateLimitedAccessTokenInput) GetExpiry() string { return v.Expiry }

// __CreateTosAgreementInput is used internally by genqlient
type __CreateTosAgreementInput struct {
	ProviderName string `json:"providerName"`
}

// GetProviderName returns __CreateTosAgreementInput.ProviderName, and is useful for accessing the field via an interface.
func (v *__CreateTosAgreementInput) GetProviderName() string { return v.ProviderName }

// __DeleteAddOnInput is used internally by genqlient
type __DeleteAddOnInput struct {
	Name     string `json:"name"`
	Provider string `json:"provider"`
}

// GetName returns __DeleteAddOnInput.Name, and is useful for accessing the field via an interface.
func (v *__DeleteAddOnInput) GetName() string { return v.Name }

// GetProvider returns __DeleteAddOnInput.Provider, and is useful for accessing the field via an interface.
func (v *__DeleteAddOnInput) GetProvider() string { return v.Provider }

// __FlyctlConfigCurrentReleaseInput is used internally by genqlient
type __FlyctlConfigCurrentReleaseInput struct {
	AppName string `json:"appName"`
}

// GetAppName returns __FlyctlConfigCurrentReleaseInput.AppName, and is useful for accessing the field via an interface.
func (v *__FlyctlConfigCurrentReleaseInput) GetAppName() string { return v.AppName }

// __GetAddOnInput is used internally by genqlient
type __GetAddOnInput struct {
	Name     string `json:"name"`
	Provider string `json:"provider"`
}

// GetName returns __GetAddOnInput.Name, and is useful for accessing the field via an interface.
func (v *__GetAddOnInput) GetName() string { return v.Name }

// GetProvider returns __GetAddOnInput.Provider, and is useful for accessing the field via an interface.
func (v *__GetAddOnInput) GetProvider() string { return v.Provider }

// __GetAddOnProviderInput is used internally by genqlient
type __GetAddOnProviderInput struct {
	Name string `json:"name"`
}

// GetName returns __GetAddOnProviderInput.Name, and is useful for accessing the field via an interface.
func (v *__GetAddOnProviderInput) GetName() string { return v.Name }

// __GetAppInput is used internally by genqlient
type __GetAppInput struct {
	Name string `json:"name"`
}

// GetName returns __GetAppInput.Name, and is useful for accessing the field via an interface.
func (v *__GetAppInput) GetName() string { return v.Name }

// __GetAppWithAddonsInput is used internally by genqlient
type __GetAppWithAddonsInput struct {
	Name      string    `json:"name"`
	AddOnType AddOnType `json:"addOnType"`
}

// GetName returns __GetAppWithAddonsInput.Name, and is useful for accessing the field via an interface.
func (v *__GetAppWithAddonsInput) GetName() string { return v.Name }

// GetAddOnType returns __GetAppWithAddonsInput.AddOnType, and is useful for accessing the field via an interface.
func (v *__GetAppWithAddonsInput) GetAddOnType() AddOnType { return v.AddOnType }

// __GetAppsByRoleInput is used internally by genqlient
type __GetAppsByRoleInput struct {
	Role           string `json:"role"`
	OrganizationId string `json:"organizationId"`
}

// GetRole returns __GetAppsByRoleInput.Role, and is useful for accessing the field via an interface.
func (v *__GetAppsByRoleInput) GetRole() string { return v.Role }

// GetOrganizationId returns __GetAppsByRoleInput.OrganizationId, and is useful for accessing the field via an interface.
func (v *__GetAppsByRoleInput) GetOrganizationId() string { return v.OrganizationId }

// __GetExtensionSsoLinkInput is used internally by genqlient
type __GetExtensionSsoLinkInput struct {
	OrgSlug  string `json:"orgSlug"`
	Provider string `json:"provider"`
}

// GetOrgSlug returns __GetExtensionSsoLinkInput.OrgSlug, and is useful for accessing the field via an interface.
func (v *__GetExtensionSsoLinkInput) GetOrgSlug() string { return v.OrgSlug }

// GetProvider returns __GetExtensionSsoLinkInput.Provider, and is useful for accessing the field via an interface.
func (v *__GetExtensionSsoLinkInput) GetProvider() string { return v.Provider }

// __GetOrganizationInput is used internally by genqlient
type __GetOrganizationInput struct {
	Slug string `json:"slug"`
}

// GetSlug returns __GetOrganizationInput.Slug, and is useful for accessing the field via an interface.
func (v *__GetOrganizationInput) GetSlug() string { return v.Slug }

// __ListAddOnPlansInput is used internally by genqlient
type __ListAddOnPlansInput struct {
	AddOnType AddOnType `json:"addOnType"`
}

// GetAddOnType returns __ListAddOnPlansInput.AddOnType, and is useful for accessing the field via an interface.
func (v *__ListAddOnPlansInput) GetAddOnType() AddOnType { return v.AddOnType }

// __ListAddOnsInput is used internally by genqlient
type __ListAddOnsInput struct {
	AddOnType AddOnType `json:"addOnType"`
}

// GetAddOnType returns __ListAddOnsInput.AddOnType, and is useful for accessing the field via an interface.
func (v *__ListAddOnsInput) GetAddOnType() AddOnType { return v.AddOnType }

// __ResetAddOnPasswordInput is used internally by genqlient
type __ResetAddOnPasswordInput struct {
	Name string `json:"name"`
}

// GetName returns __ResetAddOnPasswordInput.Name, and is useful for accessing the field via an interface.
func (v *__ResetAddOnPasswordInput) GetName() string { return v.Name }

// __SetNomadVMCountInput is used internally by genqlient
type __SetNomadVMCountInput struct {
	Input SetVMCountInput `json:"input"`
}

// GetInput returns __SetNomadVMCountInput.Input, and is useful for accessing the field via an interface.
func (v *__SetNomadVMCountInput) GetInput() SetVMCountInput { return v.Input }

// __SetSecretsInput is used internally by genqlient
type __SetSecretsInput struct {
	Input SetSecretsInput `json:"input"`
}

// GetInput returns __SetSecretsInput.Input, and is useful for accessing the field via an interface.
func (v *__SetSecretsInput) GetInput() SetSecretsInput { return v.Input }

// __UpdateAddOnInput is used internally by genqlient
type __UpdateAddOnInput struct {
	AddOnId     string      `json:"addOnId"`
	PlanId      string      `json:"planId"`
	ReadRegions []string    `json:"readRegions"`
	Options     interface{} `json:"options"`
	Metadata    interface{} `json:"metadata"`
}

// GetAddOnId returns __UpdateAddOnInput.AddOnId, and is useful for accessing the field via an interface.
func (v *__UpdateAddOnInput) GetAddOnId() string { return v.AddOnId }

// GetPlanId returns __UpdateAddOnInput.PlanId, and is useful for accessing the field via an interface.
func (v *__UpdateAddOnInput) GetPlanId() string { return v.PlanId }

// GetReadRegions returns __UpdateAddOnInput.ReadRegions, and is useful for accessing the field via an interface.
func (v *__UpdateAddOnInput) GetReadRegions() []string { return v.ReadRegions }

// GetOptions returns __UpdateAddOnInput.Options, and is useful for accessing the field via an interface.
func (v *__UpdateAddOnInput) GetOptions() interface{} { return v.Options }

// GetMetadata returns __UpdateAddOnInput.Metadata, and is useful for accessing the field via an interface.
func (v *__UpdateAddOnInput) GetMetadata() interface{} { return v.Metadata }

// The query executed by AgentGetInstances.
const AgentGetInstances_Operation = `
query AgentGetInstances ($appName: String!) {
	app(name: $appName) {
		organization {
			slug
		}
		id
		name
		allocations(showCompleted: false) {
			id
			region
			privateIP
		}
		machines {
			nodes {
				state
				id
				region
				ips {
					nodes {
						kind
						family
						ip
					}
				}
			}
		}
	}
}
`

func AgentGetInstances(
	ctx_ context.Context,
	client_ graphql.Client,
	appName string,
) (data_ *AgentGetInstancesResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "AgentGetInstances",
		Query:  AgentGetInstances_Operation,
		Variables: &__AgentGetInstancesInput{
			AppName: appName,
		},
	}

	data_ = &AgentGetInstancesResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

// The query executed by AgreedToProviderTos.
const AgreedToProviderTos_Operation = `
query AgreedToProviderTos ($addOnProviderName: String!) {
	viewer {
		__typename
		... on User {
			agreedToProviderTos(providerName: $addOnProviderName)
		}
	}
}
`

func AgreedToProviderTos(
	ctx_ context.Context,
	client_ graphql.Client,
	addOnProviderName string,
) (data_ *AgreedToProviderTosResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "AgreedToProviderTos",
		Query:  AgreedToProviderTos_Operation,
		Variables: &__AgreedToProviderTosInput{
			AddOnProviderName: addOnProviderName,
		},
	}

	data_ = &AgreedToProviderTosResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

// The query executed by AllApps.
const AllApps_Operation = `
query AllApps ($orgSlug: String!) {
	organization(slug: $orgSlug) {
		apps {
			nodes {
				id
				createdAt
			}
		}
	}
}
`

func AllApps(
	ctx_ context.Context,
	client_ graphql.Client,
	orgSlug string,
) (data_ *AllAppsResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "AllApps",
		Query:  AllApps_Operation,
		Variables: &__AllAppsInput{
			OrgSlug: orgSlug,
		},
	}

	data_ = &AllAppsResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

// The mutation executed by CreateAddOn.
const CreateAddOn_Operation = `
mutation CreateAddOn ($input: CreateAddOnInput!) {
	createAddOn(input: $input) {
		addOn {
			name
			publicUrl
			ssoLink
			environment
			primaryRegion
		}
	}
}
`

func CreateAddOn(
	ctx_ context.Context,
	client_ graphql.Client,
	input CreateAddOnInput,
) (data_ *CreateAddOnResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "CreateAddOn",
		Query:  CreateAddOn_Operation,
		Variables: &__CreateAddOnInput{
			Input: input,
		},
	}

	data_ = &CreateAddOnResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

// The mutation executed by CreateApp.
const CreateApp_Operation = `
mutation CreateApp ($input: CreateAppInput!) {
	createApp(input: $input) {
		app {
			... AppData
			config {
				definition
			}
			regions {
				name
				code
			}
		}
	}
}
fragment AppData on App {
	id
	name
	deployed
	platformVersion
	secrets {
		name
	}
	organization {
		... OrganizationData
	}
}
fragment OrganizationData on Organization {
	id
	slug
	rawSlug
	paidPlan
	addOnSsoLink
	provisionsBetaExtensions
}
`

func CreateApp(
	ctx_ context.Context,
	client_ graphql.Client,
	input CreateAppInput,
) (data_ *CreateAppResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "CreateApp",
		Query:  CreateApp_Operation,
		Variables: &__CreateAppInput{
			Input: input,
		},
	}

	data_ = &CreateAppResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

// The mutation executed by CreateExtension.
const CreateExtension_Operation = `
mutation CreateExtension ($input: CreateAddOnInput!) {
	createAddOn(input: $input) {
		addOn {
			... ExtensionData
		}
	}
}
fragment ExtensionData on AddOn {
	name
	ssoLink
	environment
	primaryRegion
}
`

func CreateExtension(
	ctx_ context.Context,
	client_ graphql.Client,
	input CreateAddOnInput,
) (data_ *CreateExtensionResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "CreateExtension",
		Query:  CreateExtension_Operation,
		Variables: &__CreateExtensionInput{
			Input: input,
		},
	}

	data_ = &CreateExtensionResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

// The mutation executed by CreateLimitedAccessToken.
const CreateLimitedAccessToken_Operation = `
mutation CreateLimitedAccessToken ($name: String!, $organizationId: ID!, $profile: String!, $profileParams: JSON, $expiry: String!) {
	createLimitedAccessToken(input: {name:$name,organizationId:$organizationId,profile:$profile,profileParams:$profileParams,expiry:$expiry}) {
		limitedAccessToken {
			tokenHeader
		}
	}
}
`

func CreateLimitedAccessToken(
	ctx_ context.Context,
	client_ graphql.Client,
	name string,
	organizationId string,
	profile string,
	profileParams interface{},
	expiry string,
) (data_ *CreateLimitedAccessTokenResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "CreateLimitedAccessToken",
		Query:  CreateLimitedAccessToken_Operation,
		Variables: &__CreateLimitedAccessTokenInput{
			Name:           name,
			OrganizationId: organizationId,
			Profile:        profile,
			ProfileParams:  profileParams,
			Expiry:         expiry,
		},
	}

	data_ = &CreateLimitedAccessTokenResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

// The mutation executed by CreateTosAgreement.
const CreateTosAgreement_Operation = `
mutation CreateTosAgreement ($providerName: String!) {
	createExtensionTosAgreement(input: {addOnProviderName:$providerName}) {
		clientMutationId
	}
}
`

func CreateTosAgreement(
	ctx_ context.Context,
	client_ graphql.Client,
	providerName string,
) (data_ *CreateTosAgreementResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "CreateTosAgreement",
		Query:  CreateTosAgreement_Operation,
		Variables: &__CreateTosAgreementInput{
			ProviderName: providerName,
		},
	}

	data_ = &CreateTosAgreementResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

// The mutation executed by DeleteAddOn.
const DeleteAddOn_Operation = `
mutation DeleteAddOn ($name: String, $provider: String) {
	deleteAddOn(input: {name:$name,provider:$provider}) {
		deletedAddOnName
	}
}
`

func DeleteAddOn(
	ctx_ context.Context,
	client_ graphql.Client,
	name string,
	provider string,
) (data_ *DeleteAddOnResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "DeleteAddOn",
		Query:  DeleteAddOn_Operation,
		Variables: &__DeleteAddOnInput{
			Name:     name,
			Provider: provider,
		},
	}

	data_ = &DeleteAddOnResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

// The query executed by FlyctlConfigCurrentRelease.
const FlyctlConfigCurrentRelease_Operation = `
query FlyctlConfigCurrentRelease ($appName: String!) {
	app(name: $appName) {
		currentReleaseUnprocessed {
			configDefinition
		}
	}
}
`

func FlyctlConfigCurrentRelease(
	ctx_ context.Context,
	client_ graphql.Client,
	appName string,
) (data_ *FlyctlConfigCurrentReleaseResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "FlyctlConfigCurrentRelease",
		Query:  FlyctlConfigCurrentRelease_Operation,
		Variables: &__FlyctlConfigCurrentReleaseInput{
			AppName: appName,
		},
	}

	data_ = &FlyctlConfigCurrentReleaseResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

// The query executed by GetAddOn.
const GetAddOn_Operation = `
query GetAddOn ($name: String, $provider: String) {
	addOn(name: $name, provider: $provider) {
		... AddOnData
		publicUrl
		privateIp
		password
		status
		primaryRegion
		readRegions
		options
		metadata
		ssoLink
		organization {
			slug
			paidPlan
		}
		addOnProvider {
			... ExtensionProviderData
		}
		app {
			... AppData
		}
		addOnPlan {
			id
			name
			displayName
			description
		}
	}
}
fragment AddOnData on AddOn {
	id
	name
	primaryRegion
	status
	errorMessage
	metadata
	options
}
fragment ExtensionProviderData on AddOnProvider {
	id
	name
	displayName
	tosUrl
	asyncProvisioning
	autoProvision
	selectName
	selectRegion
	selectReplicaRegions
	detectPlatform
	resourceName
	nameSuffix
	beta
	tosAgreement
	internal
	provisioningInstructions
	excludedRegions {
		code
	}
}
fragment AppData on App {
	id
	name
	deployed
	platformVersion
	secrets {
		name
	}
	organization {
		... OrganizationData
	}
}
fragment OrganizationData on Organization {
	id
	slug
	rawSlug
	paidPlan
	addOnSsoLink
	provisionsBetaExtensions
}
`

func GetAddOn(
	ctx_ context.Context,
	client_ graphql.Client,
	name string,
	provider string,
) (data_ *GetAddOnResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "GetAddOn",
		Query:  GetAddOn_Operation,
		Variables: &__GetAddOnInput{
			Name:     name,
			Provider: provider,
		},
	}

	data_ = &GetAddOnResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

// The query executed by GetAddOnProvider.
const GetAddOnProvider_Operation = `
query GetAddOnProvider ($name: String!) {
	addOnProvider(name: $name) {
		... ExtensionProviderData
	}
}
fragment ExtensionProviderData on AddOnProvider {
	id
	name
	displayName
	tosUrl
	asyncProvisioning
	autoProvision
	selectName
	selectRegion
	selectReplicaRegions
	detectPlatform
	resourceName
	nameSuffix
	beta
	tosAgreement
	internal
	provisioningInstructions
	excludedRegions {
		code
	}
}
`

func GetAddOnProvider(
	ctx_ context.Context,
	client_ graphql.Client,
	name string,
) (data_ *GetAddOnProviderResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "GetAddOnProvider",
		Query:  GetAddOnProvider_Operation,
		Variables: &__GetAddOnProviderInput{
			Name: name,
		},
	}

	data_ = &GetAddOnProviderResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

// The query executed by GetApp.
const GetApp_Operation = `
query GetApp ($name: String!) {
	app(name: $name) {
		... AppData
	}
}
fragment AppData on App {
	id
	name
	deployed
	platformVersion
	secrets {
		name
	}
	organization {
		... OrganizationData
	}
}
fragment OrganizationData on Organization {
	id
	slug
	rawSlug
	paidPlan
	addOnSsoLink
	provisionsBetaExtensions
}
`

func GetApp(
	ctx_ context.Context,
	client_ graphql.Client,
	name string,
) (data_ *GetAppResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "GetApp",
		Query:  GetApp_Operation,
		Variables: &__GetAppInput{
			Name: name,
		},
	}

	data_ = &GetAppResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

// The query executed by GetAppWithAddons.
const GetAppWithAddons_Operation = `
query GetAppWithAddons ($name: String!, $addOnType: AddOnType!) {
	app(name: $name) {
		... AppData
		addOns(type: $addOnType) {
			nodes {
				... AddOnData
			}
		}
	}
}
fragment AppData on App {
	id
	name
	deployed
	platformVersion
	secrets {
		name
	}
	organization {
		... OrganizationData
	}
}
fragment AddOnData on AddOn {
	id
	name
	primaryRegion
	status
	errorMessage
	metadata
	options
}
fragment OrganizationData on Organization {
	id
	slug
	rawSlug
	paidPlan
	addOnSsoLink
	provisionsBetaExtensions
}
`

func GetAppWithAddons(
	ctx_ context.Context,
	client_ graphql.Client,
	name string,
	addOnType AddOnType,
) (data_ *GetAppWithAddonsResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "GetAppWithAddons",
		Query:  GetAppWithAddons_Operation,
		Variables: &__GetAppWithAddonsInput{
			Name:      name,
			AddOnType: addOnType,
		},
	}

	data_ = &GetAppWithAddonsResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

// The query executed by GetAppsByRole.
const GetAppsByRole_Operation = `
query GetAppsByRole ($role: String!, $organizationId: ID!) {
	apps(role: $role, organizationId: $organizationId) {
		nodes {
			... AppData
		}
	}
}
fragment AppData on App {
	id
	name
	deployed
	platformVersion
	secrets {
		name
	}
	organization {
		... OrganizationData
	}
}
fragment OrganizationData on Organization {
	id
	slug
	rawSlug
	paidPlan
	addOnSsoLink
	provisionsBetaExtensions
}
`

func GetAppsByRole(
	ctx_ context.Context,
	client_ graphql.Client,
	role string,
	organizationId string,
) (data_ *GetAppsByRoleResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "GetAppsByRole",
		Query:  GetAppsByRole_Operation,
		Variables: &__GetAppsByRoleInput{
			Role:           role,
			OrganizationId: organizationId,
		},
	}

	data_ = &GetAppsByRoleResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

// The query executed by GetExtensionSsoLink.
const GetExtensionSsoLink_Operation = `
query GetExtensionSsoLink ($orgSlug: String!, $provider: String!) {
	organization(slug: $orgSlug) {
		extensionSsoLink(provider: $provider)
	}
}
`

func GetExtensionSsoLink(
	ctx_ context.Context,
	client_ graphql.Client,
	orgSlug string,
	provider string,
) (data_ *GetExtensionSsoLinkResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "GetExtensionSsoLink",
		Query:  GetExtensionSsoLink_Operation,
		Variables: &__GetExtensionSsoLinkInput{
			OrgSlug:  orgSlug,
			Provider: provider,
		},
	}

	data_ = &GetExtensionSsoLinkResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

// The query executed by GetNearestRegion.
const GetNearestRegion_Operation = `
query GetNearestRegion {
	nearestRegion {
		code
		name
		gatewayAvailable
	}
}
`

func GetNearestRegion(
	ctx_ context.Context,
	client_ graphql.Client,
) (data_ *GetNearestRegionResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "GetNearestRegion",
		Query:  GetNearestRegion_Operation,
	}

	data_ = &GetNearestRegionResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

// The query executed by GetOrganization.
const GetOrganization_Operation = `
query GetOrganization ($slug: String!) {
	organization(slug: $slug) {
		... OrganizationData
	}
}
fragment OrganizationData on Organization {
	id
	slug
	rawSlug
	paidPlan
	addOnSsoLink
	provisionsBetaExtensions
}
`

func GetOrganization(
	ctx_ context.Context,
	client_ graphql.Client,
	slug string,
) (data_ *GetOrganizationResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "GetOrganization",
		Query:  GetOrganization_Operation,
		Variables: &__GetOrganizationInput{
			Slug: slug,
		},
	}

	data_ = &GetOrganizationResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

// The query executed by ListAddOnPlans.
const ListAddOnPlans_Operation = `
query ListAddOnPlans ($addOnType: AddOnType!) {
	addOnPlans(type: $addOnType) {
		nodes {
			id
			description
			displayName
			maxDataSize
			pricePerMonth
		}
	}
}
`

func ListAddOnPlans(
	ctx_ context.Context,
	client_ graphql.Client,
	addOnType AddOnType,
) (data_ *ListAddOnPlansResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "ListAddOnPlans",
		Query:  ListAddOnPlans_Operation,
		Variables: &__ListAddOnPlansInput{
			AddOnType: addOnType,
		},
	}

	data_ = &ListAddOnPlansResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

// The query executed by ListAddOns.
const ListAddOns_Operation = `
query ListAddOns ($addOnType: AddOnType) {
	addOns(type: $addOnType) {
		nodes {
			id
			name
			addOnPlan {
				displayName
				description
			}
			privateIp
			primaryRegion
			readRegions
			options
			metadata
			organization {
				id
				slug
			}
		}
	}
}
`

func ListAddOns(
	ctx_ context.Context,
	client_ graphql.Client,
	addOnType AddOnType,
) (data_ *ListAddOnsResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "ListAddOns",
		Query:  ListAddOns_Operation,
		Variables: &__ListAddOnsInput{
			AddOnType: addOnType,
		},
	}

	data_ = &ListAddOnsResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

// The mutation executed by LogOut.
const LogOut_Operation = `
mutation LogOut {
	logOut(input: {}) {
		ok
	}
}
`

func LogOut(
	ctx_ context.Context,
	client_ graphql.Client,
) (data_ *LogOutResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "LogOut",
		Query:  LogOut_Operation,
	}

	data_ = &LogOutResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

// The mutation executed by ResetAddOnPassword.
const ResetAddOnPassword_Operation = `
mutation ResetAddOnPassword ($name: String!) {
	resetAddOnPassword(input: {name:$name}) {
		addOn {
			publicUrl
		}
	}
}
`

func ResetAddOnPassword(
	ctx_ context.Context,
	client_ graphql.Client,
	name string,
) (data_ *ResetAddOnPasswordResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "ResetAddOnPassword",
		Query:  ResetAddOnPassword_Operation,
		Variables: &__ResetAddOnPasswordInput{
			Name: name,
		},
	}

	data_ = &ResetAddOnPasswordResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

// The mutation executed by SetNomadVMCount.
const SetNomadVMCount_Operation = `
mutation SetNomadVMCount ($input: SetVMCountInput!) {
	setVmCount(input: $input) {
		taskGroupCounts {
			name
			count
		}
		warnings
	}
}
`

func SetNomadVMCount(
	ctx_ context.Context,
	client_ graphql.Client,
	input SetVMCountInput,
) (data_ *SetNomadVMCountResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "SetNomadVMCount",
		Query:  SetNomadVMCount_Operation,
		Variables: &__SetNomadVMCountInput{
			Input: input,
		},
	}

	data_ = &SetNomadVMCountResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

// The mutation executed by SetSecrets.
const SetSecrets_Operation = `
mutation SetSecrets ($input: SetSecretsInput!) {
	setSecrets(input: $input) {
		release {
			id
			version
			reason
			description
			user {
				id
				email
				name
			}
			evaluationId
			createdAt
		}
	}
}
`

func SetSecrets(
	ctx_ context.Context,
	client_ graphql.Client,
	input SetSecretsInput,
) (data_ *SetSecretsResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "SetSecrets",
		Query:  SetSecrets_Operation,
		Variables: &__SetSecretsInput{
			Input: input,
		},
	}

	data_ = &SetSecretsResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

// The mutation executed by UpdateAddOn.
const UpdateAddOn_Operation = `
mutation UpdateAddOn ($addOnId: ID!, $planId: ID!, $readRegions: [String!]!, $options: JSON!, $metadata: JSON!) {
	updateAddOn(input: {addOnId:$addOnId,planId:$planId,readRegions:$readRegions,options:$options,metadata:$metadata}) {
		addOn {
			id
		}
	}
}
`

func UpdateAddOn(
	ctx_ context.Context,
	client_ graphql.Client,
	addOnId string,
	planId string,
	readRegions []string,
	options interface{},
	metadata interface{},
) (data_ *UpdateAddOnResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "UpdateAddOn",
		Query:  UpdateAddOn_Operation,
		Variables: &__UpdateAddOnInput{
			AddOnId:     addOnId,
			PlanId:      planId,
			ReadRegions: readRegions,
			Options:     options,
			Metadata:    metadata,
		},
	}

	data_ = &UpdateAddOnResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

fragment AddOnData on AddOn {
	id
	name
	primaryRegion
	status
	errorMessage
	metadata
	options
}

query GetAddOn($name: String, $provider: String) {
	addOn(name: $name, provider: $provider) {
		...AddOnData
		publicUrl
		privateIp
		password
		status
		primaryRegion
		readRegions
		options
		metadata
		ssoLink
		organization {
			slug
			paidPlan
		}
		addOnProvider {
			...ExtensionProviderData
		}
		app {
			...AppData
		}
		addOnPlan {
			id
			name
			displayName
			description
		}
	}
}

mutation CreateAddOn($input: CreateAddOnInput!) {
	createAddOn(input: $input) {
		addOn {
			name
			publicUrl
			ssoLink
			environment
			primaryRegion
		}
	}
}

fragment ExtensionData on AddOn {
	name
	ssoLink
	environment
	primaryRegion
}


mutation CreateExtension($input: CreateAddOnInput!) {
	createAddOn(input: $input) {
		addOn {
			...ExtensionData
		}
	}
}

mutation CreateApp($input: CreateAppInput!) {
	createApp(input: $input) {
		app {
			...AppData
			config {
				definition
			}
			regions {
					name
					code
			}
		}
	}
}

mutation CreateTosAgreement($providerName: String!) {
	createExtensionTosAgreement(input: {addOnProviderName: $providerName}) {
		clientMutationId
	}
}

query AgreedToProviderTos($addOnProviderName: String!) {
	viewer {
		... on User {
			agreedToProviderTos(providerName: $addOnProviderName)
		}
	}
}

query GetOrganization($slug: String!) {
	organization(slug: $slug) {
		...OrganizationData
	}
}

query GetApp($name: String!) {
	app(name: $name) {
		...AppData
	}
}

query GetAppWithAddons($name: String!, $addOnType: AddOnType!) {
	app(name: $name) {
		...AppData
		addOns(type: $addOnType) {
			nodes {
				...AddOnData
			}
		}
	}
}

query GetAppsByRole($role: String!, $organizationId: ID!) {
	apps(role: $role, organizationId: $organizationId) {
		nodes {
		...AppData
		}
	}
}

query GetExtensionSsoLink($orgSlug: String!, $provider: String!) {
	organization(slug: $orgSlug) {
		extensionSsoLink(provider: $provider)
	}
}

fragment OrganizationData on Organization {
	id
	slug
	rawSlug
	paidPlan
	addOnSsoLink
	provisionsBetaExtensions
}

fragment AppData on App {
	id
	name
	deployed
	platformVersion
	secrets {
		name
	}
	organization {
		...OrganizationData
	}
}

mutation SetSecrets($input: SetSecretsInput!) {
	setSecrets(input: $input) {
		release {
			id
			version
			reason
			description
			user {
				id
				email
				name
			}
			evaluationId
			createdAt
		}
	}
}

query GetNearestRegion{
	nearestRegion {
		code
		name
		gatewayAvailable
	}
}

mutation CreateLimitedAccessToken($name: String!, $organizationId: ID!, $profile: String!, $profileParams: JSON, $expiry: String!) {
	createLimitedAccessToken(input: {name: $name, organizationId: $organizationId, profile: $profile, profileParams: $profileParams, expiry: $expiry}) {
		limitedAccessToken {
			tokenHeader
		}
	}
}

mutation LogOut {
	logOut(input: {}) {
		ok
	}
}

mutation SetNomadVMCount($input: SetVMCountInput!) {
	setVmCount(input: $input) {
		taskGroupCounts {
			name
			count
		}
		warnings
	}
}

mutation DeleteAddOn($name: String, $provider: String) {
	deleteAddOn(input: {name: $name, provider: $provider}) {
		deletedAddOnName
	}
}

fragment ExtensionProviderData on AddOnProvider {
	id
	name
	displayName
	tosUrl
	asyncProvisioning
  autoProvision
  selectName
  selectRegion
  selectReplicaRegions
  detectPlatform
  resourceName
	nameSuffix
	beta
	tosAgreement
	internal
	provisioningInstructions
	excludedRegions {
		code
	}
}
query GetAddOnProvider($name: String!) {
	addOnProvider(name: $name) {
		...ExtensionProviderData
	}
}

query ListAddOns($addOnType: AddOnType) {
	addOns(type: $addOnType) {
		nodes {
			id
			name
			addOnPlan {
				displayName
				description
			}
			privateIp
			primaryRegion
			readRegions
			options
			metadata
			organization {
				id
				slug
			}
		}
	}
}

 mutation UpdateAddOn($addOnId: ID!, $planId: ID!, $readRegions: [String!]!, $options: JSON!, $metadata: JSON!) {
		updateAddOn(input: {addOnId: $addOnId, planId: $planId, readRegions: $readRegions, options: $options, metadata: $metadata}) {
			addOn {
				id
			}
		}
  }

 query ListAddOnPlans($addOnType: AddOnType!) {
		addOnPlans(type: $addOnType) {
			nodes {
				id
				description
				displayName
			  maxDataSize
				pricePerMonth
			}
		}
  }

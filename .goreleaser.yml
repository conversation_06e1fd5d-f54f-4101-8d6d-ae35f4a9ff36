env:
  - BUILD_ENV={{if index .Env "BUILD_ENV"}}{{.Env.BUILD_ENV}}{{else}}production{{end}}

before:
  hooks:
    - go mod download
    - go generate ./...

builds:
  - id: default
    env:
      - CGO_ENABLED=0
    goos:
      - darwin
      - linux
    ignore:
      - goos: darwin
        goarch: 386
      - goos: linux
        goarch: 386
    ldflags:
      - -X github.com/superfly/flyctl/internal/buildinfo.buildDate={{.CommitDate}}
      - -X github.com/superfly/flyctl/internal/buildinfo.buildVersion={{.Version}}
      - -X github.com/superfly/flyctl/internal/buildinfo.commit={{.ShortCommit}}
    tags:
      - "{{.Env.BUILD_ENV}}"

  - id: windows
    env:
      - CGO_ENABLED=0
    goos:
      - windows
    ignore:
      - goos: windows
        goarch: 386
    ldflags:
      - -X github.com/superfly/flyctl/internal/buildinfo.buildDate={{.CommitDate}}
      - -X github.com/superfly/flyctl/internal/buildinfo.buildVersion={{.Version}}
      - -X github.com/superfly/flyctl/internal/buildinfo.commit={{.ShortCommit}}
    tags:
      - "{{.Env.BUILD_ENV}}"

archives:
  - id: windows
    name_template: >-
      {{ .ProjectName }}_{{ .Version}}_
      {{- if eq .Os "windows" }}Windows
      {{- else }}{{ .Os }}{{- end }}_
      {{- if eq .Arch "amd64" }}x86_64
      {{- else }}{{ .Arch }}{{- end }}
    builds:
      - windows
    files:
      - wintun.dll
    wrap_in_directory: false
    format: zip

  - id: default
    name_template: >-
      {{ .ProjectName }}_{{ .Version}}_
      {{- if eq .Os "darwin" }}macOS
      {{- else if eq .Os "linux" }}Linux
      {{- else }}{{ .Os }}{{- end }}_
      {{- if eq .Arch "amd64" }}x86_64
      {{- else }}{{ .Arch }}{{- end }}
    builds:
      - default
    files: [only-the-binary*]
    wrap_in_directory: false
    format: tar.gz

release:
  prerelease: auto

dockers:
  - goos: linux
    goarch: amd64
    image_templates:
      - "flyio/flyctl:latest"
      - "flyio/flyctl:v{{ .Version }}"
      - "ghcr.io/superfly/flyctl:latest"
      - "ghcr.io/superfly/flyctl:v{{ .Version }}"
    skip_push: auto
  - goos: linux
    goarch: amd64
    dockerfile: Dockerfile.mcp
    image_templates:
      - "flyio/mcp:latest"
      - "flyio/mcp:v{{ .Version }}"
      - "ghcr.io/superfly/mcp:latest"
      - "ghcr.io/superfly/mcp:v{{ .Version }}"
    skip_push: auto

checksum:
  name_template: "checksums.txt"

snapshot:
  name_template: "{{.Version}}-{{.Branch}}.{{.CommitTimestamp}}"

changelog:
  sort: asc
  filters:
    exclude:
      - "^docs:"
      - "^test:"

brews:
  - repository:
      owner: superfly
      name: homebrew-tap
    folder: Formula
    homepage: https://fly.io
    skip_upload: auto
    test: |
      system "#{bin}/flyctl version"
    install: |
      bin.install "flyctl"
      bin.install_symlink "flyctl" => "fly"

      bash_output = Utils.safe_popen_read("#{bin}/flyctl", "completion", "bash")
      (bash_completion/"flyctl").write bash_output
      zsh_output = Utils.safe_popen_read("#{bin}/flyctl", "completion", "zsh")
      (zsh_completion/"_flyctl").write zsh_output
      fish_output = Utils.safe_popen_read("#{bin}/flyctl", "completion", "fish")
      (fish_completion/"flyctl.fish").write fish_output

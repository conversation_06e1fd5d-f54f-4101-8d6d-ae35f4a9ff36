/**
 * Function to get machine logs using Fly.io GraphQL API
 * Based on analysis of the fly.io CLI codebase
 *
 * CLI command equivalent: fly logs -a appName -i machineId
 * Example: fly logs -a 9b1deb4d-3b7d-4bad-9bdd-2b0d7b3dcb6d -i e2860112ce1258
 */

interface LogEntry {
  id: string;
  instanceId: string;
  level: string;
  message: string;
  region: string;
  timestamp: string;
}

interface MachineLogsResponse {
  logs: LogEntry[];
  success: boolean;
  error?: string;
}

/**
 * Class method to be added to your existing Fly.io service class
 * This follows the same pattern as your allocateSharedIPv4 method
 */

/**
 * Get logs for a specific machine using GraphQL
 * This method should be added to your existing service class that has this.flyAxios
 *
 * @param appName - The name of the app
 * @param machineId - The ID of the machine/instance
 * @param limit - Maximum number of log entries to return (default: 100)
 * @param range - Max age of log entries in seconds (default: 3600 = 1 hour)
 * @returns Promise<MachineLogsResponse>
 */
async getMachineLogs(
  appName: string,
  machineId: string,
  limit: number = 100,
  range: number = 3600
): Promise<MachineLogsResponse> {
  try {
    console.log(`Getting logs for machine: ${machineId} in app: ${appName}`);

    // GraphQL query to get app allocations and their recent logs
    const query = `
      query GetAppMachineLogs($appName: String!, $machineId: String!, $limit: Int!, $range: Int!) {
        app(name: $appName) {
          id
          name
          allocations(showCompleted: true) {
            id
            idShort
            region
            status
            recentLogs(limit: $limit, range: $range) {
              id
              instanceId
              level
              message
              region
              timestamp
            }
          }
        }
      }
    `;

    const variables = {
      appName,
      machineId,
      limit,
      range,
    };

    // Make the GraphQL request
    const response = await this.flyAxios.graphql.post('', {
      query,
      variables,
    });

    if (response.data.errors?.length) {
      throw new Error(
        `GraphQL error: ${JSON.stringify(response.data.errors)}`,
      );
    }

    const app = response.data.data?.app;
    if (!app) {
      throw new Error(`App '${appName}' not found`);
    }

    // Find the specific machine/allocation
    const allocation = app.allocations.find(
      (alloc: any) => alloc.id === machineId || alloc.idShort === machineId
    );

    if (!allocation) {
      throw new Error(`Machine '${machineId}' not found in app '${appName}'`);
    }

    const logs: LogEntry[] = allocation.recentLogs.map((log: any) => ({
      id: log.id,
      instanceId: log.instanceId,
      level: log.level,
      message: log.message,
      region: log.region,
      timestamp: log.timestamp,
    }));

    console.log(`✅ Retrieved ${logs.length} log entries for machine ${machineId}`);
    
    return {
      logs,
      success: true,
    };
  } catch (error) {
    console.error('❌ Failed to get machine logs:', error);
    return {
      logs: [],
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Alternative approach: Get logs for all machines in an app, then filter
 * This might be more reliable if the direct machine lookup doesn't work
 */
async function getAllAppLogsFilteredByMachine(
  appName: string,
  machineId?: string,
  limit: number = 100,
  range: number = 3600
): Promise<MachineLogsResponse> {
  try {
    console.log(`Getting all logs for app: ${appName}${machineId ? ` filtered by machine: ${machineId}` : ''}`);

    const query = `
      query GetAllAppLogs($appName: String!, $limit: Int!, $range: Int!) {
        app(name: $appName) {
          id
          name
          allocations(showCompleted: true) {
            id
            idShort
            region
            status
            recentLogs(limit: $limit, range: $range) {
              id
              instanceId
              level
              message
              region
              timestamp
            }
          }
        }
      }
    `;

    const variables = {
      appName,
      limit,
      range,
    };

    const response = await this.flyAxios.graphql.post('', {
      query,
      variables,
    });

    if (response.data.errors?.length) {
      throw new Error(
        `GraphQL error: ${JSON.stringify(response.data.errors)}`,
      );
    }

    const app = response.data.data?.app;
    if (!app) {
      throw new Error(`App '${appName}' not found`);
    }

    let allLogs: LogEntry[] = [];

    // Collect logs from all allocations
    for (const allocation of app.allocations) {
      const allocationLogs = allocation.recentLogs.map((log: any) => ({
        id: log.id,
        instanceId: log.instanceId,
        level: log.level,
        message: log.message,
        region: log.region,
        timestamp: log.timestamp,
        allocationId: allocation.id,
        allocationIdShort: allocation.idShort,
      }));
      allLogs.push(...allocationLogs);
    }

    // Filter by machine ID if provided
    if (machineId) {
      allLogs = allLogs.filter(
        (log: any) => 
          log.instanceId === machineId || 
          log.allocationId === machineId || 
          log.allocationIdShort === machineId
      );
    }

    // Sort by timestamp (newest first)
    allLogs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    console.log(`✅ Retrieved ${allLogs.length} log entries`);
    
    return {
      logs: allLogs,
      success: true,
    };
  } catch (error) {
    console.error('❌ Failed to get app logs:', error);
    return {
      logs: [],
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Main function that tries the direct approach first, then falls back to the broader approach
 */
async function getLogsForMachine(
  appName: string,
  machineId: string,
  limit: number = 100,
  range: number = 3600
): Promise<MachineLogsResponse> {
  // Try direct machine lookup first
  const directResult = await getMachineLogs(appName, machineId, limit, range);
  
  if (directResult.success && directResult.logs.length > 0) {
    return directResult;
  }

  // Fall back to getting all logs and filtering
  console.log('Direct lookup failed or returned no logs, trying broader approach...');
  return getAllAppLogsFilteredByMachine(appName, machineId, limit, range);
}

// Export the main function
export { getLogsForMachine, getMachineLogs, getAllAppLogsFilteredByMachine };
export type { LogEntry, MachineLogsResponse };

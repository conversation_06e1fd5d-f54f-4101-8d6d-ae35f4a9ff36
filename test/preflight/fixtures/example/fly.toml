app = "damp-fire-3810"

[deploy]
release_command = "sh /release.sh"

[env]
LOG_LEVEL        = "debug"
ROOTFS_BUILD_DIR = "/rootfsdata"
QUEUES           = "bubblegum"

[[services]]
internal_port = 8080
protocol      = "tcp"

  [services.concurrency]
  hard_limit = 70
  soft_limit = 50

  [[services.http_checks]]
  interval        = 10000
  method          = "get"
  path            = "/"
  protocol        = "http"
  timeout         = 2000
  tls_skip_verify = false

    [services.http_checks.headers]

  [[services.ports]]
  handlers = [ "tls", "http" ]
  port     = "443"

  [[services.ports]]
  handlers = [ "http" ]
  port     = "80"

  [[services.tcp_checks]]
  interval = 10000
  timeout  = 2000

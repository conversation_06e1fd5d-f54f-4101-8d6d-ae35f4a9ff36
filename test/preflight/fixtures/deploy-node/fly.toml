app = "{{apps.0}}"
primary_region = '{{region}}'

[build]
  dockerfile = 'Dockerfile'

[deploy]
  release_command = "sleep 2"

[env]
  TEST_ID = "{{test.id}}"

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = "stop"
  auto_start_machines = true
  min_machines_running = 0
  processes = ['app']

  [[http_service.checks]]
    grace_period = "5s"
    interval = "20s"
    method = "GET"
    timeout = "5s"
    path = "/"

[[vm]]
  memory = '1gb'
  cpu_kind = 'shared'
  cpus = 1

issues:
  # List of regexps of issue texts to exclude, empty list by default.
  # But independently from this option we use default exclude patterns,
  # it can be disabled by `exclude-use-default: false`. To list all
  # excluded by default patterns execute `golangci-lint run --help`

  exclude-rules:
    # Exclude gosimple bool check
    - linters:
        - gosimple
      text: "S(1002|1008|1021)"
    # Exclude failing staticchecks for now
    - linters:
        - staticcheck
      text: "SA(1006|1019|4006|4010|4017|5007|6005|9004):"
    # Exclude lll issues for long lines with go:generate
    - linters:
        - lll
      source: "^//go:generate "

  # Maximum issues count per one linter. Set to 0 to disable. Default is 50.
  max-issues-per-linter: 0

  # Maximum count of issues with the same text. Set to 0 to disable. Default is 3.
  max-same-issues: 0

linters:
  disable-all: true
  enable:
    - gofmt
    - gosimple
    - govet
    - ineffassign
    - staticcheck
    - unconvert
    - unused

# options for analysis running
run:
  go: "1.21"

  # default concurrency is a available CPU number
  concurrency: 4

  # timeout for analysis, e.g. 30s, 5m, default is 1m
  timeout: 10m

  # exit code when at least one issue was found, default is 1
  issues-exit-code: 1

  # include test files or not, default is true
  tests: true

  # list of build tags, all linters use it. Default is empty list.
  #build-tags:
  #  - mytag
  #  - lib/bad.go

  # by default isn't set. If set we pass it to "go list -mod={option}". From "go help modules":
  # If invoked with -mod=readonly, the go command is disallowed from the implicit
  # automatic updating of go.mod described above. Instead, it fails when any changes
  # to go.mod are needed. This setting is most useful to check that go.mod does
  # not need updates, such as in a continuous integration and testing system.
  # If invoked with -mod=vendor, the go command assumes that the vendor
  # directory holds the correct copies of dependencies and ignores
  # the dependency descriptions in go.mod.
  # modules-download-mode: vendor

# output configuration options
output:
  # print lines of code with issue, default is true
  print-issued-lines: true

  # print linter name in the end of issue text, default is true
  print-linter-name: true


  sort-results: true

# all available settings of specific linters
linters-settings:
  errcheck:
    exclude-functions:
      - fmt.*
      - io.Close
  govet:
    settings:
      printf:
        funcs:
          - github.com/superfly/flyctl/terminal.Debugf
          - github.com/superfly/flyctl/terminal.Infof
          - github.com/superfly/flyctl/terminal.Warnf
          - github.com/superfly/flyctl/terminal.Errorf
          - github.com/superfly/flyctl/render.Printf
          - github.com/superfly/flyctl/render.Detailf
          - github.com/superfly/flyctl/render.Donef

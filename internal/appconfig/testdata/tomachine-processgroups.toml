app = "foo"
primary_region = "ord"

[processes]
app = "run-nginx"
vpn = "run-tailscale"
foo = "keep me alive"

[http_service]
  internal_port = 8080
  processes = ["app"]

[[services]]
  internal_port = 9999
  protocol = "udp"
  processes = ["vpn"]

[[services]]
  internal_port = 1111
  protocol = "tcp"
  processes = ["vpn", "app", "foo"]

[checks.listening]
  port = 8080
  type = "tcp"
  processes = ["app", "foo"]

app = "foo"
primary_region = "scl"

[http_service]
  internal_port = 8080
  force_https = true
  auto_start_machines = true
  auto_stop_machines = "stop"

[[services]]
  protocol = "tcp"
  internal_port = 1000
  auto_start_machines = true
  auto_stop_machines = "stop"

[[services]]
  protocol = "tcp"
  internal_port = 1001
  auto_start_machines = false
  auto_stop_machines = "off"

[[services]]
  protocol = "tcp"
  internal_port = 1002
  auto_start_machines = false

[[services]]
  protocol = "tcp"
  internal_port = 1003
  auto_stop_machines = "stop"

[[services]]
  protocol = "tcp"
  internal_port = 1004

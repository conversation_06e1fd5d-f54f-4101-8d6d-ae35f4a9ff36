app = "foo"
kill_signal = "SIGTERM"
kill_timeout = "3s"
swap_size_mb = 512
primary_region = "sea"
console_command = "/bin/bash"
host_dedication_id = "06031957"

[experimental]
  cmd = ["cmd"]
  entrypoint = ["entrypoint"]
  exec = ["exec"]
  auto_rollback = true
  enable_consul = true
  enable_etcd = true

[build]
  builder = "dockerfile"
  image = "foo/fighter"
  builtin = "whatisthis"
  dockerfile = "Dockerfile"
  ignorefile = ".gitignore"
  build-target = "target"
  #docker_build_target = "target"
  buildpacks = ["packme", "well"]

  [build.settings]
    foo = "bar"
    other = 2.0

  [build.args]
    param1 = "value1"
    param2 = "value2"

[deploy]
  release_command = "release command"
  release_command_timeout = "3m"
  release_command_vm.size = "performance-2x"
  release_command_vm.memory = "8g"
  strategy = "rolling-eyes"
  max_unavailable = 0.2

[env]
  FOO = "BAR"


[[restart]]
  policy = "always"
  retries = 3
  processes = ["web"]

[[metrics]]
  port = 9999
  path = "/metrics"

[[metrics]]
  port = 9998
  path = "/metrics"
  processes = ["web"]

[http_service]
  internal_port = 8080
  force_https = true
  auto_start_machines = false
  auto_stop_machines = "off"
  min_machines_running = 0

  [[http_service.checks]]
    interval = "1m21s"
    timeout = "7s"
    grace_period = "2s"
    method = "GET"
    path = "/"
    protocol = "https"
    tls_skip_verify = true
    tls_server_name = "sni2.com"
    [http_service.checks.headers]
      My-Custom-Header = "whatever"

  [[http_service.machine_checks]]
   command = ["curl", "https://fly.io"]
   image = "curlimages/curl"
   entrypoint = ["/bin/sh"]
   kill_timeout = "5s"
   kill_signal = "SIGKILL"

  [http_service.concurrency]
    type = "donuts"
    hard_limit = 10
    soft_limit = 4

  [http_service.tls_options]
    alpn = ["h2", "http/1.1"]
    versions = ["TLSv1.2", "TLSv1.3"]
    default_self_signed = false

  # https://community.fly.io/t/new-feature-basic-http-response-header-modification/3594
  [http_service.http_options]
    compress = true
    idle_timeout = 600

  [http_service.http_options.response.headers]
    fly-request-id = false
    fly-wasnt-here = "yes, it was"
    multi-valued = ["value1", "value2"]

[[statics]]
  guest_path = "/path/to/statics"
  url_prefix = "/static-assets"
  tigris_bucket = "example-bucket"
  index_document = "index.html"

[[files]]
  guest_path = "/path/to/hello.txt"
  raw_value = "aGVsbG8gd29ybGQK"

[[files]]
  guest_path = "/path/to/secret.txt"
  secret_name = "SUPER_SECRET"

[[files]]
  guest_path = "/path/to/config.yaml"
  local_path = "/local/path/config.yaml"
  processes = ["web"]

[[mounts]]
  source = "data"
  initial_size = "30gb"
  destination = "/data"
  snapshot_retention = 17

[[vm]]
  size = "shared-cpu-1x"
  cpu_kind = "performance"
  cpus = 8
  memory = "8gb"
  memory_mb = 8192
  gpus = 2
  gpu_kind = "a100-pcie-40gb"
  kernel_args = ["quiet"]
  host_dedication_id = "isolated-xxx"
  processes = ["app"]

[[vm]]
  # Don't add more fields to this section.
  # It is used to test zero values for `cpus`, `gpus` and others
  # are omitted when serialized back to toml
  memory_mb = 4096

[processes]
  web = "run web"
  task = "task all day"

[checks.status]
  port = 2020
  type = "http"
  interval = "10s"
  timeout = "2s"
  grace_period = "27s"
  method = "GET"
  path = "/status"
  protocol = "https"
  tls_skip_verify = true
  tls_server_name = "sni3.com"
  [checks.status.headers]
    Content-Type = "application/json"
    Authorization = "super-duper-secret"

[[machine_checks]]
   command = ["curl", "https://fly.io"]
   image = "curlimages/curl"
   entrypoint = ["/bin/sh"]
   kill_timeout = "5s"
   kill_signal = "SIGKILL"

[[services]]
  internal_port = 8081
  protocol = "tcp"
  processes = ["app"]
  auto_start_machines = false
  auto_stop_machines = "off"
  min_machines_running = 1

  [services.concurrency]
    type = "requests"
    hard_limit = 22
    soft_limit = 13

  [[services.ports]]
    port = 80
    start_port = 100
    end_port = 200
    handlers = ["https"]
    force_https = true

    [services.ports.http_options]
      idle_timeout = 600

  [[services.tcp_checks]]
    interval = "21s"
    timeout = "4s"
    grace_period = "1s"

  [[services.http_checks]]
    interval = "1m21s"
    timeout = "7s"
    grace_period = "2s"
    method = "GET"
    path = "/"
    protocol = "https"
    tls_skip_verify = true
    tls_server_name = "sni.com"
    [services.http_checks.headers]
      My-Custom-Header = "whatever"

  # Add an extra check to be sure first do not override second or the otherway
  [[services.http_checks]]
    interval = "33s"
    timeout = "10s"
    method = "POST"
    path = "/check2"

  [[services.machine_checks]]
   command = ["curl", "https://fly.io"]
   image = "curlimages/curl"
   entrypoint = ["/bin/sh"]
   kill_timeout = "5s"
   kill_signal = "SIGKILL"

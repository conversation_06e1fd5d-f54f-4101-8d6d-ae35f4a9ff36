app = "foo"

[[services]]
  internal_port = 8080
  protocol = "tcp"

  [[services.ports]]
    port = 80

  [services.ports.tls_options]
  alpn = ["h2", "http/1.1"]
  versions = ["TLSv1.2", "TLSv1.3"]

  # https://community.fly.io/t/new-feature-basic-http-response-header-modification/3594
  [services.ports.http_options]
    compress = true

  [services.ports.http_options.response.headers]
    fly-request-id = false
    fly-wasnt-here = "yes, it was"
    multi-valued = ["value1", "value2"]

  [[services.ports]]
    port = 82
    handlers = ["proxy_proto"]

    [services.ports.proxy_proto_options]
    version = "v2"

package flagnames

const (
	// AccessToken denotes the name of the access token flag.
	AccessToken = "access-token"

	// Verbose denotes the name of the verbose flag.
	Verbose = "verbose"

	// JSONOutput denotes the name of the json output flag.
	JSONOutput = "json"

	// LocalOnly denotes the name of the local-only flag.
	LocalOnly = "local-only"

	// Debug denotes the name of the debug flag.
	Debug = "debug"

	// Org denotes the name of the org flag.
	Org = "org"

	// Region denotes the name of the region flag.
	Region = "region"

	// Yes denotes the name of the yes flag.
	Yes = "yes"

	// App denotes the name of the app flag.
	App = "app"

	// AppConfigFilePath denotes the name of the app config file path flag.
	AppConfigFilePath = "config"

	// Image denotes the name of the image flag.
	Image = "image"

	// Now denotes the name of the now flag.
	Now = "now"

	// NoDeploy denotes the name of the no deploy flag.
	NoDeploy = "no-deploy"

	// GenerateName denotes the name of the generate name flag.
	GenerateNameFlag = "generate-name"

	// DetachName denotes the name of the detach flag.
	Detach = "detach"

	// BindAddr denotes the name of the local bind address flag.
	BindAddr = "bind-addr"

	// ProcessGroup denotes the name of the process group flag.
	ProcessGroup = "process-group"

	// MPGClusterID denotes the name of the MPG cluster ID flag.
	MPGClusterID = "cluster"
)

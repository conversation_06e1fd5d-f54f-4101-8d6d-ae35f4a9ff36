/**
 * Fly.io Machine Logs Function - CORRECT IMPLEMENTATION
 *
 * Based on the actual fly-go GetAppLogs implementation:
 * 
 * URL: GET {baseURL}/api/v1/apps/{appName}/logs?instance={instanceID}&region={region}&next_token={token}
 * 
 * CLI command equivalent: fly logs -a appName -i machineId
 * Example: fly logs -a 9b1deb4d-3b7d-4bad-9bdd-2b0d7b3dcb6d -i e2860112ce1258
 *
 * This replicates the exact GetAppLogs method from fly-go package.
 */

interface LogEntry {
  id: string;
  instance: string;
  level: string;
  message: string;
  region: string;
  timestamp: string;
  meta?: any;
}

interface GetLogsResponse {
  data: Array<{
    attributes: LogEntry;
  }>;
  meta: {
    next_token: string;
  };
}

interface MachineLogsResult {
  logs: LogEntry[];
  nextToken?: string;
  success: boolean;
  error?: string;
}

/**
 * Get logs for a specific machine in an app
 * Add this method to your existing service class (the one with this.flyAxios)
 * 
 * This is the exact implementation based on the fly-go GetAppLogs method
 * 
 * @param appName - The name of the Fly.io app
 * @param machineId - The machine/instance ID 
 * @param region - Region filter (optional)
 * @param nextToken - Pagination token (optional)
 * @returns Promise with logs data
 */
async getMachineLogs(
  appName: string,
  machineId: string,
  region?: string,
  nextToken?: string
): Promise<MachineLogsResult> {
  try {
    console.log(`Getting logs for machine: ${machineId} in app: ${appName}`);

    // Build query parameters exactly like the fly-go implementation
    const params: any = {};
    
    if (nextToken) {
      params.next_token = nextToken;
    }
    
    if (machineId) {
      params.instance = machineId;
    }
    
    if (region) {
      params.region = region;
    }

    // Make the exact same API call as fly-go GetAppLogs
    // URL: GET /api/v1/apps/{appName}/logs?instance={instanceID}&region={region}&next_token={token}
    const response = await this.flyAxios.get(`/api/v1/apps/${appName}/logs`, {
      params,
    });

    if (response.status !== 200) {
      throw new Error(`API returned status ${response.status}: ${response.statusText}`);
    }

    const result: GetLogsResponse = response.data;
    
    // Extract logs from the response structure (same as fly-go)
    const logs: LogEntry[] = [];
    const responseNextToken = result.meta?.next_token;
    
    if (result.data && Array.isArray(result.data)) {
      for (const item of result.data) {
        if (item.attributes) {
          logs.push(item.attributes);
        }
      }
    }

    console.log(`✅ Retrieved ${logs.length} log entries for machine ${machineId}`);
    
    return {
      logs,
      nextToken: responseNextToken,
      success: true,
    };

  } catch (error: any) {
    console.error('❌ Failed to get machine logs:', error);
    
    // Provide helpful error messages
    let errorMessage = error.message || 'Unknown error';
    
    if (error.response?.status === 404) {
      errorMessage = `App '${appName}' or machine '${machineId}' not found`;
    } else if (error.response?.status === 401 || error.response?.status === 403) {
      errorMessage = 'Authentication failed. Check your API token.';
    } else if (error.response?.status) {
      errorMessage = `API error ${error.response.status}: ${error.response.statusText}`;
    }
    
    return {
      logs: [],
      success: false,
      error: errorMessage,
    };
  }
}

/**
 * Helper function to get all logs with pagination
 * This will automatically fetch all pages of logs
 */
async getAllMachineLogs(
  appName: string,
  machineId: string,
  region?: string,
  maxPages: number = 10
): Promise<MachineLogsResult> {
  try {
    let allLogs: LogEntry[] = [];
    let nextToken: string | undefined;
    let pageCount = 0;

    do {
      const result = await this.getMachineLogs(appName, machineId, region, nextToken);
      
      if (!result.success) {
        return result; // Return the error
      }
      
      allLogs.push(...result.logs);
      nextToken = result.nextToken;
      pageCount++;
      
      // Safety check to prevent infinite loops
      if (pageCount >= maxPages) {
        console.warn(`Reached maximum page limit (${maxPages}). There may be more logs available.`);
        break;
      }
      
    } while (nextToken);

    // Sort by timestamp (newest first)
    allLogs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    console.log(`✅ Retrieved ${allLogs.length} total log entries across ${pageCount} pages`);
    
    return {
      logs: allLogs,
      success: true,
    };

  } catch (error) {
    console.error('❌ Failed to get all machine logs:', error);
    return {
      logs: [],
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Usage example:
 * 
 * // Get recent logs for a specific machine
 * const result = await flyService.getMachineLogs(
 *   '9b1deb4d-3b7d-4bad-9bdd-2b0d7b3dcb6d', // appName
 *   'e2860112ce1258' // machineId
 * );
 * 
 * if (result.success) {
 *   result.logs.forEach(log => {
 *     console.log(`[${log.timestamp}] ${log.level}: ${log.message}`);
 *   });
 * }
 * 
 * // Get all logs with pagination
 * const allLogs = await flyService.getAllMachineLogs(
 *   '9b1deb4d-3b7d-4bad-9bdd-2b0d7b3dcb6d',
 *   'e2860112ce1258'
 * );
 */

// Export the functions and types
export { getMachineLogs, getAllMachineLogs };
export type { LogEntry, MachineLogsResult, GetLogsResponse };

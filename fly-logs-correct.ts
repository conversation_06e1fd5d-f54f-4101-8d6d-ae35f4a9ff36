/**
 * Fly.io Machine Logs Function - GraphQL Implementation
 *
 * Based on GraphQL schema discovery showing VM.recentLogs field:
 * - VM.recentLogs(limit: Int = 10, range: Int = 300): [LogEntry!]!
 * - LogEntry { id, instanceId, level, message, region, timestamp }
 *
 * CLI command equivalent: fly logs -a appName -i machineId
 * Example: fly logs -a 9b1deb4d-3b7d-4bad-9bdd-2b0d7b3dcb6d -i e2860112ce1258
 *
 * This uses GraphQL VM.recentLogs instead of the REST API.
 */

interface LogEntry {
  id: string;
  instance: string;
  level: string;
  message: string;
  region: string;
  timestamp: string;
  meta?: any;
}

interface GetLogsResponse {
  data: Array<{
    attributes: LogEntry;
  }>;
  meta: {
    next_token: string;
  };
}

interface MachineLogsResult {
  logs: LogEntry[];
  nextToken?: string;
  success: boolean;
  error?: string;
}

/**
 * Get logs for a specific machine in an app using GraphQL VM.recentLogs
 * Add this method to your existing service class (the one with this.flyAxios)
 *
 * This uses the GraphQL VM.recentLogs field discovered in the schema
 *
 * @param appName - The name of the Fly.io app
 * @param machineId - The machine/instance ID
 * @param limit - Maximum number of log entries to return (default: 100)
 * @param range - Max age of log entries in seconds (default: 3600 = 1 hour)
 * @returns Promise with logs data
 */
async getMachineLogs(
  appName: string,
  machineId: string,
  limit: number = 100,
  range: number = 3600
): Promise<MachineLogsResult> {
  try {
    console.log(`Getting logs for machine: ${machineId} in app: ${appName}`);

    // GraphQL query using VM.recentLogs field
    const query = `
      query GetVMLogs($appName: String!, $limit: Int!, $range: Int!) {
        app(name: $appName) {
          id
          name
          machines(active: true) {
            nodes {
              id
              instanceId
              region
              state
              recentLogs(limit: $limit, range: $range) {
                id
                instanceId
                level
                message
                region
                timestamp
              }
            }
          }
        }
      }
    `;

    const variables = {
      appName,
      limit,
      range,
    };

    // Make the GraphQL request
    const response = await this.flyAxios.graphql.post('', {
      query,
      variables,
    });

    if (response.data.errors?.length) {
      throw new Error(`GraphQL error: ${JSON.stringify(response.data.errors)}`);
    }

    const app = response.data.data?.app;
    if (!app) {
      throw new Error(`App '${appName}' not found`);
    }

    // Find the specific machine/VM
    const targetMachine = app.machines.nodes.find(
      (machine: any) =>
        machine.id === machineId ||
        machine.instanceId === machineId ||
        machine.id.startsWith(machineId) ||
        machine.instanceId.startsWith(machineId)
    );

    if (!targetMachine) {
      const availableMachines = app.machines.nodes.map((m: any) => `${m.id} (${m.instanceId})`).join(', ');
      throw new Error(`Machine '${machineId}' not found in app '${appName}'. Available machines: ${availableMachines}`);
    }

    // Extract logs from the target machine
    const logs: LogEntry[] = targetMachine.recentLogs.map((log: any) => ({
      id: log.id,
      instance: log.instanceId,
      level: log.level,
      message: log.message,
      region: log.region,
      timestamp: log.timestamp,
    }));

    // Sort by timestamp (newest first)
    logs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    console.log(`✅ Retrieved ${logs.length} log entries for machine ${machineId} via GraphQL VM.recentLogs`);

    return {
      logs,
      success: true,
    };

  } catch (error: any) {
    console.error('❌ Failed to get machine logs:', error);

    return {
      logs: [],
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Helper function to get all logs with pagination
 * This will automatically fetch all pages of logs
 */
async getAllMachineLogs(
  appName: string,
  machineId: string,
  region?: string,
  maxPages: number = 10
): Promise<MachineLogsResult> {
  try {
    let allLogs: LogEntry[] = [];
    let nextToken: string | undefined;
    let pageCount = 0;

    do {
      const result = await this.getMachineLogs(appName, machineId, region, nextToken);
      
      if (!result.success) {
        return result; // Return the error
      }
      
      allLogs.push(...result.logs);
      nextToken = result.nextToken;
      pageCount++;
      
      // Safety check to prevent infinite loops
      if (pageCount >= maxPages) {
        console.warn(`Reached maximum page limit (${maxPages}). There may be more logs available.`);
        break;
      }
      
    } while (nextToken);

    // Sort by timestamp (newest first)
    allLogs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    console.log(`✅ Retrieved ${allLogs.length} total log entries across ${pageCount} pages`);
    
    return {
      logs: allLogs,
      success: true,
    };

  } catch (error) {
    console.error('❌ Failed to get all machine logs:', error);
    return {
      logs: [],
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Usage example:
 * 
 * // Get recent logs for a specific machine
 * const result = await flyService.getMachineLogs(
 *   '9b1deb4d-3b7d-4bad-9bdd-2b0d7b3dcb6d', // appName
 *   'e2860112ce1258' // machineId
 * );
 * 
 * if (result.success) {
 *   result.logs.forEach(log => {
 *     console.log(`[${log.timestamp}] ${log.level}: ${log.message}`);
 *   });
 * }
 * 
 * // Get all logs with pagination
 * const allLogs = await flyService.getAllMachineLogs(
 *   '9b1deb4d-3b7d-4bad-9bdd-2b0d7b3dcb6d',
 *   'e2860112ce1258'
 * );
 */

// Export the functions and types
export { getMachineLogs, getAllMachineLogs };
export type { LogEntry, MachineLogsResult, GetLogsResponse };

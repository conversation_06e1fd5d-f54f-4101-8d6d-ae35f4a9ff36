# Default genqlient config; for full documentation see:
# https://github.com/Khan/genqlient/blob/main/docs/genqlient.yaml
schema: gql/schema.graphql
operations:
  - gql/genqclient.graphql
  - agent/**.go
  - internal/appconfig/*.go
  - internal/command/**/*.go
  - internal/command/**/**/*.go
  - internal/build/imgsrc/*.go
  - scripts/clean-up-preflight-apps/*.go
bindings:
  JSON:
    type: interface{}
  BigInt:
    type: int64
  ISO8601DateTime:
    type: time.Time
generated: gql/generated.go

schema {
  query: Queries
  mutation: Mutations
}

# Requires that exactly one field must be supplied and that field must not be `null`.
directive @oneOf on INPUT_OBJECT

type AccessToken implements Node {
  createdAt: ISO8601DateTime!
  id: ID!
  name: String!
  type: AccessTokenType!
}

# The connection type for AccessToken.
type AccessTokenConnection {
  # A list of edges.
  edges: [AccessTokenEdge]

  # A list of nodes.
  nodes: [AccessToken]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

# An edge in a connection.
type AccessTokenEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: AccessToken
}

enum AccessTokenType {
  # token generated for and used by flyctl
  flyctl

  # token generated for our UI frontend
  ui

  # personal access token generated in the UI
  pat

  # token for fly-metrics.net
  grafana

  # used for querying access tokens
  all

  # used for Sentry
  sentry

  # access token
  token
}

# Autogenerated return type of AddCertificate.
type AddCertificatePayload {
  app: App
  certificate: AppCertificate
  check: HostnameCheck
  errors: [String!]
}

type AddOn implements Node {
  # The add-on plan
  addOnPlan: AddOnPlan

  # The display name for an add-on plan
  addOnPlanName: String

  # The add-on provider
  addOnProvider: AddOnProvider

  # An app associated with this add-on
  app: App

  # Apps associated with this add-on
  apps(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
  ): AppConnection
  createdAt: ISO8601DateTime!

  # Environment variables for the add-on
  environment: JSON

  # Optional error message when `status` is `error`
  errorMessage: String

  # DNS hostname for the add-on
  hostname: String
  id: ID!

  # Add-on metadata
  metadata: JSON

  # The service name according to the provider
  name: String

  # Add-on options
  options: JSON

  # Organization that owns this service
  organization: Organization!

  # Password for the add-on
  password: String

  # Region where the primary instance is deployed
  primaryRegion: String

  # Private flycast IP address of the add-on
  privateIp: String

  # Public URL for this service
  publicUrl: String

  # Regions where replica instances are deployed
  readRegions: [String!]

  # Single sign-on link to the add-on dashboard
  ssoLink: String

  # Redis database statistics
  stats: JSON

  # Status of the add-on
  status: String
  updatedAt: ISO8601DateTime!
}

# The connection type for AddOn.
type AddOnConnection {
  # A list of edges.
  edges: [AddOnEdge]

  # A list of nodes.
  nodes: [AddOn]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

# An edge in a connection.
type AddOnEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: AddOn
}

type AddOnPlan implements Node {
  description: String
  displayName: String
  id: ID!
  maxCommandsPerSec: Int
  maxConcurrentConnections: Int
  maxDailyBandwidth: String
  maxDailyCommands: Int
  maxDataSize: String
  maxRequestSize: String
  name: String
  pricePerMonth: Int
}

# The connection type for AddOnPlan.
type AddOnPlanConnection {
  # A list of edges.
  edges: [AddOnPlanEdge]

  # A list of nodes.
  nodes: [AddOnPlan]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

# An edge in a connection.
type AddOnPlanEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: AddOnPlan
}

type AddOnProvider {
  asyncProvisioning: Boolean!
  autoProvision: Boolean!
  beta: Boolean!
  detectPlatform: Boolean!
  displayName: String
  excludedRegions: [Region!]
  id: ID!
  internal: Boolean!
  name: String
  nameSuffix: String
  provisioningInstructions: String
  regions: [Region!]
  resourceName: String!
  selectName: Boolean!
  selectRegion: Boolean!
  selectReplicaRegions: Boolean!
  tosAgreement: String
  tosUrl: String
}

enum AddOnType {
  # An Upstash Redis database
  redis

  # An Upstash Redis database
  upstash_redis

  # An Upstash Kafka cluster
  upstash_kafka

  # An Upstash Vector cluster
  upstash_vector

  # A Sentry project endpoint
  sentry

  # A Kubernetes cluster
  kubernetes

  # A Supabase database
  supabase

  # A Tigris Data bucket
  tigris

  # An Enveloop project
  enveloop

  # A Wafris firewall
  wafris

  # An Arcjet site
  arcjet

  # A MySQL database
  fly_mysql
}

# Autogenerated input type of AddWireGuardPeer
input AddWireGuardPeerInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The node ID of the organization
  organizationId: ID!

  # The region in which to deploy the peer
  region: String

  # The name with which to refer to the peer
  name: String!

  # The 25519 public key for the peer
  pubkey: String!

  # Network ID to attach wireguard peer to
  network: String

  # Add via NATS transaction (deprecated - nats is always used)
  nats: Boolean

  # An ephemeral peer will be destroyed if not validated for too long
  ephemeral: Boolean
}

# Autogenerated return type of AddWireGuardPeer.
type AddWireGuardPeerPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  endpointip: String!
  network: String
  peerip: String!
  pubkey: String!
}

# Autogenerated input type of AllocateEgressIPAddress
input AllocateEgressIPAddressInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The ID of the app
  appId: ID!

  # ID of the machine
  machineId: ID!
}

# Autogenerated return type of AllocateEgressIPAddress.
type AllocateEgressIPAddressPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  v4: String!
  v6: String!
}

# Autogenerated input type of AllocateIPAddress
input AllocateIPAddressInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The application to allocate the ip address for
  appId: ID!

  # The type of IP address to allocate (v4, v6, or private_v6)
  type: IPAddressType!

  # The organization whose network should be used for private IP allocation
  organizationId: ID

  # Desired IP region (defaults to global)
  region: String

  # The target network name in the specified organization
  network: String

  # The name of the associated service
  serviceName: String
}

# Autogenerated return type of AllocateIPAddress.
type AllocateIPAddressPayload {
  app: App!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  ipAddress: IPAddress
}

type Allocation implements Node {
  attachedVolumes(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
  ): VolumeConnection!
  canary: Boolean!
  checks(
    # Filter checks by name
    name: String
  ): [CheckState!]!
  createdAt: ISO8601DateTime!
  criticalCheckCount: Int!

  # Desired status
  desiredStatus: String!
  events: [AllocationEvent!]!
  failed: Boolean!
  healthy: Boolean!

  # Unique ID for this instance
  id: ID!

  # Short unique ID for this instance
  idShort: ID!

  # Indicates if this instance is from the latest job version
  latestVersion: Boolean!
  passingCheckCount: Int!

  # Private IPv6 address for this instance
  privateIP: String
  recentLogs(
    # Max number of entries to return
    limit: Int = 10

    # Max age of log entries in seconds
    range: Int = 300
  ): [LogEntry!]!

  # Region this allocation is running in
  region: String!
  restarts: Int!

  # Current status
  status: String!
  taskName: String!
  totalCheckCount: Int!
  transitioning: Boolean!
  updatedAt: ISO8601DateTime!

  # The configuration version of this instance
  version: Int!
  warningCheckCount: Int!
}

type AllocationEvent {
  message: String!
  timestamp: ISO8601DateTime!
  type: String!
}

type App implements Node {
  addOns(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
    type: AddOnType
  ): AddOnConnection!
  allocation(id: String!): Allocation
  allocations(showCompleted: Boolean): [Allocation!]!
  appUrl: String
  autoscaling: AutoscalingConfig
  backupRegions: [Region!]!

  # [DEPRECATED] Builds of this application
  builds(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
  ): BuildConnection! @deprecated(reason: "Superseded by source_builds")

  # Find a certificate by hostname
  certificate(hostname: String!): AppCertificate

  # Certificates for this app
  certificates(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
    filter: String
    id: String
  ): AppCertificateConnection!

  # Changes to this application
  changes(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
  ): AppChangeConnection!
  config: AppConfig!
  createdAt: ISO8601DateTime!
  currentLock: AppLock
  currentPlacement: [RegionPlacement!]!

  # The latest release of this application
  currentRelease: Release

  # The latest release of this application, without any config processing
  currentReleaseUnprocessed: ReleaseUnprocessed
  deployed: Boolean!

  # Continuous deployment configuration
  deploymentSource: DeploymentSource

  # Find a deployment by id, defaults to latest
  deploymentStatus(id: ID, evaluationId: String): DeploymentStatus

  # Check if this app has a configured deployment source
  hasDeploymentSource: Boolean!
  healthChecks(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int

    # Filter health checks by name
    name: String
  ): CheckStateConnection!

  # Host issues affecting this app
  hostIssues(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
  ): IssueConnection

  # Autogenerated hostname for this application
  hostname: String

  # Unique application ID
  id: ID!

  # Resolve an image from a reference
  image(ref: String!): Image

  # Image details
  imageDetails: ImageVersion
  imageUpgradeAvailable: Boolean
  imageVersionTrackingEnabled: Boolean!

  # Authentication key to use with Instrumentation endpoints
  instrumentsKey: String
  internalId: String!
  internalNumericId: Int!

  # Find an ip address by address string
  ipAddress(address: String!): IPAddress
  ipAddresses(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
  ): IPAddressConnection!

  # This object's unique key
  key: String!

  # Latest image details
  latestImageDetails: ImageVersion
  limitedAccessTokens(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
  ): LimitedAccessTokenConnection!
  machine(id: String!): Machine
  machines(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
    version: Int

    # Return only started/stopped machines (excludes destroyed, etc.)
    active: Boolean
  ): MachineConnection!

  # The unique application name
  name: String!
  network: String
  networkId: Int

  # Organization that owns this app
  organization: Organization!
  parseConfig(definition: JSON!): AppConfig!

  # Fly platform version
  platformVersion: PlatformVersionEnum
  processGroups: [ProcessGroup!]!
  regions: [Region!]!

  # Find a specific release
  release(id: ID, version: Int): Release

  # Individual releases for this application
  releases(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
  ): ReleaseConnection!

  # Individual releases for this application, without any config processing
  releasesUnprocessed(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
    status: String
  ): ReleaseUnprocessedConnection!
  role: AppRole

  # Application runtime
  runtime: RuntimeType!

  # Secrets set on the application
  secrets: [Secret!]!
  services: [Service!]!
  sharedIpAddress: String
  state: AppState!

  # Application status
  status: String!
  taskGroupCounts: [TaskGroupCount!]!
  usage: [AppUsage!]!
  version: Int!
  vmSize: VMSize!
  vms(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
    showCompleted: Boolean
  ): VMConnection!
  volume(internalId: String!): Volume

  # Volumes associated with app
  volumes(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
  ): VolumeConnection!
}

type AppCertificate implements Node {
  acmeAlpnConfigured: Boolean! @deprecated(reason: "use isAcmeAlpnConfigured")
  acmeDnsConfigured: Boolean! @deprecated(reason: "use isAcmeDNSConfigured")
  certificateAuthority: String
  certificateRequestedAt: ISO8601DateTime
  check: Boolean!
  clientStatus: String!
  configured: Boolean! @deprecated(reason: "use isConfigured")
  createdAt: ISO8601DateTime
  dnsProvider: String
  dnsValidationHostname: String!
  dnsValidationInstructions: String!
  dnsValidationTarget: String!
  domain: String
  hostname: String!
  id: ID!
  isAcmeAlpnConfigured: Boolean!
  isAcmeDnsConfigured: Boolean!
  isAcmeHttpConfigured: Boolean!
  isApex: Boolean!
  isConfigured: Boolean!
  isWildcard: Boolean!
  issued(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
    includeExpired: Boolean
  ): CertificateConnection!
  source: String
  validationErrors: [AppCertificateValidationError!]!
}

# The connection type for AppCertificate.
type AppCertificateConnection {
  # A list of edges.
  edges: [AppCertificateEdge]

  # A list of nodes.
  nodes: [AppCertificate]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

# An edge in a connection.
type AppCertificateEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: AppCertificate
}

type AppCertificateValidationError {
  message: String!
  timestamp: ISO8601DateTime!
}

type AppChange implements Node {
  # Object that triggered the change
  actor: AppChangeActor
  actorType: String!
  app: App!
  createdAt: ISO8601DateTime!
  description: String!
  id: ID!
  status: String
  updatedAt: ISO8601DateTime!
  user: User
}

# Objects that change apps
union AppChangeActor = Build | Release | Secret

# The connection type for AppChange.
type AppChangeConnection {
  # A list of edges.
  edges: [AppChangeEdge]

  # A list of nodes.
  nodes: [AppChange]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

# An edge in a connection.
type AppChangeEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: AppChange
}

type AppConfig {
  definition: JSON!
  errors: [String!]!
  services: [Service!]!
  valid: Boolean!
}

# The connection type for App.
type AppConnection {
  # A list of edges.
  edges: [AppEdge]

  # A list of nodes.
  nodes: [App]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

# An edge in a connection.
type AppEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: App
}

# app lock
type AppLock {
  # Time when the lock expires
  expiration: ISO8601DateTime!

  # Lock ID
  lockId: ID!
}

interface AppRole {
  # The name of this role
  name: String!
}

enum AppState {
  # App has not been deployed
  PENDING

  # App has been deployed
  DEPLOYED

  # App has been suspended
  SUSPENDED
}

# Application usage data
type AppUsage {
  # The timespan interval for this usage sample
  interval: String!

  # Total requests for this time period
  requestsCount: Int!

  # Total app execution time (in seconds) for this time period
  totalAppExecS: Int!

  # Total GB transferred out in this time period
  totalDataOutGB: Float!

  # The start of the timespan for this usage sample
  ts: ISO8601DateTime!
}

# Autogenerated input type of AttachPostgresCluster
input AttachPostgresClusterInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The postgres cluster application id
  postgresClusterAppId: ID!

  # The application to attach postgres to
  appId: ID!

  # The database to attach. Defaults to a new database with the same name as the app.
  databaseName: String

  # The database user to create. Defaults to using the database name.
  databaseUser: String

  # The environment variable name to set the connection string to. Defaults to DATABASE_URL
  variableName: String

  # Flag used to indicate that flyctl will exec calls
  manualEntry: Boolean
}

# Autogenerated return type of AttachPostgresCluster.
type AttachPostgresClusterPayload {
  app: App!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  connectionString: String!
  environmentVariableName: String!
  postgresClusterApp: App!
}

type AutoscaleRegionConfig {
  # The region code
  code: String!

  # The minimum number of VMs to run in this region
  minCount: Int

  # The relative weight for this region
  weight: Int
}

# Region autoscaling configuration
input AutoscaleRegionConfigInput {
  # The region code to configure
  code: String!

  # The weight
  weight: Int

  # Minimum number of VMs to run in this region
  minCount: Int

  # Reset the configuration for this region
  reset: Boolean
}

enum AutoscaleStrategy {
  # autoscaling is disabled
  NONE

  # place vms in preferred regions by weight
  PREFERRED_REGIONS

  # place vms in regions near connection sources
  CONNECTION_SOURCES
}

type AutoscalingConfig {
  backupRegions: [String!]!
  balanceRegions: Boolean!
  enabled: Boolean!
  maxCount: Int!
  minCount: Int!
  preferredRegion: String
  regions: [AutoscaleRegionConfig!]!
  strategy: AutoscaleStrategy!
}

# Represents non-fractional signed whole numeric values. Since the value may exceed the size of a 32-bit integer, it's encoded as a string.
scalar BigInt

enum BillingStatus {
  CURRENT
  SOURCE_REQUIRED
  PAST_DUE
  DELINQUENT
  TRIAL_ACTIVE
  TRIAL_ENDED
  SUSPENDED
}

type Build implements Node {
  app: App!
  commitId: String
  commitUrl: String
  createdAt: ISO8601DateTime!

  # The user who initiated the build
  createdBy: User

  # Indicates if this build is complete and failed
  failed: Boolean!
  id: ID!
  image: String

  # Indicates if this build is currently in progress
  inProgress: Boolean!

  # Log output
  logs: String!
  number: Int!

  # Status of the build
  status: String!

  # Indicates if this build is complete and succeeded
  succeeded: Boolean!
  updatedAt: ISO8601DateTime!
}

# The connection type for Build.
type BuildConnection {
  # A list of edges.
  edges: [BuildEdge]

  # A list of nodes.
  nodes: [Build]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

# An edge in a connection.
type BuildEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: Build
}

input BuildFinalImageInput {
  # Sha256 id of docker image
  id: String!

  # Tag used for docker image
  tag: String!

  # Size in bytes of the docker image
  sizeBytes: BigInt!
}

input BuildImageOptsInput {
  # Path to dockerfile, if one exists
  dockerfilePath: String

  # Unused in cli?
  imageRef: String

  # Set of build time variables passed to cli
  buildArgs: JSON

  # Unused in cli?
  extraBuildArgs: JSON

  # Image label to use when tagging and pushing to the fly registry
  imageLabel: String

  # Whether publishing to the registry was requested
  publish: Boolean

  # Docker tag used to publish image to registry
  tag: String

  # Set the target build stage to build if the Dockerfile has more than one stage
  target: String

  # Do not use the build cache when building the image
  noCache: Boolean

  # Builtin builder to use
  builtIn: String

  # Builtin builder settings
  builtInSettings: JSON

  # Fly.toml build.builder setting
  builder: String

  # Fly.toml build.buildpacks setting
  buildPacks: [String!]
}

input BuildStrategyAttemptInput {
  # Build strategy attempted
  strategy: String!

  # Result attempting this strategy
  result: String!

  # Optional error message from strategy
  error: String

  # Optional note about this strategy or its result
  note: String
}

input BuildTimingsInput {
  # Time to build and push the image, measured by flyctl
  buildAndPushMs: BigInt

  # Time to initialize client used to connect to either remote or local builder
  builderInitMs: BigInt

  # Time to build the image including create context, measured by flyctl
  buildMs: BigInt

  # Time to create the build context tar file, measured by flyctl
  contextBuildMs: BigInt

  # Time for builder to build image after receiving context, measured by flyctl
  imageBuildMs: BigInt

  # Time to push completed image to registry, measured by flyctl
  pushMs: BigInt
}

input BuilderMetaInput {
  # Local or remote builder type
  builderType: String!

  # Docker version reported by builder
  dockerVersion: String

  # Whther or not buildkit is enabled on builder
  buildkitEnabled: Boolean

  # Platform reported by the builder
  platform: String

  # Remote builder app used
  remoteAppName: ID

  # Remote builder machine used
  remoteMachineId: ID
}

# Autogenerated return type of CancelBuild.
type CancelBuildPayload {
  build: Build!
}

# A set of base64 messagepack encoded macaroon caveats (See https://github.com/superfly/macaroon)
scalar CaveatSet

type Certificate implements Node {
  expiresAt: ISO8601DateTime!
  hostname: String!
  id: ID!
  type: String!
}

# The connection type for Certificate.
type CertificateConnection {
  # A list of edges.
  edges: [CertificateEdge]

  # A list of nodes.
  nodes: [Certificate]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

# An edge in a connection.
type CertificateEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: Certificate
}

# health check
type Check {
  httpHeaders: [CheckHeader!]
  httpMethod: String
  httpPath: String
  httpProtocol: HTTPProtocol
  httpTlsSkipVerify: Boolean

  # Check interval in milliseconds
  interval: Int!
  name: String
  scriptArgs: [String!]
  scriptCommand: String

  # Check timeout in milliseconds
  timeout: Int!
  type: CheckType!
}

# Autogenerated input type of CheckCertificate
input CheckCertificateInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # Application to ID
  appId: ID!

  # Certificate hostname to check
  hostname: String!
}

# Autogenerated return type of CheckCertificate.
type CheckCertificatePayload {
  app: App
  certificate: AppCertificate
  check: HostnameCheck

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
}

# check job http response
type CheckHTTPResponse implements Node {
  closeTs: String!
  connectedTs: String!
  dnsTs: String!
  firstTs: String!
  flyioDebug: JSON
  headers: JSON!
  id: ID!
  lastTs: String!
  location: CheckLocation!
  rawHeaders: String!
  rawOutput: [String!]!
  resolvedIp: String!
  sentTs: String!
  startTs: String!
  statusCode: Int!
  tlsTs: String
}

# The connection type for CheckHTTPResponse.
type CheckHTTPResponseConnection {
  # A list of edges.
  edges: [CheckHTTPResponseEdge]

  # A list of nodes.
  nodes: [CheckHTTPResponse]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

# An edge in a connection.
type CheckHTTPResponseEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: CheckHTTPResponse
}

# All available http checks verbs
enum CheckHTTPVerb {
  GET
  HEAD
}

# HTTP header for a health check
type CheckHeader {
  name: String!
  value: String!
}

input CheckHeaderInput {
  name: String!
  value: String!
}

input CheckInput {
  type: CheckType!
  name: String

  # Check interval in milliseconds
  interval: Int

  # Check timeout in milliseconds
  timeout: Int
  httpMethod: HTTPMethod
  httpPath: String
  httpProtocol: HTTPProtocol
  httpTlsSkipVerify: Boolean
  httpHeaders: [CheckHeaderInput!]
  scriptCommand: String
  scriptArgs: [String!]
}

# check job
type CheckJob implements Node {
  httpOptions: CheckJobHTTPOptions
  id: ID!
  locations(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
  ): CheckLocationConnection!
  nextRunAt: ISO8601DateTime
  runs(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
  ): CheckJobRunConnection!
  schedule: String
  url: String!
}

# The connection type for CheckJob.
type CheckJobConnection {
  # A list of edges.
  edges: [CheckJobEdge]

  # A list of nodes.
  nodes: [CheckJob]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

# An edge in a connection.
type CheckJobEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: CheckJob
}

# health check state
type CheckJobHTTPOptions {
  headers: [String!]!
  verb: CheckHTTPVerb!
}

# health check state
input CheckJobHTTPOptionsInput {
  verb: CheckHTTPVerb! = GET
  headers: [String!] = []
}

# check job run
type CheckJobRun implements Node {
  completedAt: ISO8601DateTime
  createdAt: ISO8601DateTime!
  httpOptions: CheckJobHTTPOptions!
  httpResponses(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
  ): CheckHTTPResponseConnection!
  id: ID!
  locations(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
  ): CheckLocationConnection!
  state: String!
  tests: [String!]!
  url: String!
}

# The connection type for CheckJobRun.
type CheckJobRunConnection {
  # A list of edges.
  edges: [CheckJobRunEdge]

  # A list of nodes.
  nodes: [CheckJobRun]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

# An edge in a connection.
type CheckJobRunEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: CheckJobRun
}

# check location
type CheckLocation {
  coordinates: [Float!]!
  country: String!
  locality: String!
  name: String!
  state: String
  title: String!
}

# The connection type for CheckLocation.
type CheckLocationConnection {
  # A list of edges.
  edges: [CheckLocationEdge]

  # A list of nodes.
  nodes: [CheckLocation]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

# An edge in a connection.
type CheckLocationEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: CheckLocation
}

# health check state
type CheckState {
  allocation: Allocation!
  allocationId: String!
  name: String!
  output(
    # The number of characters to truncate output to
    limit: Int

    # Remove newlines and trim whitespace
    compact: Boolean
  ): String!
  serviceName: String!
  status: String!
  type: CheckType!
  updatedAt: ISO8601DateTime!
}

# The connection type for CheckState.
type CheckStateConnection {
  # A list of edges.
  edges: [CheckStateEdge]

  # A list of nodes.
  nodes: [CheckState]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

# An edge in a connection.
type CheckStateEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: CheckState
}

enum CheckType {
  # tcp health check
  TCP

  # http health check
  HTTP

  # script health check
  SCRIPT
}

# Autogenerated input type of ConfigureRegions
input ConfigureRegionsInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The ID of the app
  appId: ID!

  # Regions to allow running in
  allowRegions: [String!]

  # Regions to deny running in
  denyRegions: [String!]

  # Fallback regions. Used if preferred regions are having issues
  backupRegions: [String!]

  # Process group to modify
  group: String
}

# Autogenerated return type of ConfigureRegions.
type ConfigureRegionsPayload {
  app: App!
  backupRegions: [Region!]!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  group: String
  regions: [Region!]!
}

# Autogenerated input type of CreateAddOn
input CreateAddOnInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # An optional application ID to attach the add-on to after provisioning
  appId: ID

  # The organization which owns the add-on
  organizationId: ID

  # The add-on type to provision
  type: AddOnType!

  # An optional name for the add-on
  name: String

  # The add-on plan ID
  planId: ID

  # A provider organization plan to set along with provisioning
  organizationPlanId: String

  # Desired primary region for the add-on
  primaryRegion: String

  # Desired regions to place replicas in
  readRegions: [String!]

  # Options specific to the add-on
  options: JSON
}

# Autogenerated return type of CreateAddOn.
type CreateAddOnPayload {
  addOn: AddOn!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
}

# Autogenerated input type of CreateApp
input CreateAppInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The node ID of the organization
  organizationId: ID!

  # The application runtime
  runtime: RuntimeType = FIRECRACKER

  # The name of the new application. Defaults to a random name.
  name: String
  preferredRegion: String
  heroku: Boolean
  network: String
  appRoleId: String
  machines: Boolean = true
  enableSubdomains: Boolean = false
}

# Autogenerated return type of CreateApp.
type CreateAppPayload {
  app: App!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
}

# Autogenerated input type of CreateBuild
input CreateBuildInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The name of the app being built
  appName: ID!

  # The ID of the machine being built (only set for machine builds)
  machineId: ID

  # Options set for building image
  imageOpts: BuildImageOptsInput

  # List of available build strategies that will be attempted
  strategiesAvailable: [String!]!

  # The kind of builder being used
  builderType: String!
}

# Autogenerated return type of CreateBuild.
type CreateBuildPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # build id
  id: ID!

  # stored build status
  status: String!
}

# Autogenerated input type of CreateCheckJob
input CreateCheckJobInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # Organization ID
  organizationId: ID!

  # The URL to check
  url: String!

  # http checks locations
  locations: [String!]!

  # http check options
  httpOptions: CheckJobHTTPOptionsInput!
}

# Autogenerated return type of CreateCheckJob.
type CreateCheckJobPayload {
  checkJob: CheckJob!
  checkJobRun: CheckJobRun

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
}

# Autogenerated input type of CreateCheckJobRun
input CreateCheckJobRunInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # Check Job ID
  checkJobId: ID!
}

# Autogenerated return type of CreateCheckJobRun.
type CreateCheckJobRunPayload {
  checkJob: CheckJob!
  checkJobRun: CheckJobRun

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
}

# Autogenerated input type of CreateDNSPortal
input CreateDNSPortalInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The node ID of the organization
  organizationId: ID!

  # The unique name of this portal. A random name will be generated if omitted.
  name: String

  # The title of this portal
  title: String

  # The return url for this portal
  returnUrl: String

  # The text to display for the return url link
  returnUrlText: String

  # The support url for this portal
  supportUrl: String

  # The text to display for the support url link
  supportUrlText: String

  # The primary branding color
  primaryColor: String

  # The secondary branding color
  accentColor: String
}

# Autogenerated return type of CreateDNSPortal.
type CreateDNSPortalPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  dnsPortal: DNSPortal!
}

# Autogenerated input type of CreateDNSPortalSession
input CreateDNSPortalSessionInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The node ID of the dns portal
  dnsPortalId: ID!

  # The node ID of the domain to edit
  domainId: ID!

  # Optionally override the portal's default title for this session
  title: String

  # Optionally override the portal's default return url for this session
  returnUrl: String

  # Optionally override the portal's default return url text for this session
  returnUrlText: String
}

# Autogenerated return type of CreateDNSPortalSession.
type CreateDNSPortalSessionPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  dnsPortalSession: DNSPortalSession!
}

# Autogenerated input type of CreateDNSRecord
input CreateDNSRecordInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The node ID of the domain
  domainId: ID!

  # The type of the record
  type: DNSRecordType!

  # The dns record name
  name: String!

  # The TTL in seconds
  ttl: Int!

  # The content of the record
  rdata: String!
}

# Autogenerated return type of CreateDNSRecord.
type CreateDNSRecordPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  record: DNSRecord!
}

# Autogenerated input type of CreateDelegatedWireGuardToken
input CreateDelegatedWireGuardTokenInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The node ID of the organization
  organizationId: ID!

  # The name with which to refer to the peer
  name: String
}

# Autogenerated return type of CreateDelegatedWireGuardToken.
type CreateDelegatedWireGuardTokenPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  token: String!
}

# Autogenerated input type of CreateDoctorReport
input CreateDoctorReportInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The report data
  data: JSON!
}

# Autogenerated return type of CreateDoctorReport.
type CreateDoctorReportPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  reportId: ID!
}

# Autogenerated return type of CreateDoctorUrl.
type CreateDoctorUrlPayload {
  putUrl: String!
}

# Autogenerated input type of CreateExtensionTosAgreement
input CreateExtensionTosAgreementInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The add-on provider name
  addOnProviderName: String!

  # The organization that agrees to the ToS
  organizationId: ID
}

# Autogenerated return type of CreateExtensionTosAgreement.
type CreateExtensionTosAgreementPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
}

# Autogenerated input type of CreateLimitedAccessToken
input CreateLimitedAccessTokenInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  name: String!

  # The node ID of the organization
  organizationId: ID!
  profile: String!
  profileParams: JSON
  expiry: String

  # Names of third-party configurations to opt into
  optInThirdParties: [String!]

  # Names of third-party configurations to opt out of
  optOutThirdParties: [String!]
}

# Autogenerated return type of CreateLimitedAccessToken.
type CreateLimitedAccessTokenPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  limitedAccessToken: LimitedAccessToken!
}

# Autogenerated input type of CreateOrganization
input CreateOrganizationInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The name of the organization
  name: String!

  # Whether or not new apps in this org use Apps V2 by default
  appsV2DefaultOn: Boolean
}

# Autogenerated input type of CreateOrganizationInvitation
input CreateOrganizationInvitationInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The node ID of the organization
  organizationId: ID!

  # The email to invite
  email: String!
}

# Autogenerated return type of CreateOrganizationInvitation.
type CreateOrganizationInvitationPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  invitation: OrganizationInvitation!
}

# Autogenerated return type of CreateOrganization.
type CreateOrganizationPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  organization: Organization!
  token: String!
}

# Autogenerated input type of CreatePostgresClusterDatabase
input CreatePostgresClusterDatabaseInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The name of the postgres cluster app
  appName: String!

  # The name of the database
  databaseName: String!
}

# Autogenerated return type of CreatePostgresClusterDatabase.
type CreatePostgresClusterDatabasePayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  database: PostgresClusterDatabase!
  postgresClusterRole: PostgresClusterAppRole!
}

# Autogenerated input type of CreatePostgresClusterUser
input CreatePostgresClusterUserInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The name of the postgres cluster app
  appName: String!

  # The name of the database
  username: String!

  # The password of the user
  password: String!

  # Should this user be a superuser
  superuser: Boolean
}

# Autogenerated return type of CreatePostgresClusterUser.
type CreatePostgresClusterUserPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  postgresClusterRole: PostgresClusterAppRole!
  user: PostgresClusterUser!
}

# Autogenerated input type of CreateRelease
input CreateReleaseInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The ID of the app
  appId: ID!

  # The image to deploy
  image: String!

  # nomad or machines
  platformVersion: String!

  # app definition
  definition: JSON!

  # The strategy for replacing existing instances. Defaults to canary.
  strategy: DeploymentStrategy!

  # The build ID linked to the release
  buildId: ID
}

# Autogenerated return type of CreateRelease.
type CreateReleasePayload {
  app: App!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  release: Release
}

# Autogenerated input type of CreateTemplateDeployment
input CreateTemplateDeploymentInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The node ID of the organization to move the app to
  organizationId: ID!
  template: JSON!
  variables: [PropertyInput!]
}

# Autogenerated return type of CreateTemplateDeployment.
type CreateTemplateDeploymentPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  templateDeployment: TemplateDeployment!
}

# Autogenerated input type of CreateThirdPartyConfiguration
input CreateThirdPartyConfigurationInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The node ID of the organization
  organizationId: ID!

  # Friendly name for this configuration
  name: String!

  # Location URL of the third-party service capable of discharging
  location: String!

  # Restrictions to be placed on third-party caveats
  caveats: CaveatSet

  # Whether to add this third-party caveat on session tokens issued to flyctl
  flyctlLevel: ThirdPartyConfigurationLevel!

  # Whether to add this third-party caveat on Fly.io session tokens
  uiexLevel: ThirdPartyConfigurationLevel!

  # Whether to add this third-party caveat on tokens issued via `flyctl tokens create`
  customLevel: ThirdPartyConfigurationLevel!
}

# Autogenerated return type of CreateThirdPartyConfiguration.
type CreateThirdPartyConfigurationPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  thirdPartyConfiguration: ThirdPartyConfiguration!
}

# Autogenerated input type of CreateVolume
input CreateVolumeInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The application to attach the new volume to
  appId: ID!

  # Volume name
  name: String!

  # Desired region for volume
  region: String!

  # Desired volume size, in GB
  sizeGb: Int!

  # Volume should be encrypted at rest
  encrypted: Boolean = true

  # Provision volume in a redundancy zone not already in use by this app
  requireUniqueZone: Boolean
  snapshotId: ID
  fsType: FsTypeType = ext4
}

# Autogenerated return type of CreateVolume.
type CreateVolumePayload {
  app: App!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  volume: Volume!
}

# Autogenerated input type of CreateVolumeSnapshot
input CreateVolumeSnapshotInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  volumeId: ID!
}

# Autogenerated return type of CreateVolumeSnapshot.
type CreateVolumeSnapshotPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  volume: Volume!
}

type DNSPortal implements Node {
  accentColor: String!
  createdAt: ISO8601DateTime!
  id: ID!
  name: String!
  organization: Organization!
  primaryColor: String!
  returnUrl: String
  returnUrlText: String
  supportUrl: String
  supportUrlText: String
  title: String!
  updatedAt: ISO8601DateTime!
}

# The connection type for DNSPortal.
type DNSPortalConnection {
  # A list of edges.
  edges: [DNSPortalEdge]

  # A list of nodes.
  nodes: [DNSPortal]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

# An edge in a connection.
type DNSPortalEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: DNSPortal
}

type DNSPortalSession implements Node {
  createdAt: ISO8601DateTime!

  # The dns portal this session
  dnsPortal: DNSPortal!
  expiresAt: ISO8601DateTime!
  id: ID!

  # Is this session expired?
  isExpired: Boolean!

  # The overridden return url for this session
  returnUrl: String

  # The overridden return url text for this session
  returnUrlText: String

  # The overridden title for this session
  title: String

  # The url to access this session's dns portal
  url: String!
}

type DNSRecord implements Node {
  createdAt: ISO8601DateTime!

  # The domain this record belongs to
  domain: Domain!

  # Fully qualified domain name for this record
  fqdn: String!
  id: ID!

  # Is this record at the zone apex?
  isApex: Boolean!

  # Is this a system record? System records are managed by fly and not editable.
  isSystem: Boolean!

  # Is this record a wildcard?
  isWildcard: Boolean!

  # The name of this record. @ indicates the record is at the zone apex.
  name: String!

  # The record data
  rdata: String!

  # The number of seconds this record can be cached for
  ttl: Int!

  # The type of record
  type: DNSRecordType!
  updatedAt: ISO8601DateTime!
}

type DNSRecordAttributes {
  # The name of the record.
  name: String!

  # The record data.
  rdata: String!

  # The number of seconds this record can be cached for.
  ttl: Int!

  # The type of record.
  type: DNSRecordType!
}

enum DNSRecordChangeAction {
  # A record should be created with the provided attributes
  CREATE

  # A record with the provided ID should be updated
  UPDATE

  # A record with the provided ID should be deleted
  DELETE
}

input DNSRecordChangeInput {
  # The action to perform on this record.
  action: DNSRecordChangeAction!

  # The id of the record this action will apply to. This is required if the action is UPDATE or DELETE.
  recordId: ID

  # The record type. This is required if action is CREATE.
  type: DNSRecordType

  # The name of the record. If omitted it will default to @ - the zone apex.
  name: String

  # The number of seconds this record can be cached for. Defaults to 1 hour.
  ttl: Int

  # The record data. Required if action is CREATE
  rdata: String
}

# The connection type for DNSRecord.
type DNSRecordConnection {
  # A list of edges.
  edges: [DNSRecordEdge]

  # A list of nodes.
  nodes: [DNSRecord]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

type DNSRecordDiff {
  # The action that was performed.
  action: DNSRecordChangeAction!

  # The attributes for this record after the action was performed.
  newAttributes: DNSRecordAttributes

  # The text representation of this record after the action was performed.
  newText: String

  # The attributes for this record before the action was performed.
  oldAttributes: DNSRecordAttributes

  # The text representation of this record before the action was performed.
  oldText: String
}

# An edge in a connection.
type DNSRecordEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: DNSRecord
}

enum DNSRecordType {
  A
  AAAA
  ALIAS
  CNAME
  MX
  NS
  SOA
  TXT
  SRV
}

type DNSRecordWarning {
  # The action to perform.
  action: DNSRecordChangeAction!

  # The desired attributes for this record.
  attributes: DNSRecordAttributes!

  # The warning message.
  message: String!

  # The record this warning applies to.
  record: DNSRecord
}

type DelegatedWireGuardToken implements Node {
  id: ID!
  name: String!
}

# The connection type for DelegatedWireGuardToken.
type DelegatedWireGuardTokenConnection {
  # A list of edges.
  edges: [DelegatedWireGuardTokenEdge]

  # A list of nodes.
  nodes: [DelegatedWireGuardToken]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

# An edge in a connection.
type DelegatedWireGuardTokenEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: DelegatedWireGuardToken
}

# Autogenerated input type of DeleteAddOn
input DeleteAddOnInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The ID of the add-on to delete
  addOnId: ID

  # The name of the add-on to delete
  name: String

  # The add-on service provider type
  provider: String
}

# Autogenerated return type of DeleteAddOn.
type DeleteAddOnPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  deletedAddOnName: String
}

# Autogenerated return type of DeleteApp.
type DeleteAppPayload {
  # The organization that owned the deleted app
  organization: Organization!
}

# Autogenerated return type of DeleteCertificate.
type DeleteCertificatePayload {
  app: App
  certificate: AppCertificate
  errors: [String!]
}

# Autogenerated input type of DeleteDNSPortal
input DeleteDNSPortalInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The node ID of the dns portal
  dnsPortalId: ID!
}

# Autogenerated return type of DeleteDNSPortal.
type DeleteDNSPortalPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The organization that owned the dns portal
  organization: Organization!
}

# Autogenerated input type of DeleteDNSPortalSession
input DeleteDNSPortalSessionInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The node ID of the dns portal session
  dnsPortalSessionId: ID!
}

# Autogenerated return type of DeleteDNSPortalSession.
type DeleteDNSPortalSessionPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The dns portal that owned the session
  dnsPortal: DNSPortal!
}

# Autogenerated input type of DeleteDNSRecord
input DeleteDNSRecordInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The node ID of the DNS record
  recordId: ID!
}

# Autogenerated return type of DeleteDNSRecord.
type DeleteDNSRecordPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  domain: Domain!
}

# Autogenerated input type of DeleteDelegatedWireGuardToken
input DeleteDelegatedWireGuardTokenInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The node ID of the organization
  organizationId: ID!

  # The raw WireGuard token
  token: String

  # The name with which to refer to the token
  name: String
}

# Autogenerated return type of DeleteDelegatedWireGuardToken.
type DeleteDelegatedWireGuardTokenPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  token: String!
}

# Autogenerated input type of DeleteDeploymentSource
input DeleteDeploymentSourceInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The application to update
  appId: String!
}

# Autogenerated return type of DeleteDeploymentSource.
type DeleteDeploymentSourcePayload {
  app: App

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
}

# Autogenerated input type of DeleteHealthCheckHandler
input DeleteHealthCheckHandlerInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The node ID of the organization
  organizationId: ID!

  # Handler name
  name: String!
}

# Autogenerated return type of DeleteHealthCheckHandler.
type DeleteHealthCheckHandlerPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
}

# Autogenerated input type of DeleteLimitedAccessToken
input DeleteLimitedAccessTokenInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The root of the macaroon
  token: String

  # The node ID for real
  id: ID

  # The email of the user who revoked the token
  revokedBy: String
}

# Autogenerated return type of DeleteLimitedAccessToken.
type DeleteLimitedAccessTokenPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  revokedBy: String
  token: String
}

# Autogenerated input type of DeleteOrganization
input DeleteOrganizationInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The ID of the organization to delete
  organizationId: ID!
}

# Autogenerated input type of DeleteOrganizationInvitation
input DeleteOrganizationInvitationInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The node ID of the invitation
  invitationId: ID!
}

# Autogenerated return type of DeleteOrganizationInvitation.
type DeleteOrganizationInvitationPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  organization: Organization!
}

# Autogenerated input type of DeleteOrganizationMembership
input DeleteOrganizationMembershipInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The node ID of the organization
  organizationId: ID!

  # The node ID of the user
  userId: ID!
}

# Autogenerated return type of DeleteOrganizationMembership.
type DeleteOrganizationMembershipPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  organization: Organization!
  user: User!
}

# Autogenerated return type of DeleteOrganization.
type DeleteOrganizationPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  deletedOrganizationId: ID!
}

# Autogenerated input type of DeleteRemoteBuilder
input DeleteRemoteBuilderInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The node ID of the organization
  organizationId: ID!
}

# Autogenerated return type of DeleteRemoteBuilder.
type DeleteRemoteBuilderPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  organization: Organization!
}

# Autogenerated input type of DeleteThirdPartyConfiguration
input DeleteThirdPartyConfigurationInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The node ID of the configuration
  thirdPartyConfigurationId: ID!
}

# Autogenerated return type of DeleteThirdPartyConfiguration.
type DeleteThirdPartyConfigurationPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  ok: Boolean!
}

# Autogenerated input type of DeleteVolume
input DeleteVolumeInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The node ID of the volume
  volumeId: ID!

  # Unique lock ID
  lockId: ID
}

# Autogenerated return type of DeleteVolume.
type DeleteVolumePayload {
  app: App!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
}

# Autogenerated input type of DeployImage
input DeployImageInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The ID of the app
  appId: ID!

  # The image to deploy
  image: String!

  # Network services to expose
  services: [ServiceInput!]

  # app definition
  definition: JSON

  # The strategy for replacing existing instances. Defaults to canary.
  strategy: DeploymentStrategy
}

# Autogenerated return type of DeployImage.
type DeployImagePayload {
  app: App!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  release: Release
  releaseCommand: ReleaseCommand
}

# Continuous deployment configuration
type DeploymentSource {
  backend: JSON!
  baseDir: String!
  connected: Boolean!
  id: ID!
  provider: String!

  # The ref to build from
  ref: String!
  repositoryId: String!

  # The repository to fetch source code from
  repositoryUrl: String!
}

type DeploymentStatus {
  allocations: [Allocation!]!
  description: String!
  desiredCount: Int!
  healthyCount: Int!

  # Unique ID for this deployment
  id: ID!
  inProgress: Boolean!
  placedCount: Int!
  promoted: Boolean!
  status: String!
  successful: Boolean!
  unhealthyCount: Int!
  version: Int!
}

enum DeploymentStrategy {
  # Deploy new instances all at once
  IMMEDIATE

  # Deploy new instances all at once
  SIMPLE

  # Incrementally replace old instances with new ones
  ROLLING

  # Incrementally replace old instances with new ones, 1 by 1
  ROLLING_ONE

  # Ensure new instances are healthy before continuing with a rolling deployment
  CANARY

  # Launch all new instances before shutting down previous instances
  BLUEGREEN
}

# Autogenerated input type of DetachPostgresCluster
input DetachPostgresClusterInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The postgres cluster application id
  postgresClusterAppId: ID!

  # The application to detach postgres from
  appId: ID!

  # The postgres attachment id
  postgresClusterAttachmentId: ID
}

# Autogenerated return type of DetachPostgresCluster.
type DetachPostgresClusterPayload {
  app: App!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  postgresClusterApp: App!
}

type Domain implements Node {
  autoRenew: Boolean
  createdAt: ISO8601DateTime!

  # The delegated nameservers for the registration
  delegatedNameservers: [String!]

  # The dns records for this domain
  dnsRecords(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
  ): DNSRecordConnection!
  dnsStatus: DomainDNSStatus!
  expiresAt: ISO8601DateTime
  id: ID!

  # The name for this domain
  name: String!

  # The organization that owns this domain
  organization: Organization!
  registrationStatus: DomainRegistrationStatus!
  updatedAt: ISO8601DateTime!

  # The nameservers for the hosted zone
  zoneNameservers: [String!]!
}

# The connection type for Domain.
type DomainConnection {
  # A list of edges.
  edges: [DomainEdge]

  # A list of nodes.
  nodes: [Domain]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

enum DomainDNSStatus {
  # The DNS zone has not been created yet
  PENDING

  # The DNS zone is being updated
  UPDATING

  # The DNS zone is ready
  READY
}

# An edge in a connection.
type DomainEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: Domain
}

enum DomainRegistrationStatus {
  # The domain is not registered on fly
  UNMANAGED

  # The domain is being registered
  REGISTERING

  # The domain is registered
  REGISTERED

  # The domain is being transferred
  TRANSFERRING

  # The domain registration has expired
  EXPIRED
}

# Autogenerated input type of DummyWireGuardPeer
input DummyWireGuardPeerInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The node ID of the organization
  organizationId: ID!

  # The region in which to deploy the peer
  region: String
}

# Autogenerated return type of DummyWireGuardPeer.
type DummyWireGuardPeerPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  endpointip: String!
  localpub: String!
  peerip: String!
  privkey: String!
  pubkey: String!
}

type EgressIPAddress implements Node {
  # ID of the object.
  id: ID!
  ip: String!
  region: String!
  version: Int!
}

# The connection type for EgressIPAddress.
type EgressIPAddressConnection {
  # A list of edges.
  edges: [EgressIPAddressEdge]

  # A list of nodes.
  nodes: [EgressIPAddress]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

# An edge in a connection.
type EgressIPAddressEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: EgressIPAddress
}

type EmptyAppRole implements AppRole {
  # The name of this role
  name: String!
}

# Autogenerated input type of EnablePostgresConsul
input EnablePostgresConsulInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The ID of the app
  appId: ID
  region: String
}

# Autogenerated return type of EnablePostgresConsul.
type EnablePostgresConsulPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  consulUrl: String!
}

# Autogenerated input type of EnsureDepotRemoteBuilder
input EnsureDepotRemoteBuilderInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The unique application name
  appName: String

  # The node ID of the organization
  organizationId: ID

  # Desired region for the remote builder
  region: String

  # The scope of the builder; either "app" or "organization"
  builderScope: String
}

# Autogenerated return type of EnsureDepotRemoteBuilder.
type EnsureDepotRemoteBuilderPayload {
  buildId: String!
  buildToken: String!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
}

# Autogenerated input type of EnsureMachineRemoteBuilder
input EnsureMachineRemoteBuilderInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The unique application name
  appName: String

  # The node ID of the organization
  organizationId: ID

  # Desired region for the remote builder
  region: String

  # Use v2 machines
  v2: Boolean
}

# Autogenerated return type of EnsureMachineRemoteBuilder.
type EnsureMachineRemoteBuilderPayload {
  app: App!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  machine: Machine!
}

# Autogenerated input type of EstablishSSHKey
input EstablishSSHKeyInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The node ID of the organization
  organizationId: ID!

  # Establish a key even if one is already set
  override: Boolean
}

# Autogenerated return type of EstablishSSHKey.
type EstablishSSHKeyPayload {
  certificate: String!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
}

# Autogenerated input type of ExportDNSZone
input ExportDNSZoneInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # ID of the domain to export
  domainId: ID!
}

# Autogenerated return type of ExportDNSZone.
type ExportDNSZonePayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  contents: String!
  domain: Domain!
}

# Autogenerated input type of ExtendVolume
input ExtendVolumeInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The node ID of the volume
  volumeId: ID!

  # The target volume size
  sizeGb: Int!
}

# Autogenerated return type of ExtendVolume.
type ExtendVolumePayload {
  app: App!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  needsRestart: Boolean!
  volume: Volume!
}

# Autogenerated input type of FinishBuild
input FinishBuildInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # Build id returned by createBuild() mutation
  buildId: ID!

  # The name of the app being built
  appName: ID!

  # The ID of the machine being built (only set for machine builds)
  machineId: ID

  # Indicate whether build completed or failed
  status: String!

  # Build strategies attempted and their result, should be in order of attempt
  strategiesAttempted: [BuildStrategyAttemptInput!]

  # Metadata about the builder
  builderMeta: BuilderMetaInput

  # Information about the docker image that was built
  finalImage: BuildFinalImageInput

  # Timings for different phases of the build
  timings: BuildTimingsInput

  # Log or error output
  logs: String
}

# Autogenerated return type of FinishBuild.
type FinishBuildPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # build id
  id: ID!

  # stored build status
  status: String!

  # wall clock time for this build
  wallclockTimeMs: Int!
}

type FlyPlatform {
  # Latest flyctl release details
  flyctl: FlyctlRelease!

  # Fly global regions
  regions: [Region!]!

  # Region current request from
  requestRegion: String

  # Available VM sizes
  vmSizes: [VMSize!]!
}

type FlyctlMachineHostAppRole implements AppRole {
  # The name of this role
  name: String!
}

type FlyctlRelease {
  timestamp: ISO8601DateTime!
  version: String!
}

# Autogenerated input type of ForkVolume
input ForkVolumeInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The application to attach the new volume to
  appId: ID!

  # The volume to fork
  sourceVolId: ID!

  # Volume name
  name: String

  # Lock the new volume to only usable on machines
  machinesOnly: Boolean

  # Unique lock ID
  lockId: ID

  # Enables experimental cross-host volume forking
  remote: Boolean
}

# Autogenerated return type of ForkVolume.
type ForkVolumePayload {
  app: App!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  volume: Volume!
}

enum FsTypeType {
  # default ext4 filesystem
  ext4

  # raw block device, no filesystem
  raw
}

type GithubAppInstallation {
  editUrl: String!
  id: ID!
  owner: String!
  repositories: [GithubRepository!]!
}

type GithubIntegration {
  installationUrl: String!
  installations: [GithubAppInstallation!]!
  viewerAuthenticated: Boolean!
}

type GithubRepository {
  fork: Boolean!
  fullName: String!
  id: String!
  name: String!
  private: Boolean!
}

# Autogenerated input type of GrantPostgresClusterUserAccess
input GrantPostgresClusterUserAccessInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The name of the postgres cluster app
  appName: String!

  # The name of the database
  username: String!

  # The database to grant access to
  databaseName: String!
}

# Autogenerated return type of GrantPostgresClusterUserAccess.
type GrantPostgresClusterUserAccessPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  database: PostgresClusterDatabase!
  postgresClusterRole: PostgresClusterAppRole!
  user: PostgresClusterUser!
}

enum HTTPMethod {
  GET
  POST
  PUT
  PATCH
  HEAD
  DELETE
}

enum HTTPProtocol {
  # HTTP protocol
  HTTP

  # HTTPS protocol
  HTTPS
}

type HealthCheck {
  # Raw name of entity
  entity: String!

  # Time check last passed
  lastPassing: ISO8601DateTime

  # Check name
  name: String!

  # Latest check output
  output: String

  # Current check state
  state: String!
}

# The connection type for HealthCheck.
type HealthCheckConnection {
  # A list of edges.
  edges: [HealthCheckEdge]

  # A list of nodes.
  nodes: [HealthCheck]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

# An edge in a connection.
type HealthCheckEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: HealthCheck
}

type HealthCheckHandler {
  # Handler name
  name: String!

  # Handler type (Slack or Pagerduty)
  type: String!
}

# The connection type for HealthCheckHandler.
type HealthCheckHandlerConnection {
  # A list of edges.
  edges: [HealthCheckHandlerEdge]

  # A list of nodes.
  nodes: [HealthCheckHandler]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

# An edge in a connection.
type HealthCheckHandlerEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: HealthCheckHandler
}

type HerokuApp {
  id: String!
  name: String!
  region: String
  releasedAt: ISO8601DateTime!
  stack: String
  teamName: String
}

type HerokuIntegration {
  herokuApps: [HerokuApp!]!
  viewerAuthenticated: Boolean!
}

type Host implements Node {
  id: ID!
}

type HostnameCheck {
  aRecords: [String!]!
  aaaaRecords: [String!]!
  acmeDnsConfigured: Boolean!
  caaRecords: [String!]!
  cnameRecords: [String!]!
  dnsConfigured: Boolean!
  dnsProvider: String
  dnsVerificationRecord: String
  errors: [String!]
  id: ID!
  isProxied: Boolean!
  resolvedAddresses: [String!]!
  soa: String
}

type IPAddress implements Node {
  address: String!
  createdAt: ISO8601DateTime!
  id: ID!
  network: Network
  region: String
  serviceName: String
  type: IPAddressType!
}

# The connection type for IPAddress.
type IPAddressConnection {
  # A list of edges.
  edges: [IPAddressEdge]

  # A list of nodes.
  nodes: [IPAddress]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

# An edge in a connection.
type IPAddressEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: IPAddress
}

enum IPAddressType {
  v4
  v6
  private_v6
  shared_v4
}

# An ISO 8601-encoded datetime
scalar ISO8601DateTime

type Image {
  absoluteRef: String!
  compressedSize: Int!
    @deprecated(
      reason: "Int cannot handle sizes over 2GB. Use compressed_size_full instead"
    )
  compressedSizeFull: BigInt!
  config: JSON!
  configDigest: JSON!
  createdAt: ISO8601DateTime!
  digest: String!
  id: ID!
  label: String!
  manifest: JSON!
  ref: String!
  registry: String!
  repository: String!
  tag: String
}

type ImageVersion {
  digest: String!
  registry: String!
  repository: String!
  tag: String!
  version: String
}

# Autogenerated return type of ImportCertificate.
type ImportCertificatePayload {
  app: App
  appCertificate: AppCertificate
  certificate: Certificate
  errors: [String!]
}

# Autogenerated input type of ImportDNSZone
input ImportDNSZoneInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # ID of the domain to export
  domainId: ID!
  zonefile: String!
}

# Autogenerated return type of ImportDNSZone.
type ImportDNSZonePayload {
  changes: [DNSRecordDiff!]!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  domain: Domain!
  warnings: [DNSRecordWarning!]!
}

type Issue implements Node {
  createdAt: ISO8601DateTime!
  id: ID!
  internalId: String!

  # Issue message
  message: String
  resolvedAt: ISO8601DateTime
  updatedAt: ISO8601DateTime!
}

# Autogenerated input type of IssueCertificate
input IssueCertificateInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The node ID of the organization
  organizationId: ID!

  # The names of the apps this certificate will be limited to accessing
  appNames: [String!]

  # Hours for which certificate will be valid
  validHours: Int

  # SSH principals for certificate (e.g. ["fly", "root"])
  principals: [String!]

  # The openssh-formatted ED25519 public key to issue the certificate for
  publicKey: String
}

# Autogenerated return type of IssueCertificate.
type IssueCertificatePayload {
  certificate: String!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The private key, if a public_key wasn't specified
  key: String @deprecated(reason: "Specify your own public key")
}

# The connection type for Issue.
type IssueConnection {
  # A list of edges.
  edges: [IssueEdge]

  # A list of nodes.
  nodes: [Issue]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

# An edge in a connection.
type IssueEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: Issue
}

# Untyped JSON data
scalar JSON

# Autogenerated input type of KillMachine
input KillMachineInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The ID of the app
  appId: ID

  # machine id
  id: String!
}

# Autogenerated return type of KillMachine.
type KillMachinePayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  machine: Machine!
}

# Autogenerated input type of LaunchMachine
input LaunchMachineInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The ID of the app
  appId: ID

  # The node ID of the organization
  organizationId: ID

  # The ID of the machine
  id: String

  # The name of the machine
  name: String

  # Region for the machine
  region: String

  # Configuration
  config: JSON!
}

# Autogenerated return type of LaunchMachine.
type LaunchMachinePayload {
  app: App!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  machine: Machine!
}

type LimitedAccessToken implements Node {
  createdAt: ISO8601DateTime!
  expiresAt: ISO8601DateTime!
  id: ID!
  name: String!
  profile: String!
  revokedAt: ISO8601DateTime
  revokedBy: String
  token: String!
  tokenHeader: String
  user: User!
}

# The connection type for LimitedAccessToken.
type LimitedAccessTokenConnection {
  # A list of edges.
  edges: [LimitedAccessTokenEdge]

  # A list of nodes.
  nodes: [LimitedAccessToken]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

# An edge in a connection.
type LimitedAccessTokenEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: LimitedAccessToken
}

# Autogenerated input type of LockApp
input LockAppInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The ID of the app
  appId: ID!
}

# Autogenerated return type of LockApp.
type LockAppPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # When this lock automatically expires
  expiration: ISO8601DateTime

  # Unique lock ID
  lockId: ID
}

type LogEntry {
  id: String!
  instanceId: String!
  level: String!
  message: String!
  region: String!
  timestamp: ISO8601DateTime!
}

# Autogenerated input type of LogOut
input LogOutInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
}

# Autogenerated return type of LogOut.
type LogOutPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  ok: Boolean!
}

type LoggedCertificate implements Node {
  cert: String!
  id: ID!
  root: Boolean!
}

# The connection type for LoggedCertificate.
type LoggedCertificateConnection {
  # A list of edges.
  edges: [LoggedCertificateEdge]

  # A list of nodes.
  nodes: [LoggedCertificate]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

# An edge in a connection.
type LoggedCertificateEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: LoggedCertificate
}

type Macaroon implements Principal {
  # URL for avatar or placeholder
  avatarUrl: String!
  createdAt: ISO8601DateTime
    @deprecated(reason: "Use User fragment on Viewer instead")

  # Email address for principal
  email: String!
  featureFlags: [String!]
    @deprecated(reason: "Use User fragment on Viewer instead")
  hasNodeproxyApps: Boolean
    @deprecated(reason: "Use User fragment on Viewer instead")
  id: ID @deprecated(reason: "Use User fragment on Viewer instead")
  lastRegion: String @deprecated(reason: "Use User fragment on Viewer instead")

  # Display name of principal
  name: String
  organizations(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
  ): OrganizationConnection
    @deprecated(reason: "Use User fragment on Viewer instead")
  personalOrganization: Organization
    @deprecated(reason: "Use User fragment on Viewer instead")
  trust: OrganizationTrust!
  twoFactorProtection: Boolean
    @deprecated(reason: "Use User fragment on Viewer instead")
  username: String @deprecated(reason: "Use User fragment on Viewer instead")
}

type Machine implements Node {
  app: App!
  config: JSON!
  createdAt: ISO8601DateTime!
  egressIpAddresses(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
  ): EgressIPAddressConnection!
  events(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
    kind: String
  ): MachineEventConnection!
  host: Host!
  id: ID!
  instanceId: String!
  ips(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
  ): MachineIPConnection!
  name: String!
  region: String!
  state: String!
  updatedAt: ISO8601DateTime!
}

# The connection type for Machine.
type MachineConnection {
  # A list of edges.
  edges: [MachineEdge]

  # A list of nodes.
  nodes: [Machine]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

# An edge in a connection.
type MachineEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: Machine
}

# A machine state change event
interface MachineEvent {
  id: ID!
  kind: String!
  timestamp: ISO8601DateTime!
}

# The connection type for MachineEvent.
type MachineEventConnection {
  # A list of edges.
  edges: [MachineEventEdge]

  # A list of nodes.
  nodes: [MachineEvent]

  # Information to aid in pagination.
  pageInfo: PageInfo!
}

type MachineEventDestroy implements MachineEvent {
  id: ID!
  kind: String!
  timestamp: ISO8601DateTime!
}

# An edge in a connection.
type MachineEventEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: MachineEvent
}

type MachineEventExit implements MachineEvent {
  exitCode: Int!
  id: ID!
  kind: String!
  metadata: JSON!
  oomKilled: Boolean!
  requestedStop: Boolean!
  timestamp: ISO8601DateTime!
}

type MachineEventGeneric implements MachineEvent {
  id: ID!
  kind: String!
  timestamp: ISO8601DateTime!
}

type MachineEventStart implements MachineEvent {
  id: ID!
  kind: String!
  timestamp: ISO8601DateTime!
}

type MachineIP implements Node {
  family: String!

  # ID of the object.
  id: ID!
  ip: String!
  kind: String!
  maskSize: Int!
}

# The connection type for MachineIP.
type MachineIPConnection {
  # A list of edges.
  edges: [MachineIPEdge]

  # A list of nodes.
  nodes: [MachineIP]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

# An edge in a connection.
type MachineIPEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: MachineIP
}

# Autogenerated input type of MoveApp
input MoveAppInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The application to move
  appId: ID!

  # The node ID of the organization to move the app to
  organizationId: ID!
}

# Autogenerated return type of MoveApp.
type MoveAppPayload {
  app: App!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
}

type Mutations {
  addCertificate(
    # The application to attach the new hostname to
    appId: ID!

    # Certificate's hostname
    hostname: String!
  ): AddCertificatePayload
  addWireGuardPeer(
    # Parameters for AddWireGuardPeer
    input: AddWireGuardPeerInput!
  ): AddWireGuardPeerPayload
  allocateEgressIpAddress(
    # Parameters for AllocateEgressIPAddress
    input: AllocateEgressIPAddressInput!
  ): AllocateEgressIPAddressPayload
  allocateIpAddress(
    # Parameters for AllocateIPAddress
    input: AllocateIPAddressInput!
  ): AllocateIPAddressPayload
  attachPostgresCluster(
    # Parameters for AttachPostgresCluster
    input: AttachPostgresClusterInput!
  ): AttachPostgresClusterPayload
  cancelBuild(
    # The node ID of the build
    buildId: ID!
  ): CancelBuildPayload
  checkCertificate(
    # Parameters for CheckCertificate
    input: CheckCertificateInput!
  ): CheckCertificatePayload
  configureRegions(
    # Parameters for ConfigureRegions
    input: ConfigureRegionsInput!
  ): ConfigureRegionsPayload
  createAddOn(
    # Parameters for CreateAddOn
    input: CreateAddOnInput!
  ): CreateAddOnPayload
  createApp(
    # Parameters for CreateApp
    input: CreateAppInput!
  ): CreateAppPayload
  createBuild(
    # Parameters for CreateBuild
    input: CreateBuildInput!
  ): CreateBuildPayload
  createCheckJob(
    # Parameters for CreateCheckJob
    input: CreateCheckJobInput!
  ): CreateCheckJobPayload
  createCheckJobRun(
    # Parameters for CreateCheckJobRun
    input: CreateCheckJobRunInput!
  ): CreateCheckJobRunPayload
  createDelegatedWireGuardToken(
    # Parameters for CreateDelegatedWireGuardToken
    input: CreateDelegatedWireGuardTokenInput!
  ): CreateDelegatedWireGuardTokenPayload
  createDnsPortal(
    # Parameters for CreateDNSPortal
    input: CreateDNSPortalInput!
  ): CreateDNSPortalPayload
  createDnsPortalSession(
    # Parameters for CreateDNSPortalSession
    input: CreateDNSPortalSessionInput!
  ): CreateDNSPortalSessionPayload
  createDnsRecord(
    # Parameters for CreateDNSRecord
    input: CreateDNSRecordInput!
  ): CreateDNSRecordPayload
  createDoctorReport(
    # Parameters for CreateDoctorReport
    input: CreateDoctorReportInput!
  ): CreateDoctorReportPayload
  createDoctorUrl: CreateDoctorUrlPayload
  createExtensionTosAgreement(
    # Parameters for CreateExtensionTosAgreement
    input: CreateExtensionTosAgreementInput!
  ): CreateExtensionTosAgreementPayload
  createLimitedAccessToken(
    # Parameters for CreateLimitedAccessToken
    input: CreateLimitedAccessTokenInput!
  ): CreateLimitedAccessTokenPayload
  createOrganization(
    # Parameters for CreateOrganization
    input: CreateOrganizationInput!
  ): CreateOrganizationPayload
  createOrganizationInvitation(
    # Parameters for CreateOrganizationInvitation
    input: CreateOrganizationInvitationInput!
  ): CreateOrganizationInvitationPayload
  createPostgresClusterDatabase(
    # Parameters for CreatePostgresClusterDatabase
    input: CreatePostgresClusterDatabaseInput!
  ): CreatePostgresClusterDatabasePayload
  createPostgresClusterUser(
    # Parameters for CreatePostgresClusterUser
    input: CreatePostgresClusterUserInput!
  ): CreatePostgresClusterUserPayload
  createRelease(
    # Parameters for CreateRelease
    input: CreateReleaseInput!
  ): CreateReleasePayload
  createTemplateDeployment(
    # Parameters for CreateTemplateDeployment
    input: CreateTemplateDeploymentInput!
  ): CreateTemplateDeploymentPayload
  createThirdPartyConfiguration(
    # Parameters for CreateThirdPartyConfiguration
    input: CreateThirdPartyConfigurationInput!
  ): CreateThirdPartyConfigurationPayload
  createVolume(
    # Parameters for CreateVolume
    input: CreateVolumeInput!
  ): CreateVolumePayload
  createVolumeSnapshot(
    # Parameters for CreateVolumeSnapshot
    input: CreateVolumeSnapshotInput!
  ): CreateVolumeSnapshotPayload
  deleteAddOn(
    # Parameters for DeleteAddOn
    input: DeleteAddOnInput!
  ): DeleteAddOnPayload

  # Delete an app
  deleteApp(
    # The application to delete
    appId: ID!
  ): DeleteAppPayload
  deleteCertificate(
    # Application to remove hostname from
    appId: ID!

    # Certificate hostname to delete
    hostname: String!
  ): DeleteCertificatePayload
  deleteDelegatedWireGuardToken(
    # Parameters for DeleteDelegatedWireGuardToken
    input: DeleteDelegatedWireGuardTokenInput!
  ): DeleteDelegatedWireGuardTokenPayload
  deleteDeploymentSource(
    # Parameters for DeleteDeploymentSource
    input: DeleteDeploymentSourceInput!
  ): DeleteDeploymentSourcePayload
  deleteDnsPortal(
    # Parameters for DeleteDNSPortal
    input: DeleteDNSPortalInput!
  ): DeleteDNSPortalPayload
  deleteDnsPortalSession(
    # Parameters for DeleteDNSPortalSession
    input: DeleteDNSPortalSessionInput!
  ): DeleteDNSPortalSessionPayload
  deleteDnsRecord(
    # Parameters for DeleteDNSRecord
    input: DeleteDNSRecordInput!
  ): DeleteDNSRecordPayload
  deleteHealthCheckHandler(
    # Parameters for DeleteHealthCheckHandler
    input: DeleteHealthCheckHandlerInput!
  ): DeleteHealthCheckHandlerPayload
  deleteLimitedAccessToken(
    # Parameters for DeleteLimitedAccessToken
    input: DeleteLimitedAccessTokenInput!
  ): DeleteLimitedAccessTokenPayload
  deleteOrganization(
    # Parameters for DeleteOrganization
    input: DeleteOrganizationInput!
  ): DeleteOrganizationPayload
  deleteOrganizationInvitation(
    # Parameters for DeleteOrganizationInvitation
    input: DeleteOrganizationInvitationInput!
  ): DeleteOrganizationInvitationPayload
  deleteOrganizationMembership(
    # Parameters for DeleteOrganizationMembership
    input: DeleteOrganizationMembershipInput!
  ): DeleteOrganizationMembershipPayload
  deleteRemoteBuilder(
    # Parameters for DeleteRemoteBuilder
    input: DeleteRemoteBuilderInput!
  ): DeleteRemoteBuilderPayload
  deleteThirdPartyConfiguration(
    # Parameters for DeleteThirdPartyConfiguration
    input: DeleteThirdPartyConfigurationInput!
  ): DeleteThirdPartyConfigurationPayload
  deleteVolume(
    # Parameters for DeleteVolume
    input: DeleteVolumeInput!
  ): DeleteVolumePayload
  deployImage(
    # Parameters for DeployImage
    input: DeployImageInput!
  ): DeployImagePayload
  detachPostgresCluster(
    # Parameters for DetachPostgresCluster
    input: DetachPostgresClusterInput!
  ): DetachPostgresClusterPayload
  dummyWireGuardPeer(
    # Parameters for DummyWireGuardPeer
    input: DummyWireGuardPeerInput!
  ): DummyWireGuardPeerPayload
  enablePostgresConsul(
    # Parameters for EnablePostgresConsul
    input: EnablePostgresConsulInput!
  ): EnablePostgresConsulPayload
  ensureDepotRemoteBuilder(
    # Parameters for EnsureDepotRemoteBuilder
    input: EnsureDepotRemoteBuilderInput!
  ): EnsureDepotRemoteBuilderPayload
  ensureMachineRemoteBuilder(
    # Parameters for EnsureMachineRemoteBuilder
    input: EnsureMachineRemoteBuilderInput!
  ): EnsureMachineRemoteBuilderPayload
  establishSshKey(
    # Parameters for EstablishSSHKey
    input: EstablishSSHKeyInput!
  ): EstablishSSHKeyPayload
  exportDnsZone(
    # Parameters for ExportDNSZone
    input: ExportDNSZoneInput!
  ): ExportDNSZonePayload
  extendVolume(
    # Parameters for ExtendVolume
    input: ExtendVolumeInput!
  ): ExtendVolumePayload
  finishBuild(
    # Parameters for FinishBuild
    input: FinishBuildInput!
  ): FinishBuildPayload
  forkVolume(
    # Parameters for ForkVolume
    input: ForkVolumeInput!
  ): ForkVolumePayload
  grantPostgresClusterUserAccess(
    # Parameters for GrantPostgresClusterUserAccess
    input: GrantPostgresClusterUserAccessInput!
  ): GrantPostgresClusterUserAccessPayload
  importCertificate(
    # The application to attach the new hostname to
    appId: ID!

    # Full chain for certificate
    fullchain: String!

    # Private signing key for certificate
    privateKey: String!

    # Hostname for certificate (certificate Common Name by default)
    hostname: String
  ): ImportCertificatePayload
  importDnsZone(
    # Parameters for ImportDNSZone
    input: ImportDNSZoneInput!
  ): ImportDNSZonePayload
  issueCertificate(
    # Parameters for IssueCertificate
    input: IssueCertificateInput!
  ): IssueCertificatePayload
  killMachine(
    # Parameters for KillMachine
    input: KillMachineInput!
  ): KillMachinePayload
  launchMachine(
    # Parameters for LaunchMachine
    input: LaunchMachineInput!
  ): LaunchMachinePayload
  lockApp(
    # Parameters for LockApp
    input: LockAppInput!
  ): LockAppPayload
  logOut(
    # Parameters for LogOut
    input: LogOutInput!
  ): LogOutPayload
  moveApp(
    # Parameters for MoveApp
    input: MoveAppInput!
  ): MoveAppPayload
  nomadToMachinesMigration(
    # Parameters for NomadToMachinesMigration
    input: NomadToMachinesMigrationInput!
  ): NomadToMachinesMigrationPayload
  nomadToMachinesMigrationPrep(
    # Parameters for NomadToMachinesMigrationPrep
    input: NomadToMachinesMigrationPrepInput!
  ): NomadToMachinesMigrationPrepPayload
  pauseApp(
    # Parameters for PauseApp
    input: PauseAppInput!
  ): PauseAppPayload
  releaseEgressIpAddress(
    # Parameters for ReleaseEgressIPAddress
    input: ReleaseEgressIPAddressInput!
  ): ReleaseEgressIPAddressPayload
  releaseIpAddress(
    # Parameters for ReleaseIPAddress
    input: ReleaseIPAddressInput!
  ): ReleaseIPAddressPayload
  removeMachine(
    # Parameters for RemoveMachine
    input: RemoveMachineInput!
  ): RemoveMachinePayload
  removeWireGuardPeer(
    # Parameters for RemoveWireGuardPeer
    input: RemoveWireGuardPeerInput!
  ): RemoveWireGuardPeerPayload
  resetAddOnPassword(
    # Parameters for ResetAddOnPassword
    input: ResetAddOnPasswordInput!
  ): ResetAddOnPasswordPayload
  restartAllocation(
    # Parameters for RestartAllocation
    input: RestartAllocationInput!
  ): RestartAllocationPayload
  restartApp(
    # Parameters for RestartApp
    input: RestartAppInput!
  ): RestartAppPayload
  restoreVolumeSnapshot(
    # Parameters for RestoreVolumeSnapshot
    input: RestoreVolumeSnapshotInput!
  ): RestoreVolumeSnapshotPayload
  resumeApp(
    # Parameters for ResumeApp
    input: ResumeAppInput!
  ): ResumeAppPayload
  revokePostgresClusterUserAccess(
    # Parameters for RevokePostgresClusterUserAccess
    input: RevokePostgresClusterUserAccessInput!
  ): RevokePostgresClusterUserAccessPayload
  saveDeploymentSource(
    # Parameters for SaveDeploymentSource
    input: SaveDeploymentSourceInput!
  ): SaveDeploymentSourcePayload
  scaleApp(
    # Parameters for ScaleApp
    input: ScaleAppInput!
  ): ScaleAppPayload
  setAppsV2DefaultOn(
    # Parameters for SetAppsv2DefaultOn
    input: SetAppsv2DefaultOnInput!
  ): SetAppsv2DefaultOnPayload
  setPagerdutyHandler(
    # Parameters for SetPagerdutyHandler
    input: SetPagerdutyHandlerInput!
  ): SetPagerdutyHandlerPayload
  setPlatformVersion(
    # Parameters for SetPlatformVersion
    input: SetPlatformVersionInput!
  ): SetPlatformVersionPayload
  setSecrets(
    # Parameters for SetSecrets
    input: SetSecretsInput!
  ): SetSecretsPayload
  setSlackHandler(
    # Parameters for SetSlackHandler
    input: SetSlackHandlerInput!
  ): SetSlackHandlerPayload
  setVmCount(
    # Parameters for SetVMCount
    input: SetVMCountInput!
  ): SetVMCountPayload
  setVmSize(
    # Parameters for SetVMSize
    input: SetVMSizeInput!
  ): SetVMSizePayload
  startBuild(
    # Parameters for StartBuild
    input: StartBuildInput!
  ): StartBuildPayload
  startMachine(
    # Parameters for StartMachine
    input: StartMachineInput!
  ): StartMachinePayload
  stopAllocation(
    # Parameters for StopAllocation
    input: StopAllocationInput!
  ): StopAllocationPayload
  stopMachine(
    # Parameters for StopMachine
    input: StopMachineInput!
  ): StopMachinePayload
  unlockApp(
    # Parameters for UnlockApp
    input: UnlockAppInput!
  ): UnlockAppPayload
  unsetSecrets(
    # Parameters for UnsetSecrets
    input: UnsetSecretsInput!
  ): UnsetSecretsPayload
  updateAddOn(
    # Parameters for UpdateAddOn
    input: UpdateAddOnInput!
  ): UpdateAddOnPayload
  updateAutoscaleConfig(
    # Parameters for UpdateAutoscaleConfig
    input: UpdateAutoscaleConfigInput!
  ): UpdateAutoscaleConfigPayload
  updateDnsPortal(
    # Parameters for UpdateDNSPortal
    input: UpdateDNSPortalInput!
  ): UpdateDNSPortalPayload
  updateDnsRecord(
    # Parameters for UpdateDNSRecord
    input: UpdateDNSRecordInput!
  ): UpdateDNSRecordPayload
  updateDnsRecords(
    # Parameters for UpdateDNSRecords
    input: UpdateDNSRecordsInput!
  ): UpdateDNSRecordsPayload
  updateOrganizationMembership(
    # Parameters for UpdateOrganizationMembership
    input: UpdateOrganizationMembershipInput!
  ): UpdateOrganizationMembershipPayload
  updateRelease(
    # Parameters for UpdateRelease
    input: UpdateReleaseInput!
  ): UpdateReleasePayload
  updateRemoteBuilder(
    # Parameters for UpdateRemoteBuilder
    input: UpdateRemoteBuilderInput!
  ): UpdateRemoteBuilderPayload
  updateThirdPartyConfiguration(
    # Parameters for UpdateThirdPartyConfiguration
    input: UpdateThirdPartyConfigurationInput!
  ): UpdateThirdPartyConfigurationPayload
  validateWireGuardPeers(
    # Parameters for ValidateWireGuardPeers
    input: ValidateWireGuardPeersInput!
  ): ValidateWireGuardPeersPayload
}

type Network implements Node {
  createdAt: ISO8601DateTime!

  # ID of the object.
  id: ID!
  name: String!
  organization: Organization!
  updatedAt: ISO8601DateTime!
}

# An object with an ID.
interface Node {
  # ID of the object.
  id: ID!
}

# Autogenerated input type of NomadToMachinesMigration
input NomadToMachinesMigrationInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The application to move
  appId: ID!
}

# Autogenerated return type of NomadToMachinesMigration.
type NomadToMachinesMigrationPayload {
  app: App!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
}

# Autogenerated input type of NomadToMachinesMigrationPrep
input NomadToMachinesMigrationPrepInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The application to move
  appId: ID!
}

# Autogenerated return type of NomadToMachinesMigrationPrep.
type NomadToMachinesMigrationPrepPayload {
  app: App!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
}

type Organization implements Node {
  activeDiscountName: String

  # Single sign-on link for the given integration type
  addOnSsoLink: String

  # List third party integrations associated with an organization
  addOns(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
    type: AddOnType
  ): AddOnConnection!

  # Check if the organization has agreed to the extension provider terms of service
  agreedToProviderTos(providerName: String!): Boolean!
  apps(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
  ): AppConnection!
  billable: Boolean!
  billingStatus: BillingStatus!

  # The account credits in cents
  creditBalance: Int!

  # The formatted account credits
  creditBalanceFormatted: String!
    @deprecated(reason: "Use credit_balance instead")
  delegatedWireGuardTokens(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
  ): DelegatedWireGuardTokenConnection!

  # Find a dns portal by name
  dnsPortal(name: String!): DNSPortal!
  dnsPortals(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
  ): DNSPortalConnection!

  # Find a domain by name
  domain(name: String!): Domain
  domains(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
  ): DomainConnection!

  # Single sign-on link for the given extension type
  extensionSsoLink(provider: String!): String
  healthCheckHandlers(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
  ): HealthCheckHandlerConnection!
  healthChecks(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
  ): HealthCheckConnection!
  id: ID!
  internalNumericId: BigInt!
  invitations(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
  ): OrganizationInvitationConnection!
  isCreditCardSaved: Boolean!
  limitedAccessTokens(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
  ): LimitedAccessTokenConnection!
  loggedCertificates(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
  ): LoggedCertificateConnection
  members(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
  ): OrganizationMembershipsConnection!

  # Organization name
  name: String!
  paidPlan: Boolean!

  # Whether the organization can provision beta extensions
  provisionsBetaExtensions: Boolean!

  # Unmodified unique org slug
  rawSlug: String!
  remoteBuilderApp: App
  remoteBuilderImage: String!
  settings: JSON

  # Unique organization slug
  slug: String!
  sshCertificate: String

  # Configurations for third-party caveats to be issued on user macaroons
  thirdPartyConfigurations(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
  ): ThirdPartyConfigurationConnection!
  trust: OrganizationTrust!

  # The type of organization
  type: OrganizationType!

  # The current user's role in the org
  viewerRole: String!

  # Find a peer by name
  wireGuardPeer(name: String!): WireGuardPeer!
  wireGuardPeers(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
  ): WireGuardPeerConnection!
}

enum OrganizationAlertsEnabled {
  # The user has alerts enabled
  ENABLED

  # The user does not have alerts enabled
  NOT_ENABLED
}

# The connection type for Organization.
type OrganizationConnection {
  # A list of edges.
  edges: [OrganizationEdge]

  # A list of nodes.
  nodes: [Organization]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

# An edge in a connection.
type OrganizationEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: Organization
}

type OrganizationInvitation implements Node {
  createdAt: ISO8601DateTime!
  email: String!
  id: ID!

  # The user who created the invitation
  inviter: User!
  organization: Organization!
  redeemed: Boolean!
  redeemedAt: ISO8601DateTime
}

# The connection type for OrganizationInvitation.
type OrganizationInvitationConnection {
  # A list of edges.
  edges: [OrganizationInvitationEdge]

  # A list of nodes.
  nodes: [OrganizationInvitation]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

# An edge in a connection.
type OrganizationInvitationEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: OrganizationInvitation
}

enum OrganizationMemberRole {
  # The user is an administrator of the organization
  ADMIN

  # The user is a member of the organization
  MEMBER
}

# The connection type for User.
type OrganizationMembershipsConnection {
  # A list of edges.
  edges: [OrganizationMembershipsEdge]

  # A list of nodes.
  nodes: [User]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

# An edge in a connection.
type OrganizationMembershipsEdge {
  # The alerts settings the user has in this organization
  alertsEnabled: OrganizationAlertsEnabled!

  # A cursor for use in pagination.
  cursor: String!

  # The date the user joined the organization
  joinedAt: ISO8601DateTime!

  # The item at the end of the edge.
  node: User

  # The role the user has in this organization
  role: OrganizationMemberRole!
}

enum OrganizationTrust {
  # We haven't set a trust level yet
  UNKNOWN

  # Organization has limited access to our service
  RESTRICTED

  # Organization cannot use our services
  BANNED

  # Organization has to prove that is not fraud over time but can use our services
  LOW

  # Organization proved that it's safe to use our services
  HIGH
}

enum OrganizationType {
  # A user's personal organization
  PERSONAL

  # An organization shared between one or more users
  SHARED
}

# Information about pagination in a connection.
type PageInfo {
  # When paginating forwards, the cursor to continue.
  endCursor: String

  # When paginating forwards, are there more items?
  hasNextPage: Boolean!

  # When paginating backwards, are there more items?
  hasPreviousPage: Boolean!

  # When paginating backwards, the cursor to continue.
  startCursor: String
}

# Autogenerated input type of PauseApp
input PauseAppInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The ID of the app
  appId: ID!
}

# Autogenerated return type of PauseApp.
type PauseAppPayload {
  app: App!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
}

enum PlatformVersionEnum {
  # Nomad managed application
  nomad

  # App with only machines
  machines

  # App in migration between nomad and machines
  detached
}

type PostgresClusterAppRole implements AppRole {
  databases: [PostgresClusterDatabase!]!

  # The name of this role
  name: String!
  users: [PostgresClusterUser!]!
}

type PostgresClusterAttachment implements Node {
  databaseName: String!
  databaseUser: String!
  environmentVariableName: String!
  id: ID!
}

# The connection type for PostgresClusterAttachment.
type PostgresClusterAttachmentConnection {
  # A list of edges.
  edges: [PostgresClusterAttachmentEdge]

  # A list of nodes.
  nodes: [PostgresClusterAttachment]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

# An edge in a connection.
type PostgresClusterAttachmentEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: PostgresClusterAttachment
}

type PostgresClusterDatabase {
  name: String!
  users: [String!]!
}

type PostgresClusterUser {
  databases: [String!]!
  isSuperuser: Boolean!
  username: String!
}

type PriceTier {
  unitAmount: String
  upTo: BigInt
}

interface Principal {
  # URL for avatar or placeholder
  avatarUrl: String!
  createdAt: ISO8601DateTime
    @deprecated(reason: "Use User fragment on Viewer instead")

  # Email address for principal
  email: String!
  featureFlags: [String!]
    @deprecated(reason: "Use User fragment on Viewer instead")
  hasNodeproxyApps: Boolean
    @deprecated(reason: "Use User fragment on Viewer instead")
  id: ID @deprecated(reason: "Use User fragment on Viewer instead")
  lastRegion: String @deprecated(reason: "Use User fragment on Viewer instead")

  # Display name of principal
  name: String
  organizations(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
  ): OrganizationConnection
    @deprecated(reason: "Use User fragment on Viewer instead")
  personalOrganization: Organization
    @deprecated(reason: "Use User fragment on Viewer instead")
  trust: OrganizationTrust!
  twoFactorProtection: Boolean
    @deprecated(reason: "Use User fragment on Viewer instead")
  username: String @deprecated(reason: "Use User fragment on Viewer instead")
}

type ProcessGroup {
  maxPerRegion: Int!
  name: String!
  regions: [String!]!
  vmSize: VMSize!
}

type Product {
  name: String!
  tiers: [PriceTier!]!
  type: String!
  unitLabel: String
}

input PropertyInput {
  # The name of the property
  name: String!

  # The value of the property
  value: String
}

type Queries {
  accessTokens(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
    type: AccessTokenType = pat
  ): AccessTokenConnection!

  # Find an add-on by ID or name
  addOn(id: ID, name: String, provider: String): AddOn

  # List add-on service plans
  addOnPlans(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
    type: AddOnType
  ): AddOnPlanConnection!
  addOnProvider(name: String!): AddOnProvider!

  # List add-ons associated with an organization
  addOns(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
    type: AddOnType
  ): AddOnConnection!

  # Find an app by name
  app(name: String, internalId: String): App

  # Validates an app name for app creation
  appNameAvailable(
    # The app name to be validated
    name: String!
  ): Boolean!

  # List apps
  apps(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
    active: Boolean
    role: String
    platform: String
    organizationId: ID
  ): AppConnection!

  # Verifies if an app can undergo a bluegreen deployment
  canPerformBluegreenDeployment(
    # The name of the app
    name: String!
  ): Boolean!

  # Find a certificate by ID
  certificate(id: ID!): AppCertificate
  checkJobs(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
  ): CheckJobConnection!
  checkLocations: [CheckLocation!]!
  currentUser: User! @deprecated(reason: "use viewer instead")

  # Find a domain by name
  domain(name: String!): Domain

  # internal field for inter-service authz check
  flapsAuthzCheck: Boolean!
  githubIntegration: GithubIntegration! @deprecated(reason: "deprecated")
  herokuIntegration: HerokuIntegration!

  # Find an ip address by ID
  ipAddress(id: ID!): IPAddress

  # Returns the latest available tag for a given image repository
  latestImageDetails(
    # <repositry>/<name>:<tag>
    image: String!

    # Fly version to use for tag resolution
    flyVersion: String
  ): ImageVersion!

  # Returns the latest available tag for a given image repository
  latestImageTag(repository: String!, snapshotId: ID): String!

  # Get a single machine
  machine(machineId: String!): Machine!

  # List machines
  machines(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
    appId: String
    state: String
    version: Int
  ): MachineConnection!
  nearestRegion(wireguardGateway: Boolean): Region!

  # Fetches an object given its ID.
  node(
    # ID of the object.
    id: ID!
  ): Node

  # Fetches a list of objects given a list of IDs.
  nodes(
    # IDs of the objects.
    ids: [ID!]!
  ): [Node]!

  # Find an organization by ID
  organization(id: ID, name: String, slug: String): Organization
  organizations(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
    withBillingIssuesOnly: Boolean
    admin: Boolean
    type: OrganizationType
  ): OrganizationConnection!
  personalOrganization: Organization!

  # fly.io platform information
  platform: FlyPlatform!

  # List postgres attachments
  postgresAttachments(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
    appName: String
    postgresAppName: String!
  ): PostgresClusterAttachmentConnection!

  # Fly.io product and price information
  products: [Product!]!

  # Whether the authentication token only allows for user access
  userOnlyToken: Boolean!
  validateConfig(definition: JSON!): AppConfig!
  viewer: Principal!

  # Find a persistent volume by ID
  volume(id: ID!): Volume
}

type Region {
  # The IATA airport code for this region
  code: String!
  gatewayAvailable: Boolean!

  # The latitude of this region
  latitude: Float

  # The longitude of this region
  longitude: Float

  # The name of this region
  name: String!
  processGroup: String
  requiresPaidPlan: Boolean!
}

type RegionPlacement {
  # The desired number of allocations
  count: Int

  # The region code
  region: String!
}

type Release implements Node {
  config: AppConfig
  createdAt: ISO8601DateTime!
  deploymentStrategy: DeploymentStrategy!

  # A description of the release
  description: String!
  evaluationId: String

  # Unique ID
  id: ID!

  # Docker image
  image: Image

  # Docker image URI
  imageRef: String
  inProgress: Boolean! @deprecated(reason: "use deployment.inProgress")
  metadata: JSON

  # The reason for the release
  reason: String!

  # Version release reverted to
  revertedTo: Int
  stable: Boolean!

  # The status of the release
  status: String!
  updatedAt: ISO8601DateTime!

  # The user who created the release
  user: User

  # The version of the release
  version: Int!
}

type ReleaseCommand implements Node {
  app: App!
  command: String!
  evaluationId: String
  exitCode: Int
  failed: Boolean!
  id: ID!
  inProgress: Boolean!
  instanceId: String
  status: String!
  succeeded: Boolean!
}

# The connection type for Release.
type ReleaseConnection {
  # A list of edges.
  edges: [ReleaseEdge]

  # A list of nodes.
  nodes: [Release]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

# An edge in a connection.
type ReleaseEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: Release
}

# Autogenerated input type of ReleaseEgressIPAddress
input ReleaseEgressIPAddressInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The ID of the app
  appId: ID!

  # The ID of the machine
  machineId: ID!
}

# Autogenerated return type of ReleaseEgressIPAddress.
type ReleaseEgressIPAddressPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  v4: String
  v6: String
}

# Autogenerated input type of ReleaseIPAddress
input ReleaseIPAddressInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The ID of the app
  appId: ID

  # The id of the ip address to release
  ipAddressId: ID
  ip: String

  # The name of the associated service
  serviceName: String
}

# Autogenerated return type of ReleaseIPAddress.
type ReleaseIPAddressPayload {
  app: App!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
}

type ReleaseUnprocessed implements Node {
  configDefinition: JSON
  createdAt: ISO8601DateTime!
  deploymentStrategy: DeploymentStrategy!

  # A description of the release
  description: String!
  evaluationId: String

  # Unique ID
  id: ID!

  # Docker image
  image: Image

  # Docker image URI
  imageRef: String
  inProgress: Boolean! @deprecated(reason: "use deployment.inProgress")

  # The reason for the release
  reason: String!

  # Version release reverted to
  revertedTo: Int
  stable: Boolean!

  # The status of the release
  status: String!
  updatedAt: ISO8601DateTime!

  # The user who created the release
  user: User

  # The version of the release
  version: Int!
}

# The connection type for ReleaseUnprocessed.
type ReleaseUnprocessedConnection {
  # A list of edges.
  edges: [ReleaseUnprocessedEdge]

  # A list of nodes.
  nodes: [ReleaseUnprocessed]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

# An edge in a connection.
type ReleaseUnprocessedEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: ReleaseUnprocessed
}

type RemoteDockerBuilderAppRole implements AppRole {
  # The name of this role
  name: String!
}

# Autogenerated input type of RemoveMachine
input RemoveMachineInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The ID of the app
  appId: ID

  # machine id
  id: String!

  # force kill machine if it's running
  kill: Boolean
}

# Autogenerated return type of RemoveMachine.
type RemoveMachinePayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  machine: Machine!
}

# Autogenerated input type of RemoveWireGuardPeer
input RemoveWireGuardPeerInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The node ID of the organization
  organizationId: ID!

  # The name of the peer to remove
  name: String!

  # Add via NATS transaction (for testing only, nosy users)
  nats: Boolean
}

# Autogenerated return type of RemoveWireGuardPeer.
type RemoveWireGuardPeerPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The organization that owned the peer
  organization: Organization!
}

# Autogenerated input type of ResetAddOnPassword
input ResetAddOnPasswordInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The ID of the add-on whose password should be reset
  name: String!
}

# Autogenerated return type of ResetAddOnPassword.
type ResetAddOnPasswordPayload {
  addOn: AddOn!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
}

# Autogenerated input type of RestartAllocation
input RestartAllocationInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The ID of the app
  appId: ID!

  # The ID of the app
  allocId: ID!
}

# Autogenerated return type of RestartAllocation.
type RestartAllocationPayload {
  allocation: Allocation!
  app: App!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
}

# Autogenerated input type of RestartApp
input RestartAppInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The ID of the app
  appId: ID!
}

# Autogenerated return type of RestartApp.
type RestartAppPayload {
  app: App!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
}

# Autogenerated input type of RestoreVolumeSnapshot
input RestoreVolumeSnapshotInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  volumeId: ID!
  snapshotId: ID!
}

# Autogenerated return type of RestoreVolumeSnapshot.
type RestoreVolumeSnapshotPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  snapshot: VolumeSnapshot!
  volume: Volume!
}

# Autogenerated input type of ResumeApp
input ResumeAppInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The ID of the app
  appId: ID!
}

# Autogenerated return type of ResumeApp.
type ResumeAppPayload {
  app: App!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
}

# Autogenerated input type of RevokePostgresClusterUserAccess
input RevokePostgresClusterUserAccessInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The name of the postgres cluster app
  appName: String!

  # The username to revoke
  username: String!

  # The database to revoke access to
  databaseName: String!
}

# Autogenerated return type of RevokePostgresClusterUserAccess.
type RevokePostgresClusterUserAccessPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  database: PostgresClusterDatabase!
  postgresClusterRole: PostgresClusterAppRole!
  user: PostgresClusterUser!
}

enum RuntimeType {
  # Fly Container Runtime
  FIRECRACKER

  # Fly JavaScript Runtime
  NODEPROXY
}

# Autogenerated input type of SaveDeploymentSource
input SaveDeploymentSourceInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The application to update
  appId: String!
  provider: String!
  repositoryId: String!
  ref: String
  baseDir: String
  skipBuild: Boolean
}

# Autogenerated return type of SaveDeploymentSource.
type SaveDeploymentSourcePayload {
  app: App
  build: Build

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
}

# Autogenerated input type of ScaleApp
input ScaleAppInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The ID of the app
  appId: ID!

  # Regions to scale
  regions: [ScaleRegionInput!]!
}

# Autogenerated return type of ScaleApp.
type ScaleAppPayload {
  app: App!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  delta: [ScaleRegionChange!]!
  placement: [RegionPlacement!]!
}

type ScaleRegionChange {
  # The original value
  fromCount: Int!

  # The region code
  region: String!

  # The new value
  toCount: Int
}

# Region placement configuration
input ScaleRegionInput {
  # The region to configure
  region: String!

  # The value to change by
  count: Int!
}

type Secret implements Node {
  createdAt: ISO8601DateTime!

  # The digest of the secret value
  digest: String!
  id: ID!

  # The name of the secret
  name: String!

  # The user who initiated the deployment
  user: User
}

# A secure configuration value
input SecretInput {
  # The unqiue key for this secret
  key: String!

  # The value of this secret
  value: String!
}

# Global port routing
type Service {
  # Health checks
  checks: [Check!]!
  description: String!

  # Hard concurrency limit
  hardConcurrency: Int!

  # Application port to forward traffic to
  internalPort: Int!

  # Ports to listen on
  ports: [ServicePort!]!

  # Protocol to listen on
  protocol: ServiceProtocolType!

  # Soft concurrency limit
  softConcurrency: Int!
}

enum ServiceHandlerType {
  # Convert TLS connection to unencrypted TCP
  TLS

  # Handle TLS for PostgreSQL connections
  PG_TLS

  # Convert TCP connection to HTTP
  HTTP

  # Convert TCP connection to HTTP (at the edge)
  EDGE_HTTP

  # Wrap TCP connection in PROXY protocol
  PROXY_PROTO
}

# Global port routing
input ServiceInput {
  # Protocol to listen on
  protocol: ServiceProtocolType!

  # Ports to listen on
  ports: [ServiceInputPort!]

  # Application port to forward traffic to
  internalPort: Int!

  # Health checks
  checks: [CheckInput!]

  # Soft concurrency limit
  softConcurrency: Int

  # Hard concurrency limit
  hardConcurrency: Int
}

# Service port
input ServiceInputPort {
  # Port to listen on
  port: Int!

  # Handlers to apply before forwarding service traffic
  handlers: [ServiceHandlerType!]

  # tls options
  tlsOptions: ServicePortTlsOptionsInput
}

# Service port
type ServicePort {
  # End port for range
  endPort: Int

  # Handlers to apply before forwarding service traffic
  handlers: [ServiceHandlerType!]!

  # Port to listen on
  port: Int

  # Start port for range
  startPort: Int
}

# TLS handshakes options for a port
input ServicePortTlsOptionsInput {
  defaultSelfSigned: Boolean
}

enum ServiceProtocolType {
  # TCP protocol
  TCP

  # UDP protocl
  UDP
}

# Autogenerated input type of SetAppsv2DefaultOn
input SetAppsv2DefaultOnInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The organization slug
  organizationSlug: String!

  # Whether or not new apps in this org use Apps V2 by default
  defaultOn: Boolean!
}

# Autogenerated return type of SetAppsv2DefaultOn.
type SetAppsv2DefaultOnPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  organization: Organization!
}

# Autogenerated input type of SetPagerdutyHandler
input SetPagerdutyHandlerInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The node ID of the organization
  organizationId: ID!

  # Handler name
  name: String!

  # PagerDuty API token
  pagerdutyToken: String!

  # Map of alert severity levels to PagerDuty severity levels
  pagerdutyStatusMap: JSON
}

# Autogenerated return type of SetPagerdutyHandler.
type SetPagerdutyHandlerPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  handler: HealthCheckHandler!
}

# Autogenerated input type of SetPlatformVersion
input SetPlatformVersionInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The ID of the app
  appId: ID!

  # nomad or machines
  platformVersion: String!

  # Unique lock ID
  lockId: ID
}

# Autogenerated return type of SetPlatformVersion.
type SetPlatformVersionPayload {
  app: App!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
}

# Autogenerated input type of SetSecrets
input SetSecretsInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The ID of the app
  appId: ID!

  # Secrets to set
  secrets: [SecretInput!]!

  # By default, we set only the secrets you specify. Set this to true to replace all secrets.
  replaceAll: Boolean
}

# Autogenerated return type of SetSecrets.
type SetSecretsPayload {
  app: App!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  release: Release
}

# Autogenerated input type of SetSlackHandler
input SetSlackHandlerInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The node ID of the organization
  organizationId: ID!

  # Handler name
  name: String!

  # Slack Webhook URL to use for health check notifications
  slackWebhookUrl: String!

  # Slack channel to send messages to, defaults to #general
  slackChannel: String

  # User name to display on Slack Messages (defaults to Fly)
  slackUsername: String

  # Icon to show with Slack messages
  slackIconUrl: String
}

# Autogenerated return type of SetSlackHandler.
type SetSlackHandlerPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  handler: HealthCheckHandler!
}

# Autogenerated input type of SetVMCount
input SetVMCountInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The ID of the app
  appId: ID!

  # Counts for VM groups
  groupCounts: [VMCountInput!]!

  # Unique lock ID
  lockId: ID
}

# Autogenerated return type of SetVMCount.
type SetVMCountPayload {
  app: App!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  release: Release
  taskGroupCounts: [TaskGroupCount!]!
  warnings: [String!]!
}

# Autogenerated input type of SetVMSize
input SetVMSizeInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The ID of the app
  appId: ID!

  # The name of the vm size to set
  sizeName: String!

  # Optionally request more memory
  memoryMb: Int

  # Process group to modify
  group: String
}

# Autogenerated return type of SetVMSize.
type SetVMSizePayload {
  app: App!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # Process Group scale change applied to (if any)
  processGroup: ProcessGroup

  # Default app vm size
  vmSize: VMSize
}

# Autogenerated input type of StartBuild
input StartBuildInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The ID of the app
  appId: ID!
}

# Autogenerated return type of StartBuild.
type StartBuildPayload {
  build: Build!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
}

# Autogenerated input type of StartMachine
input StartMachineInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The ID of the app
  appId: ID

  # machine id
  id: String!
}

# Autogenerated return type of StartMachine.
type StartMachinePayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  machine: Machine!
}

# Autogenerated input type of StopAllocation
input StopAllocationInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The ID of the app
  appId: ID!

  # The ID of the app
  allocId: ID!
}

# Autogenerated return type of StopAllocation.
type StopAllocationPayload {
  allocation: Allocation!
  app: App!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
}

# Autogenerated input type of StopMachine
input StopMachineInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The ID of the app
  appId: ID

  # machine id
  id: String!

  # signal to send the machine
  signal: String

  # how long to wait before force killing the machine
  killTimeoutSecs: Int
}

# Autogenerated return type of StopMachine.
type StopMachinePayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  machine: Machine!
}

type TaskGroupCount {
  count: Int!
  name: String!
}

type TemplateDeployment implements Node {
  apps(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
  ): AppConnection!
  id: ID!
  organization: Organization!
  status: String!
}

# Configuration for third-party caveats to be added to user macaroons
type ThirdPartyConfiguration implements Node {
  # Restrictions to be placed on third-party caveats
  caveats: CaveatSet
  createdAt: ISO8601DateTime!

  # Whether to add this third-party caveat on tokens issued via `flyctl tokens create`
  customLevel: ThirdPartyConfigurationLevel!

  # Whether to add this third-party caveat on session tokens issued to flyctl
  flyctlLevel: ThirdPartyConfigurationLevel!
  id: ID!

  # Location URL of the third-party service capable of discharging
  location: String!

  # Friendly name for this configuration
  name: String!

  # Organization that owns this third party configuration
  organization: Organization!

  # Whether to add this third-party caveat on Fly.io session tokens
  uiexLevel: ThirdPartyConfigurationLevel!
  updatedAt: ISO8601DateTime!
}

# The connection type for ThirdPartyConfiguration.
type ThirdPartyConfigurationConnection {
  # A list of edges.
  edges: [ThirdPartyConfigurationEdge]

  # A list of nodes.
  nodes: [ThirdPartyConfiguration]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

# An edge in a connection.
type ThirdPartyConfigurationEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: ThirdPartyConfiguration
}

enum ThirdPartyConfigurationLevel {
  # Configuration is disabled and cannot be opted into
  DISABLED

  # Configuration can be manually opted into
  OPT_IN

  # Configuration is enabled by default. All members can opt out
  MEMBER_OPT_OUT

  # Configuration is enabled by default. Admins can opt out
  ADMIN_OPT_OUT

  # Configuration is enabled by default. No one can opt out
  REQUIRED
}

# Autogenerated input type of UnlockApp
input UnlockAppInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The ID of the app
  appId: ID!

  # Unique lock ID
  lockId: ID!
}

# Autogenerated return type of UnlockApp.
type UnlockAppPayload {
  app: App!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
}

# Autogenerated input type of UnsetSecrets
input UnsetSecretsInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The ID of the app
  appId: ID!

  # Secret keys to unset
  keys: [String!]!
}

# Autogenerated return type of UnsetSecrets.
type UnsetSecretsPayload {
  app: App!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  release: Release
}

# Autogenerated input type of UpdateAddOn
input UpdateAddOnInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The add-on ID to update
  addOnId: ID

  # The add-on name to update
  name: String

  # The add-on plan ID
  planId: ID

  # Options specific to the add-on
  options: JSON

  # Metadata for the add-on
  metadata: JSON

  # Desired regions to place replicas in
  readRegions: [String!]

  # The add-on service provider type
  provider: String
}

# Autogenerated return type of UpdateAddOn.
type UpdateAddOnPayload {
  addOn: AddOn!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
}

# Autogenerated input type of UpdateAutoscaleConfig
input UpdateAutoscaleConfigInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The ID of the app
  appId: ID!
  enabled: Boolean
  minCount: Int
  maxCount: Int
  balanceRegions: Boolean

  # Region configs
  regions: [AutoscaleRegionConfigInput!]
  resetRegions: Boolean
}

# Autogenerated return type of UpdateAutoscaleConfig.
type UpdateAutoscaleConfigPayload {
  app: App!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
}

# Autogenerated input type of UpdateDNSPortal
input UpdateDNSPortalInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The node ID of the organization
  dnsPortalId: ID!

  # The unique name of this portal.
  name: String

  # The title of this portal
  title: String

  # The return url for this portal
  returnUrl: String

  # The text to display for the return url link
  returnUrlText: String

  # The support url for this portal
  supportUrl: String

  # The text to display for the support url link
  supportUrlText: String

  # The primary branding color
  primaryColor: String

  # The secondary branding color
  accentColor: String
}

# Autogenerated return type of UpdateDNSPortal.
type UpdateDNSPortalPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  dnsPortal: DNSPortal!
}

# Autogenerated input type of UpdateDNSRecord
input UpdateDNSRecordInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The node ID of the DNS record
  recordId: ID!

  # The dns record name
  name: String

  # The TTL in seconds
  ttl: Int

  # The content of the record
  rdata: String
}

# Autogenerated return type of UpdateDNSRecord.
type UpdateDNSRecordPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  record: DNSRecord!
}

# Autogenerated input type of UpdateDNSRecords
input UpdateDNSRecordsInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The node ID of the domain
  domainId: ID!
  changes: [DNSRecordChangeInput!]!
}

# Autogenerated return type of UpdateDNSRecords.
type UpdateDNSRecordsPayload {
  changes: [DNSRecordDiff!]!

  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  domain: Domain!
  warnings: [DNSRecordWarning!]!
}

# Autogenerated input type of UpdateOrganizationMembership
input UpdateOrganizationMembershipInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The node ID of the organization
  organizationId: ID!

  # The node ID of the user
  userId: ID!

  # The new role for the user
  role: OrganizationMemberRole!

  # The new alert settings for the user
  alertsEnabled: OrganizationAlertsEnabled
}

# Autogenerated return type of UpdateOrganizationMembership.
type UpdateOrganizationMembershipPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  organization: Organization!
  user: User!
}

# Autogenerated input type of UpdateRelease
input UpdateReleaseInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The ID of the release
  releaseId: ID!

  # The new status for the release
  status: String

  # The metadata for the release
  metadata: JSON
}

# Autogenerated return type of UpdateRelease.
type UpdateReleasePayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  release: Release!
}

# Autogenerated input type of UpdateRemoteBuilder
input UpdateRemoteBuilderInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The node ID of the organization
  organizationId: ID!

  # Docker image reference
  image: String!
}

# Autogenerated return type of UpdateRemoteBuilder.
type UpdateRemoteBuilderPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  organization: Organization!
}

# Autogenerated input type of UpdateThirdPartyConfiguration
input UpdateThirdPartyConfigurationInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String

  # The node ID of the configuration
  thirdPartyConfigurationId: ID!

  # Friendly name for this configuration
  name: String

  # Location URL of the third-party service capable of discharging
  location: String

  # Restrictions to be placed on third-party caveats
  caveats: CaveatSet

  # Whether to add this third-party caveat on session tokens issued to flyctl
  flyctlLevel: ThirdPartyConfigurationLevel

  # Whether to add this third-party caveat on Fly.io session tokens
  uiexLevel: ThirdPartyConfigurationLevel

  # Whether to add this third-party caveat on tokens issued via `flyctl tokens create`
  customLevel: ThirdPartyConfigurationLevel
}

# Autogenerated return type of UpdateThirdPartyConfiguration.
type UpdateThirdPartyConfigurationPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  thirdPartyConfiguration: ThirdPartyConfiguration!
}

type User implements Node & Principal {
  # Check if the organization has agreed to the extension provider terms of service
  agreedToProviderTos(providerName: String!): Boolean!

  # URL for avatar or placeholder
  avatarUrl: String!
  createdAt: ISO8601DateTime!

  # Email address for user (private)
  email: String!

  # Whether to create new organizations under Hobby plan
  enablePaidHobby: Boolean!
  featureFlags: [String!]!
  hasNodeproxyApps: Boolean!
  id: ID!
  internalNumericId: Int!
  lastRegion: String

  # Display / full name for user (private)
  name: String
  organizations(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
  ): OrganizationConnection!
    @deprecated(reason: "Use query.organizations instead")
  personalOrganization: Organization!
    @deprecated(reason: "Use query.personalOrganization instead")
  trust: OrganizationTrust!
  twoFactorProtection: Boolean!

  # Public username for user
  username: String
}

type UserCoupon implements Node {
  createdAt: ISO8601DateTime!
  id: ID!

  # Organization that owns this app
  organization: Organization!
  updatedAt: ISO8601DateTime!
}

type VM implements Node {
  attachedVolumes(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
  ): VolumeConnection!
  canary: Boolean!
  checks(
    # Filter checks by name
    name: String
  ): [CheckState!]!
  createdAt: ISO8601DateTime!
  criticalCheckCount: Int!

  # Desired status
  desiredStatus: String!
  events: [AllocationEvent!]!
  failed: Boolean!
  healthy: Boolean!

  # Unique ID for this instance
  id: ID!

  # Short unique ID for this instance
  idShort: ID!

  # Indicates if this instance is from the latest job version
  latestVersion: Boolean!
  passingCheckCount: Int!

  # Private IPv6 address for this instance
  privateIP: String
  recentLogs(
    # Max number of entries to return
    limit: Int = 10

    # Max age of log entries in seconds
    range: Int = 300
  ): [LogEntry!]!

  # Region this allocation is running in
  region: String!
  restarts: Int!

  # Current status
  status: String!
  taskName: String!
  totalCheckCount: Int!
  transitioning: Boolean!
  updatedAt: ISO8601DateTime!

  # The configuration version of this instance
  version: Int!
  warningCheckCount: Int!
}

# The connection type for VM.
type VMConnection {
  activeCount: Int!
  completeCount: Int!

  # A list of edges.
  edges: [VMEdge]
  failedCount: Int!
  inactiveCount: Int!
  lostCount: Int!

  # A list of nodes.
  nodes: [VM]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  pendingCount: Int!
  runningCount: Int!
  totalCount: Int!
}

input VMCountInput {
  # VM group name
  group: String

  # The desired count
  count: Int

  # Max number of VMs to allow per region
  maxPerRegion: Int
}

# An edge in a connection.
type VMEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: VM
}

type VMSize {
  cpuCores: Float!
  maxMemoryMb: Int!
  memoryGb: Float!
  memoryIncrementsMb: [Int!]!
  memoryMb: Int!
  name: String!
  priceMonth: Float!
  priceSecond: Float!
}

# Autogenerated input type of ValidateWireGuardPeers
input ValidateWireGuardPeersInput {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  peerIps: [String!]!
}

# Autogenerated return type of ValidateWireGuardPeers.
type ValidateWireGuardPeersPayload {
  # A unique identifier for the client performing the mutation.
  clientMutationId: String
  invalidPeerIps: [String!]!
  validPeerIps: [String!]!
}

type Volume implements Node {
  app: App!
  attachedAllocation: Allocation
  attachedAllocationId: String
  attachedMachine: Machine
  createdAt: ISO8601DateTime!
  encrypted: Boolean!
  host: Host!
  id: ID!
  internalId: String!
  name: String!
  region: String!
  sizeGb: Int!
  snapshotRetentionDays: Int
  snapshots(
    # Returns the elements in the list that come after the specified cursor.
    after: String

    # Returns the elements in the list that come before the specified cursor.
    before: String

    # Returns the first _n_ elements from the list.
    first: Int

    # Returns the last _n_ elements from the list.
    last: Int
  ): VolumeSnapshotConnection!
  state: String!
  status: String!
  usedBytes: BigInt!
}

# The connection type for Volume.
type VolumeConnection {
  # A list of edges.
  edges: [VolumeEdge]

  # A list of nodes.
  nodes: [Volume]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

# An edge in a connection.
type VolumeEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: Volume
}

type VolumeSnapshot implements Node {
  createdAt: ISO8601DateTime!
  digest: String!
  id: ID!
  retentionDays: Int
  size: BigInt!
  volume: Volume!
}

# The connection type for VolumeSnapshot.
type VolumeSnapshotConnection {
  # A list of edges.
  edges: [VolumeSnapshotEdge]

  # A list of nodes.
  nodes: [VolumeSnapshot]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

# An edge in a connection.
type VolumeSnapshotEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: VolumeSnapshot
}

type WireGuardPeer implements Node {
  id: ID!
  name: String!
  network: String
  peerip: String!
  pubkey: String!
  region: String!
}

# The connection type for WireGuardPeer.
type WireGuardPeerConnection {
  # A list of edges.
  edges: [WireGuardPeerEdge]

  # A list of nodes.
  nodes: [WireGuardPeer]

  # Information to aid in pagination.
  pageInfo: PageInfo!
  totalCount: Int!
}

# An edge in a connection.
type WireGuardPeerEdge {
  # A cursor for use in pagination.
  cursor: String!

  # The item at the end of the edge.
  node: WireGuardPeer
}

name: CI - Dev

on:
  pull_request:
    types:
      - opened
      - synchronize
      - reopened
      - ready_for_review
  # push:
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/master' }}

jobs:
  test:
    uses: ./.github/workflows/test.yml

  # create a dev tag for every branch except master
  tag_version:
    if: ${{ github.ref_type == 'branch' && github.ref_name != 'master' && github.event.pull_request.head.repo.full_name == github.repository && github.actor != 'dependabot[bot]' }}
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.version.outputs.next }}
      tag: v${{ steps.version.outputs.next }}
    steps:
      - name: Debug
        if: runner.debug == '1'
        run: echo "$GITHUB_CONTEXT"
        env:
          GITHUB_CONTEXT: ${{ toJSON(github) }}
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: actions/setup-go@v5
        with:
          go-version-file: "go.mod"
      - name: Get version number
        id: version
        run: |
          echo "next=$(go run ./tools/version next)" >> "$GITHUB_OUTPUT"
      - name: Print version number
        run: |
          echo "### Release Info :rocket:" >> $GITHUB_STEP_SUMMARY
          echo "Version: ${{ steps.version.outputs.next }}" >> $GITHUB_STEP_SUMMARY
      - name: Create tag
        uses: actions/github-script@v7
        env:
          TAG_NAME: v${{ steps.version.outputs.next }}
        with:
          script: |
            const { TAG_NAME } = process.env;
            await github.rest.git.createTag({
              owner: context.repo.owner,
              repo: context.repo.repo,
              tag: TAG_NAME,
              message: TAG_NAME,
              object: context.sha,
              type: 'commit'
            });
            await github.rest.git.createRef({
              owner: context.repo.owner,
              repo: context.repo.repo,
              ref: `refs/tags/${TAG_NAME}`,
              sha: context.sha
            });

          github-token: ${{ secrets.FLYIO_BUILDBOT_GITHUB_TOKEN }}

  # # we can remove this workflow_call once the release.yml workflow file is
  # # merged into the default branch
  # build_and_release:
  #   uses:
  #     ./.github/workflows/release.yml
  #   needs: tag_version
  #   secrets: inherit

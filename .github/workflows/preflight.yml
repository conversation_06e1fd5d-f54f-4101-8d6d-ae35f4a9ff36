name: Preflight Tests

on:
  workflow_dispatch:
    inputs:
      region:
        description: Region
        required: false
        default: ord
        type: string
  workflow_call:

jobs:
  preflight-tests:
    if: ${{ github.repository == 'superfly/flyctl' }}
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        parallelism: [20]
        index:
          [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19]
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: "go.mod"
          check-latest: true
      - name: Get go version
        id: go-version
        run: echo "name=version::$(go env GOVERSION)" >> $GITHUB_OUTPUT
      - name: Install gotesplit, set FLY_PREFLIGHT_TEST_APP_PREFIX
        run: |
          curl -sfL https://raw.githubusercontent.com/Songmu/gotesplit/v0.2.1/install.sh | sh -s
          echo "FLY_PREFLIGHT_TEST_APP_PREFIX=gha-$GITHUB_RUN_ID-$GITHUB_RUN_ATTEMPT" >> "$GITHUB_ENV"
      # If this workflow is triggered by code changes (eg PRs), download the binary to save time.
      - uses: actions/download-artifact@v4
        with:
          name: flyctl
          path: master-build
        continue-on-error: true
      # But if this is a manual run, build the binary first.
      - run: make
      - name: Run preflight tests
        id: preflight
        env:
          FLY_PREFLIGHT_TEST_ACCESS_TOKEN: ${{ secrets.FLYCTL_PREFLIGHT_CI_FLY_API_TOKEN }}
          FLY_PREFLIGHT_TEST_FLY_ORG: flyctl-ci-preflight
          FLY_PREFLIGHT_TEST_FLY_REGIONS: ${{ inputs.region }}
          FLY_PREFLIGHT_TEST_NO_PRINT_HISTORY_ON_FAIL: "true"
          FLY_FORCE_TRACE: "true"
        run: |
          (test -e master-build/flyctl) && mv master-build/flyctl bin/flyctl
          chmod +x bin/flyctl
          export PATH=$PWD/bin:$PATH
          echo -n failed= >> $GITHUB_OUTPUT
          ./scripts/preflight.sh -r "${{ github.ref }}" -t "${{ matrix.parallelism }}" -i "${{ matrix.index }}" -o $GITHUB_OUTPUT
      - name: Post failure to slack
        if: ${{ github.ref == 'refs/heads/master' && failure() }}
        uses: slackapi/slack-github-action@485a9d42d3a73031f12ec201c457e2162c45d02d
        with:
          webhook: ${{ secrets.PREFLIGHT_SLACK_WEBHOOK_URL }}
          webhook-type: incoming-webhook
          payload: |
            {
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": ":sob: preflight tests failed: ${{ steps.preflight.outputs.failed }} ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                  }
                }
              ]
            }
      - uses: superfly/flyctl-actions/setup-flyctl@master
        if: always()
      - name: Clean up any un-deleted preflight apps
        if: always()
        env:
          FLY_API_TOKEN: ${{ secrets.FLYCTL_PREFLIGHT_CI_FLY_API_TOKEN }}
          FLY_PREFLIGHT_TEST_FLY_ORG: flyctl-ci-preflight
        run: |
          ./scripts/delete_preflight_apps.sh "$FLY_PREFLIGHT_TEST_APP_PREFIX"

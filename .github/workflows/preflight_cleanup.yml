name: Preflight Tests Cleanup

on:
  schedule:
    - cron: "*/30 * * * *"
  workflow_dispatch:
    inputs:
      reason:
        description: Brief reason for running this workflow manually
        required: false
        default: User initiated run
        type: string

jobs:
  preflight-tests:
    if: ${{ github.repository == 'superfly/flyctl' }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: "go.mod"
          check-latest: true
      - name: Get go version
        id: go-version
        run: echo "name=version::$(go env GOVERSION)" >> $GITHUB_OUTPUT
      - uses: superfly/flyctl-actions/setup-flyctl@master
      - name: Run cleanup script
        env:
          FLY_PREFLIGHT_TEST_ACCESS_TOKEN: ${{ secrets.FLYCTL_PREFLIGHT_CI_FLY_API_TOKEN }}
          FLY_PREFLIGHT_TEST_FLY_ORG: flyctl-ci-preflight
        run: |
          go run ./scripts/clean-up-preflight-apps
      - name: Post failure to slack
        if: ${{ github.ref == 'refs/heads/master' && failure() }}
        uses: slackapi/slack-github-action@485a9d42d3a73031f12ec201c457e2162c45d02d
        with:
          webhook: ${{ secrets.PREFLIGHT_SLACK_WEBHOOK_URL }}
          webhook-type: incoming-webhook
          payload: |
            {
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": ":warning: preflight test cleanup failed: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                  }
                }
              ]
            }

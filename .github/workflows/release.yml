name: Build & Release

on:
  push:
    tags:
      - v**

  workflow_run:
    workflows: ["Automatically release a new version of flyctl"]
    types:
      - completed

  workflow_dispatch:

  # need this until this file is on the default branch so tag creates are picked up
  workflow_call:

permissions:
  contents: write
  packages: write

# concurrency:
#   group: ${{ github.workflow }}-${{ github.ref }}
#   cancel-in-progress: true

jobs:
  debug:
    runs-on: ubuntu-latest
    steps:
      - run: echo "$GITHUB_CONTEXT"
        env:
          GITHUB_CONTEXT: ${{ toJSON(github) }}

  meta:
    runs-on: ubuntu-latest
    outputs:
      sha_short: ${{ steps.gen.outputs.sha_short }}
      json: ${{ steps.gen.outputs.json }}
      version: ${{ fromJSON(steps.gen.outputs.json).version }}
      tag: ${{ fromJSON(steps.gen.outputs.json).tag }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          fetch-tags: true
      - uses: actions/setup-go@v5
        with:
          go-version-file: "go.mod"
      - name: Generate release meta
        run: |
          go run ./tools/version show > release.json
      - name: Output release meta
        id: gen
        run: |
          cat release.json | jq
          echo "sha_short=$(git rev-parse --short HEAD)" >> "$GITHUB_OUTPUT"
          echo "json=$(cat release.json)" >> "$GITHUB_OUTPUT"
      - uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: |
            release.json
          overwrite: true

  build:
    needs: meta
    strategy:
      matrix:
        GOOS: [linux, windows, darwin]
    runs-on: ubuntu-latest-m
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: actions/setup-go@v5
        with:
          go-version-file: "go.mod"
      - name: Place wintun.dll
        run: cp -r deps/wintun/bin/amd64/wintun.dll ./
      - id: cache
        uses: actions/cache@v4
        with:
          path: dist/${{ matrix.GOOS }}
          key: ${{ matrix.GOOS }}-${{ needs.meta.outputs.sha_short }}
      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.FLYIOBUILDS_DOCKERHUB_USERNAME }}
          password: ${{ secrets.FLYIOBUILDS_DOCKERHUB_TOKEN }}
      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Run GoReleaser
        if: steps.cache.outputs.cache-hit != 'true' # do not run if cache hit
        uses: goreleaser/goreleaser-action@v5
        with:
          distribution: goreleaser-pro
          version: latest
          args: release --clean -f .goreleaser.2.yml --fail-fast --split
        env:
          GORELEASER_KEY: ${{ secrets.GORELEASER_KEY }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GGOOS: ${{ matrix.GOOS }}

  release:
    runs-on: ubuntu-latest
    needs: [meta, build]
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: actions/setup-go@v5
        with:
          go-version-file: "go.mod"
      # copy the caches from prepare
      - uses: actions/cache@v4
        with:
          path: dist/linux
          key: linux-${{ needs.meta.outputs.sha_short }}
      - uses: actions/cache@v4
        with:
          path: dist/darwin
          key: darwin-${{ needs.meta.outputs.sha_short }}
      - uses: actions/cache@v4
        with:
          path: dist/windows
          key: windows-${{ needs.meta.outputs.sha_short }}
      - name: Write release meta
        env:
          RELEASE_JSON: ${{ needs.meta.outputs.json }}
        run: echo "$RELEASE_JSON" > ./dist/release.json
      - uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: |
            dist/**/*.json
            dist/**/*.zip
            dist/**/*.tar.gz
          retention-days: 1
          overwrite: true
      - name: Github release
        if: steps.cache.outputs.cache-hit != 'true' && github.ref == 'refs/heads/master'
        uses: goreleaser/goreleaser-action@v5
        with:
          distribution: goreleaser-pro
          version: latest
          args: continue --merge
        env:
          GORELEASER_KEY: ${{ secrets.GORELEASER_KEY }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GGOOS: ${{ matrix.GOOS }}

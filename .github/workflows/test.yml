name: Test

on:
  workflow_call:

jobs:
  test:
    strategy:
      fail-fast: false
      matrix:
        os: [ ubuntu-latest, macos-latest, windows-latest ]
        test:
          - workdir: "."
            target: "test"
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: actions/setup-go@v5
        with:
          go-version-file: "${{ matrix.test.workdir }}/go.mod"
      - name: Run Tests (${{ matrix.test.target }})
        run: make ${{ matrix.test.target }}

version: 2
updates:
  - package-ecosystem: "gomod"
    directory: "/"
    labels:
      - "dependencies"
      - "go"
    schedule:
      interval: "daily"
    groups:
      tracing:
        patterns:
          - "go.opentelemetry.io/*"
      golangx:
        patterns:
          - "golang.org/x/*"
      aws-sdk:
        patterns:
          - "github.com/aws/aws-sdk-go-v2/*"

  - package-ecosystem: "github-actions"
    directory: "/"
    labels:
      - "dependencies"
      - "actions"
    schedule:
      interval: "daily"
    groups:
      artifacts:
        patterns:
          - "action/upload-artifact"
          - "action/download-artifact"
